As a Senior PostgreSQL Database Architect, conduct an in-depth analysis and optimization of the existing database architecture based on the provided documentation. The primary objective is to refactor the schema for enhanced performance, scalability, security, and data integrity, while precisely implementing the specified business logic for user and asset lifecycle management.

**Context & Provided Files:**
*   **Application Requirements:** `@/.kiro/specs/agriddustria-web-portal/requirements.md`
*   **Current Database Schema:** `@/supabase/migrations/20250930230000_dump_full_schema.sql`
*   **Authentication Guide:** `@/.database/auth_schema_guide.md`
*   **Feature Specifications:** `@/.kiro/specs/agriddustria-web-portal/tasks.md`

**Scope of Refactoring and Optimization:**

1.  **State-Driven Onboarding & Role Management:**
    *   Engineer a robust, state-driven workflow for expert onboarding. This must be enforced at the database level to ensure ACID compliance through the following states: `new_signup` -> `pending_approval` -> `active`/`rejected`.
    *   Modify the `expert_profiles` and related tables to comprehensively capture all required information for admin review, as detailed in `tasks.md` (lines 74-93). This includes normalized structures for repeatable data where appropriate (e.g., Education, Work Experience, Certifications) to support the "Basic Information Tab" requirements.
    *   Architect the database changes required for an admin-to-admin invitation system. Document the integration strategy for Supabase's `auth.admin.inviteUserByEmail()` function, including necessary table structures for tracking invitation status and required Row-Level Security (RLS) policies. Append the corresponding development tasks to `@/.kiro/specs/agriddustria-web-portal/tasks.md`.
    *   Implement PostgreSQL functions and triggers to automate and enforce state transitions, log all status changes in `expert_verification_history`, and prevent illicit state modifications.

2.  **Optimizing Inter-Role Relationships:**
    *   Analyze the relationships between `experts`, `farmers`, and `factory_workers`. Propose minimal, non-disruptive schema adjustments to efficiently model the many-to-many interactions required for consultations, chat history, and shared data access. The priority is optimizing query performance for these interactions through strategic indexing or lightweight join tables, while minimizing the refactoring burden on the application layer.

3.  **Asset Registration Workflow Automation:**
    *   Redesign the asset registration flow to ensure data consistency. The process of approving a request in `registration_requests` must trigger an atomic transaction that creates a corresponding entry in the `assets` table, preventing data orphans. Define and implement a clear status lifecycle (`ENUM`: `pending`, `approved`, `rejected`) managed by triggers or stored procedures.

4.  **Schema Health, Normalization, and Performance Tuning:**
    *   Perform a comprehensive schema audit to identify and eliminate redundant columns and denormalized data, particularly consolidating disparate status columns.
    *   Establish a global, unified status management system using PostgreSQL `ENUM` types for all stateful entities (expert verification, asset registration, etc.) to ensure application-wide consistency.
    *   Develop a forward-looking indexing strategy. Propose new indexes for all foreign keys, columns frequently used in `WHERE` clauses, and fields involved in text searches to optimize query performance.
    *   Recommend a baseline set of Row-Level Security (RLS) policies to enforce data access rules at the database level, ensuring users can only view or modify data pertinent to their role.

**Deliverables:**

Produce the following artifacts within the `@/.database/` directory:

1.  **`db_enhancement_report.md`:** A comprehensive architectural report detailing:
    *   The rationale behind all proposed changes.
    *   Detailed schematics of the new state-driven workflows.
    *   Definitions for the unified `ENUM` status system.
    *   The recommended indexing and RLS policy strategy.
    *   A list of all columns and tables marked for deprecation or removal.

2.  **`migration_script.sql`:** A single, idempotent, and transactionally safe SQL script containing all DDL (`ALTER`, `CREATE`) and DML statements, including functions and triggers, to apply the proposed enhancements.

3.  **`DB_core_onboarding.md`:** Authoritative developer documentation outlining the complete database logic for the core onboarding and management processes: expert verification, asset registration, and consultation data models. This guide must detail the tables, state transitions, automated triggers, and expected data flows.

4.  **Updates to `@/.kiro/specs/agriddustria-web-portal/tasks.md`:** Append new, clearly defined tasks for the development team to implement any of the edits on the backend/frontend/database sides, the tasks may be new main tasks or by modifying or adding new subtasks to existing main ones.