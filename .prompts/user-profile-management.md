You are a full-stack developer expert with deep knowledge of Supabase, PostgreSQL, and modern web application architecture using React/Next.js. Your task is to generate a comprehensive, step-by-step implementation plan for a user profile management system within my AgriConnect web application. The application connects farmers with agricultural experts and is managed by administrators. I am using Supabase for the backend.

Based on the provided database schema, existing UI, and feature requirements, generate a complete implementation guide. The guide must cover backend (Supabase RLS, PostgreSQL Functions) and frontend (React/Next.js with `supabase-js` v2).

**Core Feature Requirements:**

1.  **Expert Profile Management:** On a dedicated `/settings` page, a logged-in expert must be able to view and edit their own information stored in the `profiles` and `expert_profiles` tables. Their current `verification_status` must be displayed but should be read-only.
2.  **Admin Profile Management:** On the `/settings` page, a logged-in admin must be able to view and edit their information from the `profiles` table.
3.  **Admin-Managed Expert Verification:** On the `/admin/users` page, which already displays a list of experts:
    *   The "Actions" column in the table must contain "Approve" and "Reject" buttons for any expert whose `verification_status` is 'pending' or 'in_review'.
    *   Clicking an expert's row (anywhere except the action buttons) should navigate to a detailed, read-only view of that expert's full profile. The existing "View" button in the Actions column should be removed.
    *   Clicking "Approve" must securely update the expert's `verification_status` to 'approved'.
    *   Clicking "Reject" must securely update the `verification_status` to 'rejected'.
4.  **Expert Access Control & Verification Flow:** Implement a robust verification flow. An expert whose `verification_status` is not 'approved' must be prevented from accessing core application functionality. They should be redirected to a dedicated status page (e.g., "Your application is pending review") that informs them of their status.

**Implementation Plan Details:**

The implementation plan must provide clear answers and strategies for the following:
*   What is the recommended workflow for a new user to register as an "expert" and have their `expert_profiles` record automatically created with a 'pending' status?
*   When an admin rejects an expert, what is the workflow for the expert to edit and resubmit their profile for a new review? How is this state change managed in the database (e.g., changing from 'rejected' back to 'pending')?
*   How can a notification system (e.g., email via a Supabase Edge Function with a service like Resend) be integrated to inform an expert when their status changes?

Propose any necessary database edits to implement these requirements. Your plan should detail the following technical components:

**1. Database and Backend Implementation:**
*   **Database Edits & Helper Functions:** Propose any necessary database modifications. Create a security-definer function `is_admin(user_id uuid)` that returns a boolean by checking the `user_roles` and `roles` tables. This function will simplify RLS policies.
*   **Supabase Row Level Security (RLS) Policies:** Provide specific, secure RLS policies for `profiles`, `expert_profiles`, and `user_roles` to enforce the following rules:
    *   Users can view and update their own `profiles` record.
    *   Experts can view and update their own `expert_profiles` record, but cannot change the `verification_status`.
    *   Admins can view all `profiles` and `expert_profiles`.
    *   Admins can perform all actions on `user_roles`.
*   **PostgreSQL Functions (RPC):** Define the necessary RPC functions to handle security-critical actions. These must be `security definer` functions to operate with elevated privileges.
    *   `approve_expert(expert_user_id uuid)`: Callable only by admins. Sets the target expert's `verification_status` to 'approved'.
    *   `reject_expert(expert_user_id uuid)`: Callable only by admins. Sets the target expert's `verification_status` to 'rejected'.
    *   `resubmit_expert_profile(expert_user_id uuid)`: Callable only by the expert themselves. Changes status from 'rejected' back to 'pending', allowing them to re-enter the review queue after updating their details.

**2. Frontend Implementation (React/Next.js):**
*   **Component Structure:** Outline the component structure for the `/settings` page (with conditional rendering for admins vs. experts), the `/admin/users` page, and the expert detail view modal/page.
*   **Data Fetching & Manipulation:** Provide code examples using `supabase-js` v2 for:
    *   Fetching a list of all experts with their combined `profiles` and `expert_profiles` data for the admin table.
    *   Calling the `approve_expert` and `reject_expert` RPC functions from the admin UI and refreshing the data.
    *   Implementing the row-click navigation to a detailed expert profile view.
    *   Creating a client-side hook `useUserProfile()` that fetches the current user's profile, role, and verification status.
*   **Route Protection:** Show how to create a higher-order component or use middleware to protect routes. This logic should use the `useUserProfile()` hook to check if an expert user is 'approved' and redirect them to a "Pending Verification" page if they are not.

**Database Schema Context:**

profiles table (general user profile management):
create table public.profiles (
  id uuid not null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone null default now(),
  email character varying(255) not null,
  first_name text not null,
  last_name text not null,
  phone_number character varying(20) null,
  profile_picture_url character varying(255) null,
  email_verified boolean null default false,
  phone_verified boolean null default false,
  is_active boolean null default true,
  language_preference character varying(10) null default 'en'::character varying,
  expertise_area text null,
  qualifications text null,
  years_of_experience integer null,
  password_set boolean null default false,
  constraint profiles_pkey primary key (id),
  constraint profiles_email_key unique (email),
  constraint profiles_phone_number_key unique (phone_number),
  constraint profiles_id_fkey foreign KEY (id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;


farmer_profiles (for management of farmer related details)
create table public.farmer_profiles (
  id uuid not null,
  preferred_contact_method character varying(20) null default 'email'::character varying,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint farmer_profiles_pkey primary key (id),
  constraint farmer_profiles_id_fkey foreign KEY (id) references profiles (id) on delete CASCADE,
  constraint farmer_profiles_preferred_contact_method_check check (
    (
      (preferred_contact_method)::text = any (
        (
          array[
            'email'::character varying,
            'phone'::character varying,
            'sms'::character varying
          ]
        )::text[]
      )
    )
  )
) TABLESPACE pg_default;

expert_profiles (for management of expert related profiles)
create table public.expert_profiles (
  id uuid not null,
  bio text null,
  years_of_experience integer null,
  education text null,
  verification_status character varying(20) not null default 'pending'::character varying,
  is_available boolean null default true,
  average_rating numeric(3, 2) null,
  total_reviews integer null default 0,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint expert_profiles_pkey primary key (id),
  constraint expert_profiles_id_fkey foreign KEY (id) references profiles (id) on delete CASCADE,
  constraint expert_profiles_verification_status_check check (
    (
      (verification_status)::text = any (
        (
          array[
            'pending'::character varying,
            'in_review'::character varying,
            'approved'::character varying,
            'rejected'::character varying,
            'info_requested'::character varying
          ]
        )::text[]
      )
    )
  )
) TABLESPACE pg_default;

user_roles (a table for linking users with roles)
create table public.user_roles (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  role_id uuid not null,
  created_at timestamp with time zone null default now(),
  constraint user_roles_pkey primary key (id),
  constraint user_roles_user_id_role_id_key unique (user_id, role_id),
  constraint user_roles_role_id_fkey foreign KEY (role_id) references roles (id) on delete CASCADE,
  constraint user_roles_user_id_fkey foreign KEY (user_id) references profiles (id) on delete CASCADE
) TABLESPACE pg_default;

roles (for storing roles)
create table public.roles (
  id uuid not null default gen_random_uuid (),
  name character varying(50) not null,
  description text null,
  is_system boolean null default false,
  is_admin_role boolean null default false,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint roles_pkey primary key (id),
  constraint roles_name_key unique (name)
) TABLESPACE pg_default;


Triggers and Function:
| trigger_name               | trigger_event | schema_name | table_name | trigger_function                            | trigger_timing | trigger_level | trigger_condition |
| -------------------------- | ------------- | ----------- | ---------- | ------------------------------------------- | -------------- | ------------- | ----------------- |
| on_auth_user_created       | INSERT        | auth        | users      | EXECUTE FUNCTION handle_new_user()          | AFTER          | ROW           | null              |
| on_auth_user_email_updated | UPDATE        | auth        | users      | EXECUTE FUNCTION handle_user_email_update() | AFTER          | ROW           | null              |
| on_auth_user_updated       | UPDATE        | auth        | users      | EXECUTE FUNCTION update_email_verified()    | AFTER          | ROW           | null              |



functions:
[
  {
    "schema_name": "public",
    "function_name": "handle_new_user",
    "function_definition": "CREATE OR REPLACE FUNCTION public.handle_new_user()\n RETURNS trigger\n LANGUAGE plpgsql\n SECURITY DEFINER\nAS $function$\nDECLARE\n  user_role text;\n  role_id uuid;\n  profile_created boolean := false;\nBEGIN\n  -- Set default role to 'expert' for this flow\n  user_role := COALESCE(new.raw_user_meta_data->>'role', 'expert');\n  \n  BEGIN\n    -- Create profile with only collected fields including password_set\n    INSERT INTO public.profiles (\n      id, \n      email, \n      first_name, \n      last_name,\n      phone_number,\n      email_verified,\n      password_set,\n      is_active,\n      created_at,\n      updated_at\n    ) VALUES (\n      new.id, \n      new.email, \n      COALESCE(new.raw_user_meta_data->>'first_name', ''),\n      COALESCE(new.raw_user_meta_data->>'last_name', ''),\n      COALESCE(new.raw_user_meta_data->>'phone_number', NULL),\n      new.email_confirmed_at IS NOT NULL,\n      false,\n      true,\n      now(),\n      now()\n    );\n    profile_created := true;\n    \n    -- Get role ID from roles table\n    SELECT id INTO role_id FROM public.roles WHERE name = user_role;\n    \n    -- Assign role if found\n    IF role_id IS NOT NULL THEN\n      INSERT INTO public.user_roles (user_id, role_id, created_at)\n      VALUES (new.id, role_id, now());\n      \n      -- Update user metadata with assigned role\n      UPDATE auth.users\n      SET raw_user_meta_data = raw_user_meta_data || jsonb_build_object('role', user_role)\n      WHERE id = new.id;\n    ELSE\n      RAISE WARNING 'Role % not found in roles table', user_role;\n    END IF;\n    \n  EXCEPTION WHEN others THEN\n    -- Log error if profile creation fails\n    RAISE LOG 'Error creating profile for user %: %', new.id, SQLERRM;\n    \n    -- If profile was created but role assignment failed, delete profile\n    IF profile_created THEN\n      DELETE FROM public.profiles WHERE id = new.id;\n    END IF;\n    \n    RAISE;\n  END;\n\n  RETURN new;\nEND;\n$function$\n"
  },
  {
    "schema_name": "public",
    "function_name": "handle_user_email_update",
    "function_definition": "CREATE OR REPLACE FUNCTION public.handle_user_email_update()\n RETURNS trigger\n LANGUAGE plpgsql\n SECURITY DEFINER\nAS $function$\nBEGIN\n  -- Only update if email actually changed\n  IF new.email <> old.email THEN\n    UPDATE public.profiles\n    SET \n      email = new.email,\n      email_verified = new.email_confirmed_at IS NOT NULL,\n      updated_at = now()\n    WHERE id = new.id;\n    \n    -- Log email changes for security\n    INSERT INTO public.audit_logs (user_id, action, details)\n    VALUES (new.id, 'email_update', jsonb_build_object('old_email', old.email, 'new_email', new.email));\n  END IF;\n  \n  RETURN new;\nEND;\n$function$\n"
  },
  {
    "schema_name": "public",
    "function_name": "update_email_verified",
    "function_definition": "CREATE OR REPLACE FUNCTION public.update_email_verified()\n RETURNS trigger\n LANGUAGE plpgsql\n SECURITY DEFINER\nAS $function$\nBEGIN\n  UPDATE public.profiles\n  SET email_verified = (new.email_confirmed_at IS NOT NULL),\n      updated_at = now()\n  WHERE id = new.id;\n  RETURN new;\nEND;\n$function$\n"
  }
]