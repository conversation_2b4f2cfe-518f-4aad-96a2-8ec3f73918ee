Generate a comprehensive, actionable development plan to finalize the expert verification feature, incorporating both expert-facing UI/UX enhancements and critical admin-facing functionalities. This plan must address and resolve the existing backend console errors by leveraging the provided context files, i previously created another plan but it's not completed if you want to build on it '@/expert_verification_finalization_plan.md '.

**Project Context:**
-   **Existing Plan:** `@/.plans/expert_verification_plan.md`
-   **Database Schema:** `@/supabase/migrations/20251002_public_schema.sql`
-   **DB Enhancement Notes:** `@/.database/db_enhancement/README.md`

---

### **Phase 1: Expert-Facing Experience Overhaul**

**Task 1.1: Redesign Verification Prompt & Flow**
-   **Internationalization:** Add the Arabic translation for the 'Complete Your Expert Verification' message displayed on the expert's dashboard.
-   **UI Styling:**
    -   Modify the verification popup's background color to light yellow (`#FFFFE0`) with black text.
    -   Restyle the 'Start Verification' button to have a black background with white text.
    -   Remove the status tags (e.g., 'PENDING') and the exclamation mark icon next to the title.
-   **Workflow Logic:**
    -   Upon successful submission of the verification form, update the expert's status and change the dashboard message to 'Waiting for verification'.
    -   Once an admin approves the verification, the message component should no longer be rendered on the expert's dashboard.

**Task 1.2: Overhaul the Verification Form Page (`/verification-pending`)**
-   **Layout & Structure:**
    -   Eliminate the tabbed view. Consolidate all form fields into a single, scrollable page, using horizontal separators to distinguish between sections (e.g., "Basic Information," "Education," "Experience").
-   **Content and Wording:**
    -   Change the main title to 'Verify Your Account to Get Started'.
    -   Update the descriptive text to: 'Fill in the form below to be reviewed by our team and get started.'
    -   Add a prominent note encouraging the user to ensure all provided information is accurate and authentic to expedite the review process.
-   **Functionality:**
    -   Integrate a document upload component directly under each relevant section. Add instructional text specifying the required document (e.g., "Upload a clear image of your National ID" under Basic Information, "Upload relevant certifications" under Education).
    -   Add a final 'Submit for Review' button at the bottom of the page.
-   **Internationalization & Styling:**
    -   Ensure the entire `/verification-pending` page fully supports RTL (Right-to-Left) layout for Arabic.
    -   Translate all static text and form labels into Arabic.
    -   Remove the yellow status icon and the 'PENDING' status tag from this page's title area.

**Task 1.3: Implement Non-Invasive Feature Locking**
-   Instead of forcefully redirecting unverified experts, create a UI overlay component.
-   This overlay should conditionally render on pages with restricted features (e.g., consultations), displaying a message prompting verification and preventing interaction with the page content beneath it.
-   On the expert's home page, under the 'طلبات الاستشارة' list, display a clear message: "You will have access to consultations upon successful verification."

---

### **Phase 2: Admin Dashboard Functionality & Management**

**Task 2.1: Activate and Enhance Existing User Management (`/dashboard/users`)**
-   Verify that the data fetching mechanisms for both the "Experts" and "Clients" tabs on the `/dashboard/users` page are correctly implemented and populating the respective lists (Currently no users are showing).
-   Implement the routing for the 'View' button in the "Clients" tab to navigate to a new, dedicated client details page (e.g., `/dashboard/clients/[id]`).

**Task 2.2: Implement Full Functionality for Expert Detail Page (`/dashboard/users/[id]`)**
-   Activate the data fetching on this page to display all profile information for the selected expert, including their `verification_status` from the `expert_profiles` table.
-   Implement the backend logic for the 'Approve' and 'Reject' actions. Wire these functions to the corresponding buttons on the page to update the expert's `verification_status` in the database taking into consideration the latest Databaes enhancements we made @/.database/db_enhancement/README.md and the Verification functionalities created @/.database/migrations/expert_verification_functions_safe.sql .
-   Add a new component to this page that conditionally fetches and displays a list of all consultations associated with the expert, but only if their `verification_status` is 'approved' (or what is equivalent representing an approved expert).

**Task 2.3: Build and Implement Client Detail Page (`/dashboard/clients/[id]`)**
-   Create the new dynamic page and associated components to display the full details of a selected client.
-   Implement a dedicated section on this page to fetch and display all entities (assets) that are linked to the viewed client.

---

### **Phase 3: Critical Backend & Database Error Resolution**
**Task 3.1: Fix Console Error: `406 (Not Acceptable)`**
-   **Problem:** The query `GET /rest/v1/profiles?select=*&id=eq.{uuid}` in `layout.tsx` and `user-profile.tsx` is failing with the error `JSON object requested, multiple (or no) rows returned`. This occurs when the query expects a single object but finds zero or more than one row.
-   **Solution:**
    1.  Refactor the Supabase query in the data fetching logic (`fetchProfile`).
    2.  Change the method from `.single()` to `.maybeSingle()`. This will correctly handle cases where a profile might not exist for a user, returning `null` instead of throwing an error.
    3.  Enhance the frontend components to gracefully handle a `null` profile state, relying on the fallback user metadata where appropriate without causing a console error.

**Task 3.2: Fix Console Errors: `400 (Bad Request)` & `column profiles.verification_status does not exist`**
-   **Problem:** Queries in `page.tsx` (for the admin expert list) and `user-profile.tsx` are attempting to select `verification_status` directly from the `profiles` table, where it does not exist, causing a crash.
-   **Solution:**
    1.  Confirm the database schema in `@/supabase/migrations/20251002_public_schema.sql`. The `verification_status` column resides in the `expert_profiles` table, not `profiles`.
    2.  Rewrite the failing Supabase queries to correctly join `profiles` with `expert_profiles`.
    3.  The corrected query should look similar to: `supabase.from('profiles').select('id, first_name, ..., expert_profiles!inner(verification_status, bio, ...)')`. This will correctly fetch the status through the established relationship. Apply this fix to all queries attempting to access expert-specific details.