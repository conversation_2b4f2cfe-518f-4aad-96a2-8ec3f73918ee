Your primary objective is to implement critical authentication and expert verification workflows, aligning the application with the recent database enhancements. Your work must be guided by the provided implementation plans and the new database schema.

**Context & Provided Files:**
*   **Database Enhancement Summary:** `@/.database/db_enhancement/README.md`
*   **Latest Database Schema:** `@/supabase/migrations/20251002_public_schema.sql`
*   **Authentication & Onboarding Plan:** `@/.plans/authentication_and_onboarding_plan.md`
*   **Expert Verification Plan:** `@/.plans/expert_verification_plan.md`
*   **Legacy Task Context:** `@/.kiro/specs/agriddustria-web-portal/tasks.md` (lines 65-102)

**Your Implementation Tasks:**

1.  **Implement the Authentication and Onboarding Workflow:**
    *   Execute the plan detailed in `@/.plans/authentication_and_onboarding_plan.md`. This includes creating a role-differentiated login flow that grants admins immediate access while directing unverified experts into the mandatory verification process.

2.  **Implement the Expert Verification System:**
    *   Your primary source of truth for this task is the `@/.plans/expert_verification_plan.md`.
    *   Use the feature specifications in `@/.kiro/specs/agriddustria-web-portal/tasks.md` (lines 65-102) for additional context on UI elements and user-facing requirements, but defer to the implementation plan where there are discrepancies.
    *   Your implementation must cover both the **Admin View** (functionality for the "Approve" and "Reject" buttons on the `/dashboard/users/[id]` page) and the **Expert View** (the verification form, status display, and application locking for unverified users).