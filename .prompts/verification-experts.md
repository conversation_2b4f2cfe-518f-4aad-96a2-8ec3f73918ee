I am working on the task 3 enhance existing expert verification system on the @/.kiro/specs/agriddustria-web-portal/tasks.md i finished it but now i am working on other parts of it that aren't completed.
first let's finalize the building of the '/dashboard/users/'  I want to simplify the expert view page:

- For the 'Verification History' tab, display only the date of verification and the date of signing up to the platform if not available should display in offline text style not available (similarly all missing information).
- Translate the labels and content for the 'Verification History' tab and the 'Verification Documents' tab views into Arabic (or the target language for RTL support).
- Remove the request documents buttons on the view page the one next to the action buttons and the one on the 'Verification Documents''.
- Ensure that all tab views are RTL-supporting, including proper text alignment, layout mirroring, and font handling.

Second, update the @/.kiro/specs/agriddustria-web-portal/tasks.md task 3.4, and 3.5. This includes adding an application lockdown feature for experts who aren't verified, Creating an expert verification flow for the experts view to verifiy their accounts, and testing the new verification feature for both the admin and expert views. Key requirements:

- Show a message with a verify your account button redirecting to a verification page for unverified experts to add their verification details. The forms fields should match those displayed on the admin expert view page and include the ability to upload documents.
- At the top of the screen, display a prominent message such as "Verify your account to access the app features" with the verification button link. The expert should not have access to the main app features until verification is submitted or completed.
- The form fields to collect are: 
basic information (e.g., name, contact, profile picture), study information (e.g., education background, degrees), work experience (e.g., previous roles, companies, durations), and supporting documents (e.g., ID, certifications; with upload functionality) all on one page form style.
- The add verification details page should be a single-page view. 
- The lockdown should restrict navigation to core app functionality, allowing only access to this verification page and perhaps account settings related to logout or basic info, but no expert dashboards, client interactions, or other accessible areas blocked pages should show a message highlighting with verify your account button.
- seeding the Database with better data especially the tables that are related experts profile, verification status, and so on.

Given the context of the database schema as the following:
- 'profiles' table linked to 'auth.users' table for authenticated users to manage users core information for more information on the auth schema @/.database/auth_schema_guide.md (generate a better one)
- dedicated tables to manage specalized a

In the plan, also include a comprehensive testing plan for this feature. The testing plan should cover happy secenarios with basic scenarios for verified vs. unverified states, data validation, uploads, and lockdown enforcement.