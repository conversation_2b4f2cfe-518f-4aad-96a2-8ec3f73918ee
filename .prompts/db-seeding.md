Based on your knowledge of the database, your primary task is to architect and implement a series of critical schema enhancements and then produce a comprehensive, testing-friendly data seeding solution. This involves creating a detailed migration script, updating documentation to reflect the new architecture, and generating a complete SQL script to populate the database with realistic, scenario-based data.

**Context & Provided Files:**
*   **Pre-Authenticated Users:** `@/.database/db_enhancement/authenticated_users.sql` (This file contains `INSERT` statements for `auth.users` and must be used as the foundation for all user-related data).
*   **Preferred Seeding Methods:** `@/.database/guides/supabase_testing.md`.
*   **Existing Onboarding Guide:** `@/.database/db_enhancement/DB_core_onboarding.md`.

**Part 1: Core Schema Refactoring and Implementation**

Before seeding data, you must first design and script a fundamental schema enhancement to correctly differentiate between agricultural and industrial users and experts. Given that the `farmer_profiles` table is already in use by the mobile app, the solution must be efficient and minimize breaking changes.

*   **Refactor User Profiles:**
    1.  **Problem:** The existence of only a `farmer_profiles` table is incorrect, as it does not support `factory_worker` users.
    2.  **Solution:** Modify the single existing table to support both user types.
        *   Rename the `farmer_profiles` table to `client_profiles`.
        *   Add a new `client_type` ENUM column to this table with possible values: `FARMER`, `FACTORY_WORKER`. This column will serve as the differentiator.
*   **Refactor Expert Profiles:**
    1.  **Problem:** The `expert_profiles` table does not differentiate between agricultural and industrial experts.
    2.  **Solution:** Add a new `expert_type` ENUM column to the existing `expert_profiles` table with possible values: `AGRICULTURE`, `INDUSTRIAL`.
*   **Deliverable:** These schema changes must be the first steps in the `seed.sql` script you create in Part 3, using `ALTER TABLE` commands.

**Part 2: Correct and Update Core Documentation**

Update the `@/.database/db_enhancement/DB_core_onboarding.md` guide to reflect the complete and corrected database architecture, including your refactoring from Part 1.

*   **Update the Roles Section:** Modify the guide to accurately describe the five essential roles: `admin`, `agriculture_expert`, `industrial_expert`, `farmer`, and `factory_worker`. Explain their importance in separating the platform's core logic.
*   **Document Schema Changes:** Add detailed sections explaining the new structure of the `client_profiles` table (formerly `farmer_profiles`) and the modified `expert_profiles` table, including the purpose of the new `client_type` and `expert_type` columns.

**Part 3: Create a Comprehensive Migration and Seeding SQL Script**

Develop a single, idempotent SQL script named `@/.database/db_enhancement/seed.sql`. This script will first apply the schema modifications from Part 1 and then populate all necessary tables.

**Script Requirements:**

1.  **Migration First:** The script must begin with the `ALTER TABLE` commands to implement the refactoring described in Part 1.
2.  **Idempotency:** After the migration commands, the script must truncate all relevant `public` schema tables (`profiles`, `user_roles`, `expert_profiles`, etc.) using `TRUNCATE TABLE ... RESTART IDENTITY CASCADE;` to ensure it can be run multiple times.
3.  **Roles Initialization:** Populate the `public.roles` table with the five corrected roles.
4.  **User & Profile Seeding:**
    *   Using the UUIDs from `authenticated_users.sql`, create corresponding entries in `public.profiles`.
    *   Assign each profile to one of the five roles via `public.user_roles`.
5.  **Scenario-Based Data Simulation:**
    *   **Differentiated Expert Lifecycle:**
        *   Create `expert_profiles` for all experts, correctly setting the `expert_type` to `AGRICULTURE` or `INDUSTRIAL` based on their assigned role.
        *   Simulate **pending**, **approved**, and **rejected** experts, ensuring both types are represented.
    *   **Differentiated User Lifecycle:**
        *   Create `client_profiles` for all `farmer` and `factory_worker` users, correctly setting the `client_type` for each.
        *   Populate fields to simulate a completed account setup, tracked in `account_setup_progress`.
    *   **Asset Registration Lifecycle:**
        *   Create `registration_requests` from both farmers and factory workers.
        *   Include a **pending**, **rejected**, and **approved** request. For the approved request, also create the corresponding record in `public.assets`.
    *   **Consultation Lifecycle:**
        *   Create multiple `consultation_requests` that demonstrate the core logic:
            *   Link an asset owned by a `farmer` to an `agriculture_expert`.
            *   Link an asset owned by a `factory_worker` to an `industrial_expert`.
        *   For at least one of each consultation type, populate `public.messages` with a simulated conversation.
6.  **Data Integrity:** Do not seed unused tables like `consultation_packages`. Ensure all `INSERT` statements respect the database's relational integrity.