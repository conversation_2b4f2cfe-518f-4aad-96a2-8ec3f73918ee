import { useState, useEffect } from 'react';
import { supabase } from '@/supabase/client';
import { User } from '@supabase/supabase-js';
import { Profile, ExpertProfile } from '@/types/database';

interface UserProfileData {
  user: User | null;
  profile: Profile | null;
  expertProfile: ExpertProfile | null;
  role: string | null;
  loading: boolean;
}

export function useUserProfile(): UserProfileData {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [expertProfile, setExpertProfile] = useState<ExpertProfile | null>(null);
  const [role, setRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        setUser(user);
        
        // Use user_metadata for role, as it's more reliable during development
        const userRole = user.user_metadata?.role || 'expert';
        setRole(userRole);

        // Fetch profile data (handle RLS errors gracefully)
        try {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
          
          if (!profileError && profileData) {
            setProfile(profileData);
          } else {
            console.log("Could not fetch profile from database, using user metadata fallback");
            // Create fallback profile from user metadata
            const fallbackProfile = {
              id: user.id,
              email: user.email || '',
              first_name: user.user_metadata?.first_name || user.email?.split('@')[0] || 'User',
              last_name: user.user_metadata?.last_name || '',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
            setProfile(fallbackProfile);
          }
        } catch (error) {
          console.log("Error fetching profile, using user metadata fallback");
          // Create fallback profile from user metadata
          const fallbackProfile = {
            id: user.id,
            email: user.email || '',
            first_name: user.user_metadata?.first_name || user.email?.split('@')[0] || 'User',
            last_name: user.user_metadata?.last_name || '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          setProfile(fallbackProfile);
        }

        if (userRole === 'expert') {
          try {
            const { data: expertData, error: expertError } = await supabase
              .from('expert_profiles')
              .select('*')
              .eq('id', user.id)
              .single();
            
            if (!expertError && expertData) {
              setExpertProfile(expertData);
            } else {
              console.log("Could not fetch expert profile, using fallback");
              // Create a minimal expert profile if none exists
              const fallbackExpertProfile = {
                id: user.id,
                bio: null,
                years_of_experience: null,
                education: null,
                verification_status: 'pending',
                is_available: false,
                average_rating: null,
                total_reviews: null,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              };
              setExpertProfile(fallbackExpertProfile);
            }
          } catch (error) {
            console.log("Error fetching expert profile, using fallback");
            // Create a minimal expert profile if none exists
            const fallbackExpertProfile = {
              id: user.id,
              bio: null,
              years_of_experience: null,
              education: null,
              verification_status: 'pending',
              is_available: false,
              average_rating: null,
              total_reviews: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
            setExpertProfile(fallbackExpertProfile);
          }
        }
      }
      setLoading(false);
    };

    fetchData();
  }, []);

  return { user, profile, expertProfile, role, loading };
}