"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import en from '@/locales/en.json';
import ar from '@/locales/ar.json';

const translations = { en, ar };

type Locale = 'en' | 'ar';

interface I18nContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string) => string;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

export function I18nProvider({ children }: { children: ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>('ar');

  useEffect(() => {
    const storedLocale = localStorage.getItem('locale') as Locale | null;
    if (storedLocale && ['en', 'ar'].includes(storedLocale)) {
      setLocaleState(storedLocale);
    } else {
      // Set Arabic as default if no locale is stored
      localStorage.setItem('locale', 'ar');
    }
  }, []);

  useEffect(() => {
    document.documentElement.lang = locale;
    document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
  }, [locale]);

  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale);
    localStorage.setItem('locale', newLocale);
  };

  const t = (key: string): string => {
    const keys = key.split('.');
    let result: unknown = translations[locale];
    for (const k of keys) {
      result = (result as Record<string, unknown>)?.[k];
      if (result === undefined) {
        // Fallback to English if translation is missing
        let fallbackResult: unknown = translations['en'];
        for (const fk of keys) {
          fallbackResult = (fallbackResult as Record<string, unknown>)?.[fk];
        }
        return (fallbackResult as string) || key;
      }
    }
    return (result as string) || key;
  };

  return (
    <I18nContext.Provider value={{ locale, setLocale, t }}>
      {children}
    </I18nContext.Provider>
  );
}

export function useTranslation() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useTranslation must be used within an I18nProvider');
  }
  return context;
}