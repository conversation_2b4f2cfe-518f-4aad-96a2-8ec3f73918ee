"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import { Users, UserCircle } from "lucide-react";

interface RoleSelectorProps {
  selectedRole?: "admin" | "expert";
  defaultValue?: "admin" | "expert";
  onRoleSelect?: (role: string) => void;
}

export default function RoleSelector({
  selectedRole,
  defaultValue = "expert",
  onRoleSelect,
}: RoleSelectorProps = {}) {
  const [role, setRole] = useState<"admin" | "expert">(
    selectedRole || defaultValue,
  );

  // Update hidden input when role changes
  useEffect(() => {
    const hiddenInput = document.getElementById(
      "role-input",
    ) as HTMLInputElement;
    if (hiddenInput) {
      hiddenInput.value = role;
    }
    if (onRoleSelect) {
      onRoleSelect(role);
    }
  }, [role, onRoleSelect]);

  return (
    <div className="w-full">
      <h3 className="text-lg font-medium text-green-900 mb-4">I am a:</h3>
      <div className="grid grid-cols-2 gap-4">
        <Button
          type="button"
          variant={role === "admin" ? "default" : "outline"}
          className={`h-auto py-6 flex flex-col items-center ${role === "admin" ? "bg-green-700 hover:bg-green-800" : "border-green-200 hover:border-green-300 hover:bg-green-50"}`}
          onClick={() => setRole("admin")}
          data-role="admin"
        >
          <UserCircle
            className={`h-8 w-8 mb-2 ${role === "admin" ? "text-white" : "text-green-600"}`}
          />
          <span className={role === "admin" ? "text-white" : "text-green-700"}>
            Administrator
          </span>
        </Button>
        <Button
          type="button"
          variant={role === "expert" ? "default" : "outline"}
          className={`h-auto py-6 flex flex-col items-center ${role === "expert" ? "bg-green-700 hover:bg-green-800" : "border-green-200 hover:border-green-300 hover:bg-green-50"}`}
          onClick={() => setRole("expert")}
          data-role="expert"
        >
          <Users
            className={`h-8 w-8 mb-2 ${role === "expert" ? "text-white" : "text-green-600"}`}
          />
          <span className={role === "expert" ? "text-white" : "text-green-700"}>
            Agricultural Expert
          </span>
        </Button>
      </div>
    </div>
  );
}
