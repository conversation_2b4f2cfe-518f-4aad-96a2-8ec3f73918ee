"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

interface DashboardChartProps {
  userRole?: string;
}

export function DashboardChart({ userRole = "expert" }: DashboardChartProps) {
  // Only show chart for admin users
  if (userRole !== "admin") {
    return null;
  }

  return (
    <Card className="col-span-4">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="space-y-1">
          <CardTitle>Platform Analytics</CardTitle>
          <p className="text-sm text-muted-foreground">
            Platform performance overview
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="h-8">
            Last 3 months
          </Button>
          <Button variant="outline" size="sm" className="h-8">
            Last 30 days
          </Button>
          <Button variant="outline" size="sm" className="h-8">
            Last 7 days
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full rounded-md bg-gradient-to-b from-indigo-900 to-indigo-500 opacity-80">
          {/* This is a placeholder for the chart */}
          <div className="flex h-full items-center justify-center">
            <p className="text-white">Analytics visualization would appear here</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
