"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Activity, CreditCard, DollarSign, Users } from "lucide-react";

interface MetricCardProps {
  title: string;
  value: string;
  description: string;
  icon: React.ReactNode;
  trend?: {
    value: string;
    positive?: boolean;
    negative?: boolean;
  };
}

export function MetricCard({
  title,
  value,
  description,
  icon,
  trend,
}: MetricCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between space-y-0">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <div className="text-muted-foreground">{icon}</div>
        </div>
        <div className={`${trend ? 'flex items-center justify-between' : ''} pt-4`}>
          <div>
            <p className="text-2xl font-bold">{value}</p>
            <p className="text-xs text-muted-foreground">{description}</p>
          </div>
          {trend && (
            <div
              className={`flex items-center rounded-md px-2 py-1 text-xs font-medium ${trend.positive ? "bg-green-100 text-green-800" : trend.negative ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"}`}
            >
              {trend.value}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface DashboardMetricsProps {
  userRole?: string;
}

export function DashboardMetrics({ userRole = "expert" }: DashboardMetricsProps) {
  // Only show metrics for admin users
  if (userRole !== "admin") {
    return null;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <MetricCard
        title="Total Farmers"
        value="1,234"
        description="Registered farmers on platform"
        icon={<Users className="h-4 w-4" />}
      />
      <MetricCard
        title="Total Experts"
        value="89"
        description="Agricultural experts available"
        icon={<Activity className="h-4 w-4" />}
      />
      <MetricCard
        title="Active Consultations"
        value="156"
        description="Ongoing consultation sessions"
        icon={<CreditCard className="h-4 w-4" />}
      />
      <MetricCard
        title="Total Revenue"
        value="124,500 EGP"
        description="Platform revenue generated"
        icon={<DollarSign className="h-4 w-4" />}
      />
    </div>
  );
}
