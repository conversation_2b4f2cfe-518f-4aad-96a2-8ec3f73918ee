"use client";

import { useState, useEffect } from "react";
import { useUserProfile } from "@/hooks/use-user-profile";
import { supabase } from "@/supabase/client";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle, Clock, FileText, ArrowRight } from "lucide-react";
import Link from "next/link";
import { useTranslation } from "@/components/i18n-provider";

interface VerificationData {
  verification_status?: string;
  rejection_reasons?: string[];
  admin_notes?: string;
}

export function VerificationStatusBanner() {
  const { user, role, loading } = useUserProfile();
  const { t } = useTranslation();
  const [verificationData, setVerificationData] = useState<VerificationData>({});
  const [fetchingStatus, setFetchingStatus] = useState(false);

  // Only show for expert roles and when not admin
  const shouldShow = role && ['agriculture_expert', 'industrial_expert', 'expert'].includes(role);

  useEffect(() => {
    if (user?.id && shouldShow) {
      fetchVerificationStatus();
    }
  }, [user?.id, shouldShow]);

  const fetchVerificationStatus = async () => {
    if (!user?.id) return;

    setFetchingStatus(true);
    try {
      const { data, error } = await supabase.rpc('get_my_verification_status');
      
      if (error) {
        console.error('Error fetching verification status:', error);
        return;
      }
      
      if (data) {
        setVerificationData(data);
      }
    } catch (error) {
      console.error('Error fetching verification status:', error);
    } finally {
      setFetchingStatus(false);
    }
  };

  // Don't render if user is admin or not an expert
  if (loading || !shouldShow || role === 'admin') {
    return null;
  }

  const status = verificationData.verification_status || 'not_submitted';

  // Don't show banner if already approved
  if (status === 'approved') {
    return null;
  }

  const getStatusInfo = () => {
    switch (status) {
      case 'pending':
      case 'under_review':
        // For submitted and under review - show "processing" message with no button
        return {
          title: t("verification.verificationSubmittedTitle") || "Verification Submitted for Review",
          description: t("verification.verificationSubmittedDescription") || "Thank you. Our team is currently reviewing your verification request. You will be notified via email once the review is complete.",
          action: null, // No button for submitted state
          urgent: false
        };
      case 'rejected':
        return {
          title: t("verification.verificationRejected") || "Verification Application Rejected",
          description: t("verification.rejectionDescription") || "Your application was not approved. Please review the feedback and resubmit.",
          action: t("verification.reviewResubmit") || "Review & Resubmit",
          urgent: true
        };
      case 'resubmission_required':
        return {
          title: t("verification.additionalInfoRequired") || "Additional Information Required",
          description: t("verification.additionalInfoDescription") || "Please provide additional information or documents as requested.",
          action: t("verification.completeApplication") || "Complete Application",
          urgent: true
        };
      default:
        // For not submitted or initial state - show "become verified" message with button
        return {
          title: t("verification.becomeVerifiedExpert") || "Become a Verified Expert",
          description: t("verification.verificationInitialDescription") || "To access all features and build trust with clients, please complete your expert verification.",
          action: t("verification.startVerification") || "Start Verification",
          urgent: true
        };
    }
  };

  const { title, description, action, urgent } = getStatusInfo();

  return (
    <Card className="mb-6" style={{ backgroundColor: '#FFFFE0', border: '1px solid #E5E5E5' }}>
      <CardContent className="p-4">
        <div className={`flex items-start ${action ? 'justify-between' : 'justify-center'}`}>
          <div className="flex-1">
            <h3 className="font-semibold text-black mb-2">
              {title}
            </h3>
            <p className="text-sm text-black">
              {description}
            </p>

            {/* Show rejection reasons if available */}
            {status === 'rejected' && verificationData.rejection_reasons && (
              <div className="mt-3 p-3 bg-red-100 rounded-md">
                <p className="text-sm font-medium text-red-800 mb-1">
                  {t("verification.rejectionReasons") || "Rejection Reasons:"}
                </p>
                <ul className="text-sm text-red-700 list-disc list-inside">
                  {verificationData.rejection_reasons.map((reason, index) => (
                    <li key={index}>{reason}</li>
                  ))}
                </ul>
                {verificationData.admin_notes && (
                  <div className="mt-2">
                    <p className="text-sm font-medium text-red-800">
                      {t("verification.adminNotes") || "Admin Notes:"}
                    </p>
                    <p className="text-sm text-red-700">{verificationData.admin_notes}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {action && (
            <Link href="/dashboard/verification-pending">
              <Button
                size="sm"
                className="bg-black text-white hover:bg-gray-800"
              >
                {action}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          )}
        </div>
      </CardContent>
    </Card>
  );
}