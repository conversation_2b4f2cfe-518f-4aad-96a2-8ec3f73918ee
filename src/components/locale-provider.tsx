"use client";

import { useEffect } from "react";
import { useTranslation } from "@/components/i18n-provider";

// Export the useLocale hook that returns the same shape as useTranslation
export const useLocale = useTranslation;

export function LocaleProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { locale } = useTranslation();

  useEffect(() => {
    if (locale) {
      document.documentElement.lang = locale;
      document.documentElement.dir = locale === "ar" ? "rtl" : "ltr";
    }
  }, [locale]);

  return <>{children}</>;
}