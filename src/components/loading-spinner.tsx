import React from 'react';

interface PageLoadingSpinnerProps {
  text?: string;
}

export const PageLoadingSpinner: React.FC<PageLoadingSpinnerProps> = ({ text = 'Loading...' }) => {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-lime-500 border-t-transparent"></div>
        <p className="text-sm text-muted-foreground">{text}</p>
      </div>
    </div>
  );
};
