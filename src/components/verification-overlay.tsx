"use client";

import { useUserProfile } from "@/hooks/use-user-profile";
import { useTranslation } from "@/components/i18n-provider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shield, ArrowRight, Clock } from "lucide-react";
import Link from "next/link";

interface VerificationOverlayProps {
  children: React.ReactNode;
  requireVerification?: boolean;
  message?: string;
}

export function VerificationOverlay({ 
  children, 
  requireVerification = true,
  message 
}: VerificationOverlayProps) {
  const { user, role, expertProfile, loading } = useUserProfile();
  const { t, locale } = useTranslation();

  // Don't show overlay if loading or user is not an expert
  if (loading || role !== 'expert') {
    return <>{children}</>;
  }

  // Don't show overlay if verification is not required
  if (!requireVerification) {
    return <>{children}</>;
  }

  // Don't show overlay if expert is already approved
  if (expertProfile?.verification_status === 'approved') {
    return <>{children}</>;
  }

  // For pending/under_review status, show the pending message instead of overlay
  if (expertProfile?.verification_status === 'pending' || expertProfile?.verification_status === 'under_review') {
    return <PendingVerificationMessage />;
  }

  // Show overlay for unverified experts
  return (
    <div className="relative">
      {/* Blurred background content */}
      <div className="filter blur-sm pointer-events-none">
        {children}
      </div>
      
      {/* Overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="max-w-md mx-4" dir={locale === "ar" ? "rtl" : "ltr"}>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-fit">
              <Shield className="h-8 w-8 text-blue-600" />
            </div>
            <CardTitle className="text-xl">
              {t("verification.verificationRequired") || "Verification Required"}
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              {message || 
               t("verification.featureLockedMessage") || 
               "This feature is available only to verified experts. Please complete your verification to access this functionality."}
            </p>
            
            <div className="space-y-2">
              <Link href="/dashboard/verification-pending">
                <Button className="w-full bg-black text-white hover:bg-gray-800">
                  {t("verification.startVerification") || "Start Verification"}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              
              <Link href="/dashboard">
                <Button variant="outline" className="w-full">
                  {t("common.backToDashboard") || "Back to Dashboard"}
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Hook for checking verification status
export function useVerificationStatus() {
  const { role, expertProfile, loading } = useUserProfile();
  
  const isExpert = role === 'expert';
  const isVerified = expertProfile?.verification_status === 'approved';
  const needsVerification = isExpert && !isVerified && !loading;
  
  return {
    isExpert,
    isVerified,
    needsVerification,
    verificationStatus: expertProfile?.verification_status,
    loading
  };
}

// Component for showing verification message on expert dashboard
export function VerificationMessage() {
  const { t, locale } = useTranslation();
  const { needsVerification, verificationStatus } = useVerificationStatus();

  if (!needsVerification) {
    return null;
  }

  const getStatusMessage = () => {
    switch (verificationStatus) {
      case 'pending':
      case 'under_review':
        return t("verification.verificationSubmittedTitle") || "Verification Submitted for Review";
      case 'rejected':
      case 'resubmission_required':
        return t("verification.verificationActionRequired") || "Verification Action Required";
      default:
        return t("verification.becomeVerifiedExpert") || "Become a Verified Expert";
    }
  };

  return (
    <Card className="mb-6" style={{ backgroundColor: '#FFFFE0', border: '1px solid #E5E5E5' }}>
      <CardContent className="p-4" dir={locale === "ar" ? "rtl" : "ltr"}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-black mb-2">
              {getStatusMessage()}
            </h3>
            <p className="text-sm text-black">
              {verificationStatus === 'pending' || verificationStatus === 'under_review'
                ? (t("verification.verificationSubmittedDescription") || "Thank you. Our team is currently reviewing your verification request. You will be notified via email once the review is complete.")
                : (t("verification.verificationInitialDescription") || "To access all features and build trust with clients, please complete your expert verification.")
              }
            </p>
          </div>
          
          {(verificationStatus !== 'pending' && verificationStatus !== 'under_review') && (
            <Link href="/dashboard/verification-pending">
              <Button 
                size="sm"
                className="bg-black text-white hover:bg-gray-800"
              >
                {t("verification.startVerification") || "Start Verification"}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Component for showing pending verification message in protected features
export function PendingVerificationMessage({ featureName }: { featureName?: string }) {
  const { t, locale } = useTranslation();
  const { verificationStatus } = useVerificationStatus();

  // Only show for pending/under_review status
  if (verificationStatus !== 'pending' && verificationStatus !== 'under_review') {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-[400px] p-8">
      <Card className="max-w-md mx-4" style={{ backgroundColor: '#FFFFE0', border: '1px solid #E5E5E5' }}>
        <CardContent className="p-6 text-center" dir={locale === "ar" ? "rtl" : "ltr"}>
          <div className="mx-auto mb-4 p-3 bg-yellow-100 rounded-full w-fit">
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
          <h3 className="text-lg font-semibold text-black mb-3">
            {t("verification.verificationSubmittedTitle") || "Verification Submitted for Review"}
          </h3>
          <p className="text-sm text-black mb-4">
            {t("verification.verificationSubmittedDescription") || "Thank you. Our team is currently reviewing your verification request. You will be notified via email once the review is complete."}
          </p>
          {featureName && (
            <p className="text-xs text-gray-600">
              {t("verification.featureAvailableAfterVerification", { feature: featureName }) || `${featureName} will be available once your verification is approved.`}
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Component for consultation access message
export function ConsultationAccessMessage() {
  const { t, locale } = useTranslation();
  const { needsVerification } = useVerificationStatus();

  if (!needsVerification) {
    return null;
  }

  return (
    <div className="text-center py-8 px-4" dir={locale === "ar" ? "rtl" : "ltr"}>
      <div className="mx-auto mb-4 p-3 bg-gray-100 rounded-full w-fit">
        <Shield className="h-8 w-8 text-gray-500" />
      </div>
      <h3 className="text-lg font-semibold text-gray-700 mb-2">
        {t("verification.consultationAccessTitle") || "Consultation Access"}
      </h3>
      <p className="text-gray-600 max-w-md mx-auto">
        {t("verification.consultationAccessMessage") || "You will have access to consultations upon successful verification."}
      </p>
    </div>
  );
}
