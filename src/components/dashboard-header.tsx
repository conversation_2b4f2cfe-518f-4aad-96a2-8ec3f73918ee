"use client";

import { usePageTitle } from "@/components/page-title-provider";
import { useTranslation } from "@/components/i18n-provider";
import UserProfile from "@/components/user-profile";

export default function DashboardHeader() {
  const { title } = usePageTitle();
  const { t } = useTranslation();
  const pageTitle = title ? t(title) : "";

  return (
    <header className="sticky top-0 z-10 flex h-16 items-center justify-between gap-4 border-b bg-white px-6">
      <div className="flex items-center gap-2 text-left rtl:text-right">
        <h1 className="text-xl font-semibold">{pageTitle}</h1>
      </div>
      <div className="flex items-center gap-4 rtl:flex-row-reverse">
        <UserProfile />
      </div>
    </header>
  );
}
