"use client";

import Link from "next/link";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { useTranslation } from "@/components/i18n-provider";
import {
  LifeBuoy,
  Users,
  Settings,
  Leaf,
  Home,
  Calendar,
  MessageSquare,
  UserCircle,
  BarChart3,
} from "lucide-react";

interface SidebarNavProps extends React.HTMLAttributes<HTMLElement> {
  items: {
    href: string;
    title: string;
    icon: React.ReactNode;
  }[];
}

export function SidebarNav({ className, items, ...props }: SidebarNavProps) {
  const pathname = usePathname();
  const { locale } = useTranslation();

  return (
    <nav className={cn("flex flex-col space-y-1", className)} {...props}>
      {items.map((item) => {
        const isActive = pathname === item.href;
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium w-full transition-all hover:bg-[#e0f5b1] hover:text-[#121212]",
              "ltr:flex-row rtl:flex-row-reverse ltr:text-left rtl:text-right",
              isActive
                ? "bg-[#d2e5a4] text-[#121212]"
                : "text-muted-foreground",
            )}
          >
            {locale === "ar" ? (
              <>
                <span className="flex-1">{item.title}</span>
                {item.icon}
              </>
            ) : (
              <>
                {item.icon}
                <span className="flex-1">{item.title}</span>
              </>
            )}
          </Link>
        );
      })}
    </nav>
  );
}

interface DashboardSidebarProps {
  userRole?: string;
}

export default function DashboardSidebar({
  userRole = "expert",
}: DashboardSidebarProps) {
  const { t, locale } = useTranslation();

  // Common items for both roles
  const commonItems = [
    {
      title: t("sidebar.dashboard"),
      href: "/dashboard",
      icon: <Home className="h-5 w-5" />,
    },
  ];

  // Admin-specific items (only shown to admin users)
  const adminItems = [
    {
      title: t("sidebar.users"),
      href: "/dashboard/users",
      icon: <Users className="h-5 w-5" />,
    },
    {
      title: t("sidebar.management"),
      href: "/dashboard/management",
      icon: <UserCircle className="h-5 w-5" />,
    },
    {
      title: t("sidebar.analytics"),
      href: "/dashboard/analytics",
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      title: t("sidebar.settings"),
      href: "/dashboard/settings",
      icon: <Settings className="h-5 w-5" />,
    },
  ];

  // Expert-specific items (only shown to expert users)
  const expertItems = [
    {
      title: t("sidebar.requests"),
      href: "/dashboard/requests",
      icon: <MessageSquare className="h-5 w-5" />,
    },
    {
      title: t("sidebar.consultations"),
      href: "/dashboard/consultations",
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      title: t("sidebar.myProfile"),
      href: "/dashboard/my-profile",
      icon: <UserCircle className="h-5 w-5" />,
    },
  ];

  // Farmer-specific items (only shown to farmer users)
  const farmerItems = [
    {
      title: t("sidebar.myProfile"),
      href: "/dashboard/my-profile",
      icon: <UserCircle className="h-5 w-5" />,
    },
  ];

  // Common items at the bottom for both roles
  const bottomItems = [
    {
      title: t("sidebar.support"),
      href: "/dashboard/support",
      icon: <LifeBuoy className="h-5 w-5" />,
    },
  ];

  // Combine items based on role - strict role-based access control
  let roleSpecificItems: typeof adminItems = [];
  
  if (userRole === "admin") {
    roleSpecificItems = adminItems;
  } else if (userRole === "expert") {
    roleSpecificItems = expertItems;
  } else if (userRole === "farmer") {
    roleSpecificItems = farmerItems;
  }

  let items = [...commonItems];

  // Add role-specific items only for the user's role
  items = [...items, ...roleSpecificItems];

  // Add bottom items (support) for all roles, but settings only for admin
  const filteredBottomItems = bottomItems.filter(item => {
    // Only show settings to admin users
    if (item.href === "/dashboard/settings") {
      return userRole === "admin";
    }
    return true;
  });
  
  items = [...items, ...filteredBottomItems];

  return (
    <div
      className={cn(
        "h-screen bg-white py-8 px-4",
        "border-gray-200",
        locale === "ar" ? "border-l" : "border-r"
      )}
    >
      <div className={cn(
        "flex items-center gap-2 px-2 mb-8",
        "ltr:flex-row rtl:flex-row-reverse"
      )}>
        <Leaf className="h-6 w-6 text-[#d2e5a4]" />
        <span className="text-xl font-bold text-[#121212] ltr:text-left rtl:text-right">AgriConnect</span>
      </div>
      <SidebarNav items={items} />
    </div>
  );
}
