'use client';

import { useEffect } from 'react';
import { useLocale } from '@/components/locale-provider'; // Assuming this path is correct

export default function DynamicLocaleAttributes() {
  const { locale } = useLocale();

  useEffect(() => {
    if (locale) {
      document.documentElement.lang = locale;
      document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
    }
  }, [locale]);

  return null; // This component does not render anything visible
}
