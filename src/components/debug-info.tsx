import { useState, useEffect } from 'react';
import { Session, User } from '@supabase/supabase-js';

interface DebugInfoProps {
  isVisible: boolean;
  session: Session | null;
  user: User | null;
}

export const useDebugMode = () => {
  const [isDebugMode, setIsDebugMode] = useState(false);

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Toggle debug mode with Ctrl+Shift+D
      if (event.ctrlKey && event.shiftKey && event.key.toLowerCase() === 'd') {
        setIsDebugMode(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  return isDebugMode;
};

export const DebugInfo: React.FC<DebugInfoProps> = ({ isVisible, session, user }) => {
  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 max-w-md p-4 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg text-xs font-mono overflow-auto max-h-[80vh]">
      <h3 className="text-yellow-800 font-bold mb-2">🐛 Debug Information</h3>
      <div className="space-y-2">
        <div>
          <h4 className="text-yellow-700 font-semibold">Session:</h4>
          <pre className="text-yellow-600 whitespace-pre-wrap">
            {JSON.stringify(session, null, 2)}
          </pre>
        </div>
        <div>
          <h4 className="text-yellow-700 font-semibold">User:</h4>
          <pre className="text-yellow-600 whitespace-pre-wrap">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};
