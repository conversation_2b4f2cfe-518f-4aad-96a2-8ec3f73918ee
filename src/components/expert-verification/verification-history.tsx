"use client";

import { useEffect, useState } from "react";
import { supabase } from "@/supabase/client";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, User, MessageSquare, Alert<PERSON>riangle, UserPlus, FileText } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslation } from "@/components/i18n-provider";

interface VerificationHistoryEntry {
  id: string;
  previous_status: string;
  new_status: string;
  admin_notes?: string;
  rejection_reasons?: string[];
  created_at: string;
  changed_by?: string;
  type: 'signup' | 'form_submission' | 'status_change';
  title?: string;
  description?: string;
}

interface VerificationHistoryProps {
  expertId: string;
}

export function VerificationHistory({ expertId }: VerificationHistoryProps) {
  const [history, setHistory] = useState<VerificationHistoryEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const { t, locale } = useTranslation();

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        // Fetch verification history
        const { data: historyData, error: historyError } = await supabase
          .from("expert_verification_history")
          .select(`
            id,
            previous_status,
            new_status,
            admin_notes,
            rejection_reasons,
            created_at,
            changed_by
          `)
          .eq("expert_id", expertId)
          .order("created_at", { ascending: false });

        // Fetch user signup date and expert profile creation date
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select(`
            created_at,
            expert_profiles(created_at, updated_at)
          `)
          .eq("id", expertId)
          .single();

        if (historyError) {
          console.error("Error fetching verification history:", historyError);
        }

        if (profileError) {
          console.error("Error fetching profile data:", profileError);
        }

        // Combine all timeline events
        const allEvents: VerificationHistoryEntry[] = [];

        // Add signup event
        if (profileData?.created_at) {
          allEvents.push({
            id: `signup-${expertId}`,
            previous_status: '',
            new_status: 'registered',
            created_at: profileData.created_at,
            type: 'signup',
            title: t('historyTab.signupTitle'),
            description: t('historyTab.signupDescription')
          });
        }

        // Add verification form submission event
        if (profileData?.expert_profiles?.created_at) {
          allEvents.push({
            id: `form-${expertId}`,
            previous_status: 'registered',
            new_status: 'pending',
            created_at: profileData.expert_profiles.created_at,
            type: 'form_submission',
            title: t('historyTab.formSubmissionTitle'),
            description: t('historyTab.formSubmissionDescription')
          });
        }

        // Add verification history events
        if (historyData) {
          historyData.forEach(entry => {
            allEvents.push({
              ...entry,
              type: 'status_change'
            });
          });
        }

        // Sort all events by date (newest first)
        allEvents.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        setHistory(allEvents);
      } catch (error) {
        console.error("Error fetching verification history:", error);
      } finally {
        setLoading(false);
      }
    };

    if (expertId) {
      fetchHistory();
    }
  }, [expertId, t]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const localeCode = locale === 'ar' ? 'ar-SA' : 'en-US';

    return date.toLocaleDateString(localeCode, {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "approved":
        return "default";
      case "rejected":
        return "destructive";
      case "under_review":
        return "secondary";
      case "resubmission_required":
        return "outline";
      case "suspended":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      registered: t('historyTab.statusRegistered'),
      pending: t('usersPage.verificationStatus.pending'),
      under_review: t('usersPage.verificationStatus.under_review'),
      approved: t('usersPage.verificationStatus.approved'),
      rejected: t('usersPage.verificationStatus.rejected'),
      resubmission_required: t('usersPage.verificationStatus.resubmission_required'),
      suspended: t('usersPage.verificationStatus.suspended'),
    };
    return statusMap[status] || status;
  };

  if (loading) {
    return (
      <Card dir={locale === 'ar' ? 'rtl' : 'ltr'}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-left rtl:text-right">
            <Clock className="h-5 w-5" />
            {t('historyTab.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-3 w-3/4" />
              <Skeleton className="h-3 w-1/4" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (history.length === 0) {
    return (
      <Card dir={locale === 'ar' ? 'rtl' : 'ltr'}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-left rtl:text-right">
            <Clock className="h-5 w-5" />
            {t('historyTab.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground py-8">
            {t('historyTab.noHistory')}
          </p>
        </CardContent>
      </Card>
    );
  }

  const getEventIcon = (entry: VerificationHistoryEntry) => {
    switch (entry.type) {
      case 'signup':
        return <UserPlus className="h-4 w-4" />;
      case 'form_submission':
        return <FileText className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <Card dir={locale === 'ar' ? 'rtl' : 'ltr'}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-left rtl:text-right">
          <Clock className="h-5 w-5" />
          {t('historyTab.title')}
        </CardTitle>
        <p className="text-sm text-muted-foreground text-left rtl:text-right">
          {t('historyTab.description')}
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {history.map((entry, index) => (
            <div key={entry.id} className="relative">
              {/* Timeline connector line */}
              {index < history.length - 1 && (
                <div className="absolute left-4 rtl:right-4 top-8 w-0.5 h-full bg-border" />
              )}

              <div className="flex gap-4">
                {/* Timeline dot */}
                <div
                  className={`w-8 h-8 rounded-full border-2 bg-background flex items-center justify-center ${
                    entry.type === 'signup'
                      ? "border-blue-500"
                      : entry.type === 'form_submission'
                        ? "border-yellow-500"
                        : entry.new_status === "approved"
                          ? "border-green-500"
                          : entry.new_status === "rejected"
                            ? "border-red-500"
                            : entry.new_status === "under_review"
                              ? "border-blue-500"
                              : "border-gray-500"
                  }`}
                >
                  {entry.type === 'signup' || entry.type === 'form_submission' ? (
                    <div className={`${
                      entry.type === 'signup'
                        ? "text-blue-500"
                        : "text-yellow-600"
                    }`}>
                      {getEventIcon(entry)}
                    </div>
                  ) : (
                    <div
                      className={`w-3 h-3 rounded-full ${
                        entry.new_status === "approved"
                          ? "bg-green-500"
                          : entry.new_status === "rejected"
                            ? "bg-red-500"
                            : entry.new_status === "under_review"
                              ? "bg-blue-500"
                              : "bg-gray-500"
                      }`}
                    />
                  )}
                </div>

                {/* Entry content */}
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between text-left rtl:text-right">
                    <div className="flex items-center gap-2">
                      {entry.type === 'signup' || entry.type === 'form_submission' ? (
                        <div>
                          <p className="font-medium">{entry.title}</p>
                          <p className="text-sm text-muted-foreground">{entry.description}</p>
                        </div>
                      ) : (
                        <div>
                          {entry.new_status === 'pending' ? (
                            <p className="font-medium">
                              {t('historyTab.formSubmissionDescription')}
                            </p>
                          ) : (
                            <p className="font-medium">
                              {t('historyTab.statusChanged')}{" "}
                              <Badge
                                variant={getStatusBadgeVariant(entry.previous_status)}
                                className="mx-1"
                              >
                                {getStatusLabel(entry.previous_status)}
                              </Badge>{" "}
                              {t('historyTab.to')}{" "}
                              <Badge
                                variant={getStatusBadgeVariant(entry.new_status)}
                                className="mx-1"
                              >
                                {getStatusLabel(entry.new_status)}
                              </Badge>
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(entry.created_at)}
                    </p>
                  </div>

                  {entry.admin_notes && entry.new_status !== 'pending' && (
                    <div className="bg-muted/50 p-3 rounded-lg text-left rtl:text-right">
                      <div className="flex items-center gap-2 mb-1">
                        <MessageSquare className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm font-medium">Admin Notes</p>
                      </div>
                      <p className="text-sm">{entry.admin_notes}</p>
                    </div>
                  )}

                  {entry.rejection_reasons && entry.rejection_reasons.length > 0 && (
                    <div className="bg-red-50 border border-red-200 p-3 rounded-lg text-left rtl:text-right">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <p className="text-sm font-medium text-red-800">
                          Rejection Reasons
                        </p>
                      </div>
                      <ul className="list-disc list-inside space-y-1 rtl:list-inside">
                        {entry.rejection_reasons.map((reason, i) => (
                          <li key={i} className="text-sm text-red-700">
                            {reason}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}


                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}