"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Calendar,
  FileIcon,
  ImageIcon,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface DocumentViewerProps {
  document: {
    id: string;
    document_name: string;
    document_type: string;
    document_url: string;
    file_size?: number;
    file_type?: string;
    upload_date: string;
    verification_status: string;
    admin_notes?: string;
  };
  isOpen: boolean;
  onClose: () => void;
  onDownload: (url: string, name: string) => void;
  onApprove?: (documentId: string) => void;
  onReject?: (documentId: string, notes: string) => void;
}

export function DocumentViewer({
  document,
  isOpen,
  onClose,
  onDownload,
  onApprove,
  onReject,
}: DocumentViewerProps) {
  const [rejectNotes, setRejectNotes] = useState("");
  const [showRejectForm, setShowRejectForm] = useState(false);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "approved":
        return "default";
      case "rejected":
        return "destructive";
      case "under_review":
        return "secondary";
      default:
        return "outline";
    }
  };

  const getDocumentTypeIcon = (fileType?: string, documentType?: string) => {
    if (fileType?.startsWith("image/")) {
      return <ImageIcon className="h-5 w-5" />;
    } else if (fileType?.includes("pdf") || documentType?.includes("pdf")) {
      return <FileText className="h-5 w-5" />;
    } else {
      return <FileIcon className="h-5 w-5" />;
    }
  };

  const getDocumentTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      qualification: "Educational Qualification",
      certification: "Professional Certification",
      experience: "Work Experience",
      id_document: "Identity Document",
    };
    return typeLabels[type] || type;
  };

  const handleReject = () => {
    if (onReject && rejectNotes.trim()) {
      onReject(document.id, rejectNotes);
      setRejectNotes("");
      setShowRejectForm(false);
      onClose();
    }
  };

  const handleApprove = () => {
    if (onApprove) {
      onApprove(document.id);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getDocumentTypeIcon(document.file_type, document.document_type)}
            Document Review: {document.document_name}
          </DialogTitle>
          <DialogDescription>
            Review and verify expert documentation
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Document Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                Document Information
                <Badge variant={getStatusBadgeVariant(document.verification_status)}>
                  {document.verification_status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-medium text-sm text-muted-foreground">
                    Document Type
                  </p>
                  <p>{getDocumentTypeLabel(document.document_type)}</p>
                </div>
                <div>
                  <p className="font-medium text-sm text-muted-foreground">
                    File Size
                  </p>
                  <p>{document.file_size ? formatFileSize(document.file_size) : "Unknown"}</p>
                </div>
                <div>
                  <p className="font-medium text-sm text-muted-foreground">
                    File Type
                  </p>
                  <p>{document.file_type || "Unknown"}</p>
                </div>
                <div>
                  <p className="font-medium text-sm text-muted-foreground">
                    Upload Date
                  </p>
                  <p className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {formatDate(document.upload_date)}
                  </p>
                </div>
              </div>

              {document.admin_notes && (
                <div>
                  <p className="font-medium text-sm text-muted-foreground">
                    Admin Notes
                  </p>
                  <p className="text-sm bg-muted p-2 rounded">
                    {document.admin_notes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Document Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Document Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/50">
                  <div className="flex items-center gap-3">
                    {getDocumentTypeIcon(document.file_type, document.document_type)}
                    <div>
                      <p className="font-medium">{document.document_name}</p>
                      <p className="text-sm text-muted-foreground">
                        {getDocumentTypeLabel(document.document_type)}
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(document.document_url, "_blank")}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDownload(document.document_url, document.document_name)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>

                {/* Image preview for image files */}
                {document.file_type?.startsWith("image/") && (
                  <div className="border rounded-lg overflow-hidden">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={document.document_url}
                      alt={document.document_name}
                      className="w-full max-h-96 object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = "none";
                      }}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          {document.verification_status === "pending" && (onApprove || onReject) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Verification Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4">
                  {onApprove && (
                    <Button onClick={handleApprove} className="flex-1">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve Document
                    </Button>
                  )}
                  {onReject && (
                    <Button
                      variant="destructive"
                      onClick={() => setShowRejectForm(true)}
                      className="flex-1"
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject Document
                    </Button>
                  )}
                </div>

                {showRejectForm && (
                  <div className="mt-4 space-y-4 p-4 border rounded-lg bg-muted/50">
                    <div>
                      <label className="text-sm font-medium">
                        Rejection Reason (Required)
                      </label>
                      <textarea
                        value={rejectNotes}
                        onChange={(e) => setRejectNotes(e.target.value)}
                        className="w-full mt-1 p-2 border rounded-md min-h-[100px]"
                        placeholder="Please provide specific reasons for rejection..."
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="destructive"
                        onClick={handleReject}
                        disabled={!rejectNotes.trim()}
                      >
                        Confirm Rejection
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowRejectForm(false);
                          setRejectNotes("");
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}