"use client";

import { useState, useEffect } from "react";
import { supabase } from "@/supabase/client";
import { useTranslation } from "@/components/i18n-provider";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { MessageSquare, Calendar, User, MapPin } from "lucide-react";

interface ConsultationsListProps {
  expertId: string;
}

interface Consultation {
  id: string;
  problem_description: string;
  status: string;
  created_at: string;
  requester?: {
    first_name: string;
    last_name: string;
    email: string;
  };
  asset?: {
    name: string;
    asset_type: string;
    location_address?: string;
  };
}

export function ConsultationsList({ expertId }: ConsultationsListProps) {
  const { t, locale } = useTranslation();
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConsultations = async () => {
      try {
        setLoading(true);
        setError(null);

        // First, check if consultation_requests table exists
        const { data, error } = await supabase
          .from('consultation_requests')
          .select(`
            id,
            problem_description,
            status,
            created_at,
            requester:profiles!requester_id(first_name, last_name, email),
            asset:assets!asset_id(name, asset_type, location_address)
          `)
          .eq('expert_id', expertId)
          .order('created_at', { ascending: false });

        if (error) {
          // If table doesn't exist or query fails, show a message
          console.error('Error fetching consultations:', error);
          setError('Consultation data not available');
          setConsultations([]);
        } else {
          setConsultations(data || []);
        }
      } catch (error) {
        console.error('Error fetching consultations:', error);
        setError('Failed to load consultations');
        setConsultations([]);
      } finally {
        setLoading(false);
      }
    };

    if (expertId) {
      fetchConsultations();
    }
  }, [expertId]);

  const getStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'default';
      case 'in_progress':
      case 'active':
        return 'secondary';
      case 'pending':
        return 'outline';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t("consultations.dataNotAvailable") || "Consultation Data Not Available"}
          </h3>
          <p className="text-gray-600">
            {t("consultations.dataNotAvailableDescription") || "The consultation system is not yet fully implemented."}
          </p>
        </CardContent>
      </Card>
    );
  }

  if (consultations.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t("consultations.noConsultations") || "No Consultations"}
          </h3>
          <p className="text-gray-600">
            {t("consultations.noConsultationsDescription") || "This expert hasn't received any consultation requests yet."}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4" dir={locale === "ar" ? "rtl" : "ltr"}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">
          {t("consultations.title") || "Consultations"} ({consultations.length})
        </h3>
      </div>

      <div className="space-y-4">
        {consultations.map((consultation) => (
          <Card key={consultation.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-base font-medium line-clamp-2">
                    {consultation.problem_description}
                  </CardTitle>
                  <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(consultation.created_at)}</span>
                    </div>
                    {consultation.requester && (
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        <span>
                          {consultation.requester.first_name} {consultation.requester.last_name}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <Badge variant={getStatusBadgeVariant(consultation.status)}>
                  {t(`consultationStatus.${consultation.status}`) || consultation.status}
                </Badge>
              </div>
            </CardHeader>
            
            {consultation.asset && (
              <CardContent className="pt-0">
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{consultation.asset.name}</span>
                    <span className="text-gray-500">({consultation.asset.asset_type})</span>
                  </div>
                  {consultation.asset.location_address && (
                    <p className="text-sm text-gray-600 mt-1">
                      {consultation.asset.location_address}
                    </p>
                  )}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>
    </div>
  );
}
