"use client";

import React, { useEffect, useState } from "react";

interface UrlProviderProps {
  children: React.ReactNode;
}

export function UrlProvider({ children }: UrlProviderProps) {
  const [currentUrl, setCurrentUrl] = useState<string>("");

  useEffect(() => {
    // Get the full URL information safely
    try {
      const baseUrl = window.location.origin;
      setCurrentUrl(baseUrl);
    } catch (error) {
      console.error("Error getting URL:", error);
      // Fallback to empty string if there's an error
      setCurrentUrl("");
    }
  }, []);

  return (
    <>
      {children}
      <input type="hidden" name="site_url" value={currentUrl} />
    </>
  );
}