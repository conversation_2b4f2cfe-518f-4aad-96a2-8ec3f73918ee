import Link from "next/link";
import { ArrowUpRight, Check } from "lucide-react";

export default function Hero() {
  return (
    <div className="relative overflow-hidden bg-white">
      {/* Background image with semi-transparent overlay */}
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=1200&q=80')] bg-cover bg-center" />
      <div className="absolute inset-0 bg-gradient-to-br from-green-900/70 via-green-800/60 to-lime-700/50" />

      <div className="relative pt-24 pb-32 sm:pt-32 sm:pb-40">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-5xl sm:text-6xl font-bold text-white mb-8 tracking-tight">
              Connect{" "}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-lime-300 to-lime-500">
                with Experts
              </span>{" "}
              in Agriculture
            </h1>

            <p className="text-xl text-gray-100 mb-12 max-w-2xl mx-auto leading-relaxed">
              AgriConnect brings farmers and agricultural experts together on
              one platform. Get personalized advice, schedule consultations, and
              optimize your farm&apos;s productivity.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/sign-up"
                className="inline-flex items-center px-8 py-4 text-green-900 bg-lime-400 rounded-lg hover:bg-lime-500 transition-colors text-lg font-medium"
              >
                Sign Up
                <ArrowUpRight className="ml-2 w-5 h-5" />
              </Link>

              <Link
                href="/sign-in"
                className="inline-flex items-center px-8 py-4 text-white bg-green-800/70 rounded-lg hover:bg-green-800/90 transition-colors text-lg font-medium border border-white/20"
              >
                Log In
              </Link>
            </div>

            <div className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-8 text-sm text-gray-200">
              <div className="flex items-center gap-2">
                <Check className="w-5 h-5 text-lime-400" />
                <span>Verified agricultural experts</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-5 h-5 text-lime-400" />
                <span>Personalized consultations</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-5 h-5 text-lime-400" />
                <span>Data-driven insights</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
