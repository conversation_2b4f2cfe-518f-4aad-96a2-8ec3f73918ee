"use client";

import { createContext, useContext, useEffect, useState, useRef } from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "@/supabase/client";

type AuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const isMounted = useRef(true); // Fast Refresh protection

  const refreshSession = async () => {
    try {
      setIsLoading(true);
      // 1. Get session first
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;

      if (session) {
        // 2. Only get user if session exists
        const { data: { user: authUser }, error: userError } = await supabase.auth.getUser();
        if (userError) throw userError;

        setSession(session);
        setUser(authUser);
      } else {
        // No session, so no user
        setSession(null);
        setUser(null);
      }
    } catch (error) {
      console.error("Error refreshing session:", error);
      setSession(null);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // This ref check prevents the auth listener from running twice in development
    // due to React's Strict Mode.
    if (!isMounted.current) return;

    // Immediately set loading to true when starting.
    setIsLoading(true);

    // Get the initial session and user data.
    const getInitialSession = async () => {
      try {
        // 1. Get session first
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        if (sessionError) throw sessionError;

        if (session) {
          // 2. Only get user if session exists
          const { data: { user: authUser }, error: userError } = await supabase.auth.getUser();
          if (userError) throw userError;

          setSession(session);
          setUser(authUser);

          // If authenticated, ensure we're on dashboard
          if (window.location.pathname === '/sign-in') {
            window.location.href = '/dashboard';
          }
        } else {
          // No session, so no user
          setSession(null);
          setUser(null);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
        setSession(null);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    getInitialSession();

    // Set up the listener for authentication state changes.
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (_event, session) => {
      console.log(`Auth event: ${_event}`);
      
      try {
        if (_event === 'SIGNED_OUT') {
          setSession(null);
          setUser(null);
          // Don't redirect if we're on the set-password page
          if (!window.location.pathname.startsWith('/auth/set-password')) {
            window.location.href = '/sign-in';
          }
        } else if (_event === 'SIGNED_IN' || _event === 'TOKEN_REFRESHED') {
          if (session) {
            // Only get user data if we have a session
            const { data: { user: authUser }, error: userError } = await supabase.auth.getUser();
            if (userError) throw userError;

            setSession(session);
            setUser(authUser);

          // Ensure we're on dashboard unless we're resetting password
          const isPasswordReset = window.location.pathname.startsWith('/auth/set-password');
          if (window.location.pathname === '/sign-in' && !isPasswordReset) {
            window.location.href = '/dashboard';
          }
          } else {
            // No session in auth event
            setSession(null);
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Error handling auth state change:', error);
        setSession(null);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    });

    // Set the ref to false on cleanup to allow the effect to run again on remount.
    isMounted.current = false;

    // Unsubscribe from the listener when the component unmounts.
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Handle sign out
  const signOut = async () => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Clear state and redirect
      setSession(null);
      setUser(null);
      window.location.href = '/sign-in';
      console.log("Sign out successful");
    } catch (error) {
      console.error("Sign out failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    session,
    isLoading,
    signOut,
    refreshSession,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
