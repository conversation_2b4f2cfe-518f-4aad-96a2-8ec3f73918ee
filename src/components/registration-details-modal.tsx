"use client";

import React, { useState, useEffect } from "react";
import { supabase } from "@/supabase/client";
import { useTranslation } from "@/components/i18n-provider";
import {
  Dialog,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Cross2Icon } from "@radix-ui/react-icons";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  User, 
  MapPin, 
  Phone, 
  Calendar, 
  Building2, 
  Tractor,
  FileText,
  Image as ImageIcon,
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react";

interface RegistrationDetailsModalProps {
  requestId: string | null;
  isOpen: boolean;
  onClose: () => void;
  onApprove?: (requestId: string) => void;
  onReject?: (requestId: string) => void;
}

interface RegistrationDetails {
  request: {
    id: string;
    user_id: string;
    entity_type: 'FARM' | 'FACTORY';
    status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'RESUBMITTED';
    submitted_at: string;
    reviewed_at?: string;
    reviewed_by?: string;
    admin_notes?: string;
    rejection_reason?: string;
    created_at: string;
    updated_at: string;
  };
  user: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
    phone_number?: string;
    entity_type?: string;
    account_activated: boolean;
  };
  asset_data: {
    basic_info: any;
    location_info: any;
    contact_info: any;
    additional_info: any;
    images: any[];
    documents: any[];
    computed: {
      asset_name: string;
      asset_location: string;
      contact_person: string;
      contact_phone: string;
      total_images: number;
      total_documents: number;
    };
  };
  approval_history: any[];
}

// Custom RTL-aware DialogContent component
const RTLDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & { isRTL?: boolean }
>(({ className, children, isRTL = false, ...props }, ref) => (
  <DialogPrimitive.Portal>
    <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full",
        className,
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className={cn(
        "absolute top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",
        isRTL ? "left-4" : "right-4"
      )}>
        <Cross2Icon className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPrimitive.Portal>
));
RTLDialogContent.displayName = "RTLDialogContent";

export default function RegistrationDetailsModal({
  requestId,
  isOpen,
  onClose,
  onApprove,
  onReject
}: RegistrationDetailsModalProps) {
  const { t, locale } = useTranslation();
  const isRTL = locale === 'ar';
  const [details, setDetails] = useState<RegistrationDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen && requestId) {
      setDetails(null); // Reset details when opening
      fetchRegistrationDetails();
    }
  }, [isOpen, requestId]);

  const fetchRegistrationDetails = async () => {
    if (!requestId) return;
    
    setIsLoading(true);
    console.log("Fetching details for request ID:", requestId);
    
    try {
      // Fetch registration request details
      console.log("Fetching registration request for ID:", requestId);
      const { data: requestData, error: requestError } = await supabase
        .from('registration_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (requestError) {
        console.error("Error fetching registration request:", requestError);
        console.error("Request ID:", requestId);
        setDetails(null); // Ensure details is set to null on error
        return;
      }

      if (!requestData) {
        console.error("No registration request data found for ID:", requestId);
        setDetails(null);
        return;
      }

      console.log("Registration request data:", requestData);

      console.log("Registration request data:", requestData);

      // Fetch user details
      console.log("Fetching user data for user ID:", requestData.user_id);
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('id, email, first_name, last_name, phone_number, entity_type, account_activated, created_at, updated_at')
        .eq('id', requestData.user_id)
        .single();

      if (userError) {
        console.error("Error fetching user data:", userError);
        console.error("User ID:", requestData.user_id);
        setDetails(null);
        return;
      }

      if (!userData) {
        console.error("No user data found for user ID:", requestData.user_id);
        setDetails(null);
        return;
      }

      console.log("User data:", userData);

      console.log("User data:", userData);

      // Fetch approval history
      const { data: historyData, error: historyError } = await supabase
        .from('registration_approval_history')
        .select('*')
        .eq('request_id', requestId)
        .order('created_at', { ascending: false });

      if (historyError) {
        console.error("Error fetching approval history:", historyError);
      }

      // Process the registration data to extract asset information
      const registrationSteps = requestData.registration_data?.registration_steps || [];
      console.log("Registration steps:", registrationSteps);
      
      let basicInfo: any = {};
      let locationInfo: any = {};
      let contactInfo: any = {};
      let images: any[] = [];
      let documents: any[] = [];

      registrationSteps.forEach((step: any) => {
        switch (step.step_name) {
          case 'farm_basic_info':
          case 'factory_basic_info':
            basicInfo = step.step_data || {};
            break;
          case 'farm_location':
          case 'factory_location':
            locationInfo = step.step_data || {};
            break;
          case 'farm_contact':
          case 'factory_contact':
            contactInfo = step.step_data || {};
            break;
          case 'farm_images':
          case 'factory_images':
            images = step.step_data?.images || [];
            break;
          case 'farm_documents':
          case 'factory_documents':
            documents = step.step_data?.documents || [];
            break;
        }
      });

      // Compute derived fields
      const assetName = basicInfo?.name || basicInfo?.farm_name || basicInfo?.factory_name || 'N/A';
      const assetLocation = locationInfo?.location_address || locationInfo?.address || basicInfo?.location_address || 'N/A';
      const contactPerson = contactInfo?.contact_person || basicInfo?.owner_name || 'N/A';
      const contactPhone = contactInfo?.phone || contactInfo?.contact_phone || basicInfo?.phone || 'N/A';

      // Build the details object
      const detailsObject: RegistrationDetails = {
        request: {
          id: requestData.id,
          user_id: requestData.user_id,
          entity_type: requestData.entity_type,
          status: requestData.status,
          submitted_at: requestData.submitted_at,
          reviewed_at: requestData.reviewed_at,
          reviewed_by: requestData.reviewed_by,
          admin_notes: requestData.admin_notes,
          rejection_reason: requestData.rejection_reason,
          created_at: requestData.created_at,
          updated_at: requestData.updated_at,
        },
        user: {
          id: userData.id,
          email: userData.email,
          first_name: userData.first_name,
          last_name: userData.last_name,
          phone_number: userData.phone_number,
          entity_type: userData.entity_type,
          account_activated: userData.account_activated,
        },
        asset_data: {
          basic_info: basicInfo,
          location_info: locationInfo,
          contact_info: contactInfo,
          additional_info: {},
          images: images,
          documents: documents,
          computed: {
            asset_name: assetName,
            asset_location: assetLocation,
            contact_person: contactPerson,
            contact_phone: contactPhone,
            total_images: images.length,
            total_documents: documents.length,
          },
        },
        approval_history: historyData || [],
      };

      console.log("Final details object:", detailsObject);
      setDetails(detailsObject);
    } catch (error) {
      console.error("Error fetching registration details:", error);
      setDetails(null); // Ensure details is null on error
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const localeCode = locale === 'ar' ? 'ar-EG' : 'en-US';
    return new Date(dateString).toLocaleDateString(localeCode, {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getInitials = (firstName?: string, lastName?: string, email?: string) => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }
    if (firstName) {
      return firstName.charAt(0).toUpperCase();
    }
    if (email) {
      return email.charAt(0).toUpperCase();
    }
    return "U";
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'PENDING':
      case 'RESUBMITTED':
        return 'secondary';
      case 'APPROVED':
        return 'default';
      case 'REJECTED':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const renderBasicInfo = () => {
    if (!details?.asset_data.basic_info) {
      return (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
              {details?.request.entity_type === 'FARM' ? 
                <Tractor className="h-5 w-5" /> : 
                <Building2 className="h-5 w-5" />
              }
              {t("registrationDetailsModal.basicInfo.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.basicInfo.noInfoAvailable")}</p>
          </CardContent>
        </Card>
      );
    }

    const basicInfo = details.asset_data.basic_info;
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
            {details.request.entity_type === 'FARM' ? 
              <Tractor className="h-5 w-5" /> : 
              <Building2 className="h-5 w-5" />
            }
            {t("registrationDetailsModal.basicInfo.title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.basicInfo.name")}</label>
              <p className="text-sm text-left rtl:text-right">{basicInfo.name || basicInfo.farm_name || basicInfo.factory_name || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.basicInfo.ownerName")}</label>
              <p className="text-sm text-left rtl:text-right">{basicInfo.owner_name || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.basicInfo.size")}</label>
              <p className="text-sm text-left rtl:text-right">{basicInfo.size || basicInfo.area || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.basicInfo.description")}</label>
              <p className="text-sm break-words text-left rtl:text-right">{basicInfo.description || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
            </div>
            {/* Show any additional fields that might be in the basic info */}
            {Object.entries(basicInfo).map(([key, value]) => {
              if (!['name', 'farm_name', 'factory_name', 'owner_name', 'size', 'area', 'description'].includes(key) && value) {
                return (
                  <div key={key}>
                    <label className="text-sm font-medium text-muted-foreground capitalize text-left rtl:text-right">
                      {key === 'location_address' ? t("registrationDetailsModal.locationInfo.address") : key.replace(/_/g, ' ')}
                    </label>
                    <p className="text-sm break-words text-left rtl:text-right">{String(value)}</p>
                  </div>
                );
              }
              return null;
            })}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderLocationInfo = () => {
    if (!details?.asset_data.location_info) return null;

    const locationInfo = details.asset_data.location_info;
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
            <MapPin className="h-5 w-5" />
            {t("registrationDetailsModal.locationInfo.title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.locationInfo.address")}</label>
              <p className="text-sm text-left rtl:text-right">{locationInfo.location_address || locationInfo.address || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.locationInfo.city")}</label>
                <p className="text-sm text-left rtl:text-right">{locationInfo.city || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.locationInfo.governorate")}</label>
                <p className="text-sm text-left rtl:text-right">{locationInfo.governorate || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
              </div>
            </div>
            {locationInfo.coordinates && (
              <div>
                <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.locationInfo.coordinates")}</label>
                <p className="text-sm text-left rtl:text-right">{locationInfo.coordinates}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderContactInfo = () => {
    if (!details?.asset_data.contact_info) return null;

    const contactInfo = details.asset_data.contact_info;
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
            <Phone className="h-5 w-5" />
            {t("registrationDetailsModal.contactInfo.title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.contactInfo.contactPerson")}</label>
              <p className="text-sm text-left rtl:text-right">{contactInfo.contact_person || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.contactInfo.phone")}</label>
              <p className="text-sm text-left rtl:text-right">{contactInfo.phone || contactInfo.contact_phone || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.contactInfo.email")}</label>
              <p className="text-sm text-left rtl:text-right">{contactInfo.email || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.contactInfo.alternativePhone")}</label>
              <p className="text-sm text-left rtl:text-right">{contactInfo.alternative_phone || t("registrationDetailsModal.basicInfo.notAvailable")}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderImages = () => {
    if (!details?.asset_data.images || details.asset_data.images.length === 0) {
      return (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
              <ImageIcon className="h-5 w-5" />
              {t("registrationDetailsModal.images.title")} ({details?.asset_data.computed.total_images || 0})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.images.noImages")}</p>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
            <ImageIcon className="h-5 w-5" />
            {t("registrationDetailsModal.images.title")} ({details.asset_data.computed.total_images})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {details.asset_data.images.map((image: any, index: number) => (
              <div key={index} className="aspect-square bg-muted rounded-lg overflow-hidden">
                <img
                  src={image.url || image.path}
                  alt={image.name || `${t("registrationDetailsModal.images.imageAlt")} ${index + 1}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder-image.png';
                  }}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderApprovalHistory = () => {
    if (!details?.approval_history || details.approval_history.length === 0) {
      return (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
              <Clock className="h-5 w-5" />
              {t("registrationDetailsModal.approvalHistory.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground text-left rtl:text-right">{t("registrationDetailsModal.approvalHistory.noHistory")}</p>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
            <Clock className="h-5 w-5" />
            {t("registrationDetailsModal.approvalHistory.title")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {details.approval_history.map((history: any, index: number) => (
              <div key={index} className="flex items-start gap-3 p-3 border rounded-lg rtl:flex-row-reverse">
                <div className="flex-shrink-0">
                  {history.action === 'APPROVED' ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : history.action === 'REJECTED' ? (
                    <XCircle className="h-5 w-5 text-red-500" />
                  ) : (
                    <Clock className="h-5 w-5 text-blue-500" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
                    <Badge variant={history.action === 'APPROVED' ? 'default' : 'destructive'}>
                      {t(`registrationDetailsModal.actions.${history.action}`)}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(history.created_at)}
                    </span>
                  </div>
                  {history.notes && (
                    <p className="text-sm mt-1 text-left rtl:text-right">{history.notes}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <RTLDialogContent 
        className={`max-w-4xl max-h-[90vh] overflow-y-auto ${isRTL ? 'rtl' : 'ltr'}`} 
        dir={isRTL ? 'rtl' : 'ltr'}
        isRTL={isRTL}
      >
        <DialogHeader>
          <div className="flex items-center gap-2 text-left rtl:text-right">
            <FileText className="h-5 w-5" />
            <DialogTitle className="text-xl font-semibold">{t("registrationDetailsModal.title")}</DialogTitle>
          </div>
          <DialogDescription className="text-left rtl:text-right">
            {t("registrationDetailsModal.description")}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">{t("registrationDetailsModal.loadingText")}</p>
            </div>
          </div>
        ) : details ? (
          <div className="space-y-6">
            {/* Request Summary */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between text-left rtl:text-right">
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    <CardTitle>{t("registrationDetailsModal.requestSummary")}</CardTitle>
                  </div>
                  <Badge variant={getStatusBadgeVariant(details.request.status)}>
                    {t(`registrationDetailsModal.statuses.${details.request.status}`)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 rtl:grid-flow-col">
                  <div className="flex items-center gap-3 text-left rtl:text-right">
                    <Avatar>
                      <AvatarFallback>
                        {getInitials(
                          details.user.first_name,
                          details.user.last_name,
                          details.user.email
                        )}
                      </AvatarFallback>
                    </Avatar>
                    <div className="text-left rtl:text-right">
                      <p className="font-medium">
                        {details.user.first_name && details.user.last_name
                          ? `${details.user.first_name} ${details.user.last_name}`
                          : details.user.email}
                      </p>
                      <p className="text-sm text-muted-foreground">{details.user.email}</p>
                    </div>
                  </div>
                  <div className="space-y-2 text-left rtl:text-right">
                    <div className="flex items-center gap-2 text-sm text-left rtl:text-right">
                      <Calendar className="h-4 w-4" />
                      <span>{t("registrationDetailsModal.userInfo.submitted")} {formatDate(details.request.submitted_at)}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-left rtl:text-right">
                      {details.request.entity_type === 'FARM' ? 
                        <Tractor className="h-4 w-4" /> : 
                        <Building2 className="h-4 w-4" />
                      }
                      <span>{t("registrationDetailsModal.userInfo.type")} {t(`registrationDetailsModal.entityTypes.${details.request.entity_type}`)}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-left rtl:text-right">
                      <span className="font-medium">{t("registrationDetailsModal.userInfo.asset")} {details.asset_data.computed.asset_name}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-left rtl:text-right">
                      <span>{t("registrationDetailsModal.userInfo.location")} {details.asset_data.computed.asset_location}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tabs for different sections */}
            <Tabs defaultValue="basic" className="w-full" dir={isRTL ? 'rtl' : 'ltr'}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">{t("registrationDetailsModal.basicInfoTab")}</TabsTrigger>
                <TabsTrigger value="location">{t("registrationDetailsModal.locationTab")}</TabsTrigger>
                <TabsTrigger value="contact">{t("registrationDetailsModal.contactTab")}</TabsTrigger>
                <TabsTrigger value="media">{t("registrationDetailsModal.mediaTab")}</TabsTrigger>
              </TabsList>
              
              <TabsContent value="basic" className="space-y-4">
                {renderBasicInfo()}
              </TabsContent>
              
              <TabsContent value="location" className="space-y-4">
                {renderLocationInfo()}
              </TabsContent>
              
              <TabsContent value="contact" className="space-y-4">
                {renderContactInfo()}
              </TabsContent>
              
              <TabsContent value="media" className="space-y-4">
                {renderImages()}
              </TabsContent>
            </Tabs>

            {/* Approval History */}
            {renderApprovalHistory()}

            {/* Action Buttons */}
            {details.request.status === 'PENDING' && (
              <div className="flex gap-2 pt-4 border-t justify-end rtl:justify-start">
                <Button variant="outline" onClick={onClose}>
                  {t("registrationDetailsModal.closeButton")}
                </Button>
                <Button 
                  variant="destructive" 
                  onClick={() => onReject?.(details.request.id)}
                >
                  {t("registrationDetailsModal.rejectButton")}
                </Button>
                <Button 
                  variant="default" 
                  onClick={() => onApprove?.(details.request.id)}
                >
                  {t("registrationDetailsModal.approveButton")}
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-muted-foreground">
              <div className="text-4xl mb-2">⚠️</div>
              <div className="font-medium">{t("registrationDetailsModal.failedToLoad")}</div>
              <div className="text-sm mt-2">
                {t("registrationDetailsModal.requestId")} {requestId}
              </div>
              <div className="text-sm mt-1">
                {t("registrationDetailsModal.checkConsole")}
              </div>
              <div className="flex gap-2 justify-center mt-4 rtl:flex-row-reverse">
                <Button 
                  variant="outline" 
                  onClick={fetchRegistrationDetails}
                  disabled={isLoading}
                >
                  {isLoading ? t("registrationDetailsModal.loadingText") : t("registrationDetailsModal.retryButton")}
                </Button>
                <Button 
                  variant="secondary" 
                  onClick={onClose}
                >
                  {t("registrationDetailsModal.closeButton")}
                </Button>
              </div>
            </div>
          </div>
        )}
      </RTLDialogContent>
    </Dialog>
  );
}