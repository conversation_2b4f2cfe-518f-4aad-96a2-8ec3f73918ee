"use client";

import { useTranslation } from "@/components/i18n-provider";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Clock,
  CheckCircle,
  AlertCircle,
  Search
} from "lucide-react";

interface Ticket {
  id: string;
  subject: string;
  status: string;
  statusColor: string;
  priority: string;
  created: string;
}

interface AdminSupportViewProps {
  openTickets: number;
  resolvedToday: number;
  totalTickets: string;
  recentTickets: Ticket[];
}

export default function AdminSupportView({
  openTickets,
  resolvedToday,
  totalTickets,
  recentTickets,
}: AdminSupportViewProps) {
  const { t } = useTranslation();
  return (
    <>
      {/* Support Overview Cards */}
      <div className="grid gap-4 md:grid-cols-3 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2 rtl:flex-row-reverse">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <CardTitle className="text-sm font-medium">{t("supportPage.openTickets")}</CardTitle>
                <AlertCircle className="h-4 w-4 text-orange-500" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="text-start">
            <div className="text-2xl font-bold">{openTickets}</div>
            <p className="text-xs text-muted-foreground">
              3 {t("supportPage.highPriority")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2 rtl:flex-row-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <CardTitle className="text-sm font-medium">{t("supportPage.resolvedToday")}</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </div>
          </CardHeader>
          <CardContent className="text-start">
            <div className="text-2xl font-bold">{resolvedToday}</div>
            <p className="text-xs text-muted-foreground">
              {t("supportPage.avgResponse")} 2.3h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2 rtl:flex-row-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <CardTitle className="text-sm font-medium">{t("supportPage.totalTickets")}</CardTitle>
              <Clock className="h-4 w-4 text-blue-500" />
            </div>
          </CardHeader>
          <CardContent className="text-start">
            <div className="text-2xl font-bold">{totalTickets}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">-15%</span> {t("supportPage.fromLastWeek")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Tickets Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between text-left rtl:text-right mb-0">
            <div className="flex-1">
              <CardTitle className="text-2xl font-bold rtl:text-right ltr:text-left">
                {t("supportPage.tickets")}
              </CardTitle>
              <CardDescription className="rtl:text-right ltr:text-left">
                {t("supportPage.latestRequests")}
              </CardDescription>
            </div>
            <div className="relative shrink-0">
              <Search className="absolute left-2.5 rtl:left-auto rtl:right-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t("supportPage.searchPlaceholder")}
                className="pl-8 rtl:pr-8 w-48"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentTickets.map((ticket, index) => (
              <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 text-start">
                <div className="flex items-start justify-between mb-2 rtl:flex-row-reverse">
                  <div>
                    <p className="font-medium text-sm">{ticket.subject}</p>
                    <p className="text-xs text-muted-foreground">#{ticket.id}</p>
                  </div>
                  <Badge
                    variant="secondary"
                    className={`${ticket.statusColor} text-white text-xs`}
                  >
                    {t(`supportPage.${ticket.status.toLowerCase().replace(" ", "")}`)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-xs text-muted-foreground rtl:flex-row-reverse">
                  <span>{t("supportPage.priority")}: {t(`supportPage.${ticket.priority.toLowerCase()}`)}</span>
                  <span>{ticket.created}</span>
                </div>
              </div>
            ))}
          </div>
          <Button variant="outline" className="w-full mt-4">
            {t("supportPage.viewAllTickets")}
          </Button>
        </CardContent>
      </Card>
    </>
  );
}
