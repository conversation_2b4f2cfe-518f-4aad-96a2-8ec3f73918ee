"use client";

import { useTranslation } from "@/components/i18n-provider";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { LifeBuoy, MessageSquare, Phone, Mail, FileText } from "lucide-react";

export default function ExpertSupportView() {
  const { t } = useTranslation();
  return (
    <Card>
      <CardHeader className="text-start">
        <CardTitle className="flex items-center gap-2 rtl:flex-row-reverse">
          <LifeBuoy className="h-5 w-5 text-blue-600" />
          {t("supportPage.contactSupport")}
        </CardTitle>
        <CardDescription>
          {t("supportPage.submitRequestDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 text-start">
        <div className="grid gap-4">
          <div>
            <label className="text-sm font-medium">{t("supportPage.subject")}</label>
            <Input placeholder={t("supportPage.subjectPlaceholder")} />
          </div>
          <div>
            <label className="text-sm font-medium">{t("supportPage.category")}</label>
            <select className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm">
              <option>{t("supportPage.technicalIssue")}</option>
              <option>{t("supportPage.accountProblem")}</option>
              <option>{t("supportPage.featureRequest")}</option>
              <option>{t("supportPage.billingQuestion")}</option>
              <option>{t("supportPage.other")}</option>
            </select>
          </div>
          <div>
            <label className="text-sm font-medium">{t("supportPage.priority")}</label>
            <select className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm">
              <option value="low">{t("supportPage.low")}</option>
              <option value="medium">{t("supportPage.medium")}</option>
              <option value="high">{t("supportPage.high")}</option>
              <option value="critical">{t("supportPage.critical")}</option>
            </select>
          </div>
          <div>
            <label className="text-sm font-medium">{t("supportPage.descriptionLabel")}</label>
            <Textarea
              placeholder={t("supportPage.descriptionPlaceholder")}
              rows={4}
            />
          </div>
          <Button className="w-full">
            <MessageSquare className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
            {t("supportPage.submitTicket")}
          </Button>
        </div>

        <div className="border-t pt-4">
          <CardTitle className="font-medium mb-3">{t("supportPage.otherWaysToHelp")}</CardTitle>
          <div className="space-y-2">
            <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 rtl:flex-row-reverse">
              <Phone className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">{t("supportPage.phoneSupport")}</p>
                <p className="text-xs text-muted-foreground">+****************</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 rtl:flex-row-reverse">
              <Mail className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">{t("supportPage.emailSupport")}</p>
                <p className="text-xs text-muted-foreground"><EMAIL></p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 rtl:flex-row-reverse">
              <FileText className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium">{t("supportPage.documentation")}</p>
                <p className="text-xs text-muted-foreground">{t("supportPage.browseHelpCenter")}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}