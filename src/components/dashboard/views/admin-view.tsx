"use client";

import { useTranslation } from "@/components/i18n-provider";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, UserCheck, DollarSign, Activity } from "lucide-react";

interface AdminViewProps {
  profile?: {
    id?: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    // Add more fields as needed
  } | null;
}

export default function AdminView({ profile }: AdminViewProps) {
  const { t } = useTranslation();
  // In a real app, this data would come from your API
  const metrics = [
    {
      title: t("analyticsPage.totalExperts"),
      value: "247",
      icon: <UserCheck className="h-4 w-4 text-muted-foreground" />,
      change: `+12% ${t("analyticsPage.fromLastMonth")}`,
      changeType: 'positive' as const,
    },
    {
      title: t("analyticsPage.totalFarmers"),
      value: "1,234",
      icon: <Users className="h-4 w-4 text-muted-foreground" />,
      change: `+23% ${t("analyticsPage.fromLastMonth")}`,
      changeType: 'positive' as const,
    },
    {
      title: t("analyticsPage.totalRevenue"),
      value: "$12,345",
      icon: <DollarSign className="h-4 w-4 text-muted-foreground" />,
      change: `+5% ${t("analyticsPage.fromLastMonth")}`,
      changeType: 'positive' as const,
    },
    {
      title: t("analyticsPage.consultations"),
      value: "89",
      icon: <Activity className="h-4 w-4 text-muted-foreground" />,
      change: `-2% ${t("analyticsPage.fromYesterday")}`,
      changeType: 'negative' as const,
    },
  ];

  return (
    <div className="space-y-6 text-left rtl:text-right">
      <h1 className="text-2xl font-bold text-left rtl:text-right">
        {t("dashboard.welcome")} {profile?.first_name || t("dashboard.admin")}!
      </h1>
      
      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 rtl:flex-row-reverse">
              <CardTitle className="text-sm font-medium">
                {metric.title}
              </CardTitle>
              <div className="h-4 w-4 text-muted-foreground">
                {metric.icon}
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <p className={`text-xs text-muted-foreground ${
                metric.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.change}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Additional admin widgets can be added here */}
    </div>
  );
}
