"use client";

import { useTranslation } from "@/components/i18n-provider";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Clock,
  MessageSquare,
  CheckCircle2,
  XCircle,
  User,
} from "lucide-react";
import { VerificationStatusBanner } from "@/components/verification-status-banner";
import { ConsultationAccessMessage, useVerificationStatus } from "@/components/verification-overlay";

interface ExpertViewProps {
  profile?: {
    id?: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    // Add more fields as needed
  } | null;
}

export default function ExpertView({ profile }: ExpertViewProps) {
  const { t } = useTranslation();
  const { needsVerification } = useVerificationStatus();
  const consultationRequests = [
    {
      id: 1,
      farmerName: "<PERSON>",
      request: t("dashboard.soilAnalysis"),
      date: "2023-06-15",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=farmer1",
      cropType: t("dashboard.wheat"),
      farmSize: `5 ${t("dashboard.acres")}`,
    },
    {
      id: 2,
      farmerName: "Sarah Grower",
      request: t("dashboard.pestControl"),
      date: "2023-06-16",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=farmer2",
      cropType: t("dashboard.tomatoes"),
      farmSize: `2.5 ${t("dashboard.acres")}`,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="flex w-full items-center justify-between text-left rtl:text-right">
        {/* Welcome text */}
        <h1 className="text-2xl font-bold">
          {t("dashboard.welcome")}{" "}
          {profile?.first_name || t("dashboard.expert")}!
        </h1>
        {/* Date section */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4 ltr:mr-2 rtl:ml-2" />
          <span>
            {new Date().toLocaleDateString(t("dashboard.locale"), {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </span>
        </div>
      </div>

      {/* Verification Status Banner */}
      <VerificationStatusBanner />

      <div className="space-y-6">
        <Card className="border-primary/20">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between text-left rtl:text-right">
              <div className="flex-1">
                <CardTitle className="text-lg">
                  {t("dashboard.consultationRequests")}
                </CardTitle>
                <CardDescription>
                  {t("dashboard.requestsDescription")}
                </CardDescription>
              </div>
              <div className="shrink-0">
                <span className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary">
                  {consultationRequests.length} {t("dashboard.new")}
                </span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {needsVerification ? (
              <ConsultationAccessMessage />
            ) : (
              <div className="space-y-4">
                {consultationRequests.map((request) => (
                <div
                  key={request.id}
                  className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                >
                  {/* Main card container */}
                  <div className="flex items-center justify-between gap-4 rtl:flex-row-reverse">
                    {/* Profile Block - Right in RTL */}
                    <div className="flex items-center gap-3 flex-1 text-left rtl:text-right">
                      <Avatar className="h-10 w-10 shrink-0">
                        <AvatarImage src={request.avatar} />
                        <AvatarFallback>
                          {request.farmerName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium truncate">
                          {request.farmerName}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {request.request}
                        </p>
                        <div className="flex items-center mt-1 text-xs text-muted-foreground">
                          <span className="inline-flex items-center">
                            <User className="h-3 w-3 ltr:mr-1 rtl:ml-1" />
                            {request.cropType}
                          </span>
                          <span className="mx-2">•</span>
                          <span>{request.farmSize}</span>
                        </div>
                      </div>
                    </div>

                    {/* Actions Section - Left in RTL */}
                    <div className="flex items-center gap-2 shrink-0 rtl:order-first">
                     <Button size="sm" className="h-8 whitespace-nowrap">
                       <CheckCircle2 className="h-4 w-4 ltr:mr-1.5 rtl:ml-1.5" />
                       {t("requestsPage.acceptAction")}
                     </Button>
                     <Button
                       variant="outline"
                       size="sm"
                       className="h-8 w-8 p-0"
                     >
                       <XCircle className="h-4 w-4 text-destructive" />
                       <span className="sr-only">
                         {t("requestsPage.declineAction")}
                       </span>
                     </Button>
                   </div>
                  </div>
                </div>
              ))}
                {consultationRequests.length === 0 && (
                  <div className="text-center py-8">
                    <MessageSquare className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground text-center">
                      {t("dashboard.noPendingRequests")}
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
