"use client";
import { Settings } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { supabase } from "@/supabase/client";
import { useRouter } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { useEffect, useState } from "react";

// Define type for profile
interface UserProfile {
  id: string;
  first_name?: string;
  last_name?: string;
  profile_picture_url?: string;
  email?: string;
}

export default function UserProfile() {
  const router = useRouter();
  const [user, setUser] = useState<{ id: string; email?: string } | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        // Get authenticated user
        const { data, error } = await supabase.auth.getUser();
        
        if (error || !data.user) {
          console.error("Auth error in UserProfile:", error);
          return;
        }
        
        // Set the user state
        setUser(data.user);
        
        try {
          // Get user profile data
          const { data: profileData, error: profileError } = await supabase
            .from("profiles")
            .select("*")
            .eq("id", data.user.id)
            .maybeSingle();
          
          if (profileData) {
            setProfile(profileData as UserProfile);
          } else if (profileError) {
            console.warn("Error fetching user profile:", profileError);
            // Create a minimal profile from auth data if needed
            setProfile({
              id: data.user.id,
              email: data.user.email,
              first_name: data.user.user_metadata?.first_name || undefined,
              last_name: data.user.user_metadata?.last_name || undefined
            });
          }
          
          // Get role from user metadata instead of querying user_roles
          // This avoids potential permission issues with the user_roles table
          
        } catch (profileError) {
          console.error("Profile fetch error:", profileError);
        }
      } catch (error) {
        console.error("Error in UserProfile fetchUserData:", error);
      }
    };
    
    fetchUserData();
  }, []);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative h-8 w-8 rounded-full"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage src={profile?.profile_picture_url || undefined} alt="Profile" />
            <AvatarFallback>
              {profile?.first_name?.charAt(0) || user?.email?.charAt(0) || "U"}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <div className="flex items-center justify-start gap-2 p-2">
          <div className="flex flex-col space-y-1 leading-none">
            <p className="font-medium">{(profile?.first_name || profile?.last_name) ? `${profile?.first_name || ''} ${profile?.last_name || ''}`.trim() : user?.email}</p>
            <p className="w-[200px] truncate text-sm text-muted-foreground">
              {user?.email}
            </p>
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => router.push("/dashboard/settings")}>
          <Settings className="mr-2 h-4 w-4" />
          Settings
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={async () => {
            await supabase.auth.signOut();
            router.refresh();
          }}
        >
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
