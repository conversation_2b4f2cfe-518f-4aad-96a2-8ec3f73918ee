import { User } from "@supabase/supabase-js";

export async function getUserRole(user: User): Promise<string> {
  // Since this is a client-side utility, we'll rely on the user metadata
  // which should be populated by the server-side logic upon login.
  const metadataRole = user.user_metadata?.role;
  if (metadataRole) {
    return metadataRole.toLowerCase();
  }

  // Fallback if metadata is not available
  return "expert";
}