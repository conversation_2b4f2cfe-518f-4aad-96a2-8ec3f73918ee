import { createClient } from "@/supabase/server";
import { User } from "@supabase/supabase-js";

export async function getUserRole(user: User): Promise<string> {
  const supabase = createClient();
  
  try {
    // First priority: Check user metadata (most reliable during development)
    const metadataRole = user.user_metadata?.role;
    if (metadataRole) {
      console.log("Role found in user metadata:", metadataRole);
      return metadataRole.toLowerCase();
    }

    // Second priority: Try to check user_roles table (handle RLS errors gracefully)
    try {
      const { data: userRoleData, error: userRoleError } = await supabase
        .from("user_roles")
        .select(`
          roles (
            name,
            is_admin_role
          )
        `)
        .eq("user_id", user.id)
        .maybeSingle();

      if (!userRoleError && userRoleData?.roles) {
        const roles = userRoleData.roles as unknown as { name: string; is_admin_role: boolean };
        const roleName = roles.name?.toLowerCase();
        console.log("Role found in user_roles table:", roleName);
        return roleName || "expert";
      }
    } catch (roleError) {
      console.log("Could not access user_roles table, continuing with fallbacks...");
    }

    // Third priority: Check expert_profiles table (handle RLS errors gracefully)
    try {
      const { data: expertData, error: expertError } = await supabase
        .from("expert_profiles")
        .select("id")
        .eq("id", user.id)
        .maybeSingle();

      if (!expertError && expertData) {
        console.log("User found in expert_profiles table");
        return "expert";
      }
    } catch (expertError) {
      console.log("Could not access expert_profiles table, continuing with fallbacks...");
    }

    // Fourth priority: Check farmer_profiles table (handle RLS errors gracefully)
    try {
      const { data: farmerData, error: farmerError } = await supabase
        .from("farmer_profiles")
        .select("id")
        .eq("id", user.id)
        .maybeSingle();

      if (!farmerError && farmerData) {
        console.log("User found in farmer_profiles table");
        return "farmer";
      }
    } catch (farmerError) {
      console.log("Could not access farmer_profiles table, continuing with fallbacks...");
    }

    // Final fallback: Default to expert
    console.log("No specific role found, defaulting to 'expert'");
    return "expert";

  } catch (error) {
    console.error("Error determining user role:", error);
    // Ultimate fallback: check user metadata again
    const metadataRole = user.user_metadata?.role;
    if (metadataRole) {
      return metadataRole.toLowerCase();
    }
    return "expert"; // Default to expert if there's an error
  }
}