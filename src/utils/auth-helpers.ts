import { toast } from "@/components/ui/use-toast";

export interface AuthError {
  code?: string;
  message: string;
  field?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

/**
 * Validates email format
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates password strength
 */
export const validatePassword = (password: string): { isValid: boolean; message?: string } => {
  if (!password) {
    return { isValid: false, message: "Password is required" };
  }
  
  if (password.length < 6) {
    return { isValid: false, message: "Password must be at least 6 characters long" };
  }
  
  if (password.length > 128) {
    return { isValid: false, message: "Password must be less than 128 characters" };
  }
  
  return { isValid: true };
};

/**
 * Validates sign-in form data
 */
export const validateSignInForm = (email: string, password: string): ValidationResult => {
  const errors: Record<string, string> = {};
  
  // Email validation
  if (!email) {
    errors.email = "Email is required";
  } else if (!validateEmail(email)) {
    errors.email = "Please enter a valid email address";
  }
  
  // Password validation
  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.message!;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Maps Supabase auth errors to user-friendly messages
 */
export const mapAuthError = (error: { message?: string; error_description?: string }) : string => {
  if (!error) return "An unexpected error occurred";
  
  const errorMessage = error.message || error.error_description || "";
  
  // Common Supabase auth error mappings
  const errorMappings: Record<string, string> = {
    "Invalid login credentials": "Invalid email or password. Please check your credentials and try again.",
    "Email not confirmed": "Please check your email and click the confirmation link before signing in.",
    "Too many requests": "Too many sign-in attempts. Please wait a few minutes before trying again.",
    "User not found": "No account found with this email address. Please check your email or sign up for a new account.",
    "Invalid email": "Please enter a valid email address.",
    "Password should be at least 6 characters": "Password must be at least 6 characters long.",
    "Signup requires a valid password": "Please enter a valid password.",
    "Unable to validate email address: invalid format": "Please enter a valid email address.",
    "Password is too weak": "Please choose a stronger password.",
    "Email address is invalid": "Please enter a valid email address.",
  };
  
  // Check for exact matches first
  if (errorMappings[errorMessage]) {
    return errorMappings[errorMessage];
  }
  
  // Check for partial matches
  for (const [key, value] of Object.entries(errorMappings)) {
    if (errorMessage.toLowerCase().includes(key.toLowerCase())) {
      return value;
    }
  }
  
  // Handle rate limiting
  if (errorMessage.includes("rate limit") || errorMessage.includes("too many")) {
    return "Too many attempts. Please wait before trying again.";
  }
  
  // Handle network errors
  if (errorMessage.includes("network") || errorMessage.includes("fetch")) {
    return "Network error. Please check your connection and try again.";
  }
  
  // Handle timeout errors
  if (errorMessage.includes("timeout")) {
    return "Request timed out. Please try again.";
  }
  
  // Return the original message if no mapping found, but make it more user-friendly
  return errorMessage || "An unexpected error occurred. Please try again.";
};

/**
 * Shows a toast notification for authentication errors
 */
export const showAuthError = (error: { message?: string; error_description?: string }, title: string = "Authentication Error") => {
  const message = mapAuthError(error);
  
  toast({
    variant: "destructive",
    title,
    description: message,
  });
};

/**
 * Shows a toast notification for authentication success
 */
export const showAuthSuccess = (message: string, title: string = "Success") => {
  toast({
    title,
    description: message,
  });
};

/**
 * Validates role access
 * More robust implementation to handle role verification for authentication
 */
export const validateRoleAccess = (userRole: string, requiredRole: string, isAdminRole: boolean = false): boolean => {
  // Handle empty values to prevent unexpected behavior
  if (!userRole || !requiredRole) {
    console.warn("validateRoleAccess called with empty role values", { userRole, requiredRole });
    return false;
  }

  // Normalize roles to lowercase to avoid case-sensitivity issues
  const normalizedUserRole = userRole.toLowerCase().trim();
  const normalizedRequiredRole = requiredRole.toLowerCase().trim();
  
  // Admins can access everything
  if (isAdminRole === true) {
    console.log("Admin access granted");
    return true;
  }
  
  // Exact role match
  if (normalizedUserRole === normalizedRequiredRole) {
    return true;
  }
  
  // Special case: admin role can access expert pages
  if (normalizedRequiredRole === "expert" && normalizedUserRole === "admin") {
    return true;
  }
  
  console.log(`Role access denied: User role '${userRole}' trying to access '${requiredRole}' page`);
  return false;
};

/**
 * Gets user-friendly role name
 */
export const getRoleName = (role: string): string => {
  const roleNames: Record<string, string> = {
    admin: "Administrator",
    expert: "Agricultural Expert",
    farmer: "Farmer",
  };
  
  return roleNames[role] || role;
};

/**
 * Sanitizes user input to prevent XSS
 */
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, "");
};