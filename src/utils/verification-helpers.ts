import { supabase } from '@/supabase/client';
import { User, Session } from '@supabase/supabase-js';

export interface VerificationStatus {
  emailVerified: boolean;
  passwordSet: boolean;
  isFullyVerified: boolean;
  nextStep?: 'email_verification' | 'password_setup' | 'complete';
}

export interface UserProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  email_verified: boolean;
  password_set: boolean;
  phone_number?: string;
}

/**
 * Get the verification status for a user
 */
export async function getUserVerificationStatus(userId: string): Promise<VerificationStatus | null> {
  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('email_verified, password_set')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user verification status:', error);
      return null;
    }

    const emailVerified = profile.email_verified || false;
    const passwordSet = profile.password_set || false;
    const isFullyVerified = emailVerified && passwordSet;

    let nextStep: VerificationStatus['nextStep'] = 'complete';
    if (!emailVerified) {
      nextStep = 'email_verification';
    } else if (!passwordSet) {
      nextStep = 'password_setup';
    }

    return {
      emailVerified,
      passwordSet,
      isFullyVerified,
      nextStep: isFullyVerified ? 'complete' : nextStep,
    };
  } catch (error) {
    console.error('Unexpected error checking verification status:', error);
    return null;
  }
}

/**
 * Check if a user is fully verified (both email and password)
 */
export async function isUserFullyVerified(userId: string): Promise<boolean> {
  const status = await getUserVerificationStatus(userId);
  return status?.isFullyVerified || false;
}

/**
 * Get user profile with verification details
 */
export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('id, email, first_name, last_name, email_verified, password_set, phone_number')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }

    return profile;
  } catch (error) {
    console.error('Unexpected error fetching user profile:', error);
    return null;
  }
}

/**
 * Get the appropriate redirect path based on user verification status
 */
export function getRedirectPath(verificationStatus: VerificationStatus, userRole?: string): string {
  if (!verificationStatus.isFullyVerified) {
    switch (verificationStatus.nextStep) {
      case 'email_verification':
        return '/sign-in?error=email_not_verified';
      case 'password_setup':
        return '/auth/set-password';
      default:
        return '/sign-in';
    }
  }

  // User is fully verified, redirect based on role
  if (userRole === 'farmer') {
    return '/download-app';
  }
  
  return '/dashboard';
}

/**
 * Validate if current user can access protected routes
 */
export async function canAccessProtectedRoute(user: User | null): Promise<{
  canAccess: boolean;
  redirectPath?: string;
  reason?: string;
}> {
  if (!user) {
    return {
      canAccess: false,
      redirectPath: '/sign-in',
      reason: 'No authenticated user',
    };
  }

  const verificationStatus = await getUserVerificationStatus(user.id);
  
  if (!verificationStatus) {
    return {
      canAccess: false,
      redirectPath: '/sign-in?error=verification_check_failed',
      reason: 'Unable to check verification status',
    };
  }

  if (!verificationStatus.isFullyVerified) {
    return {
      canAccess: false,
      redirectPath: getRedirectPath(verificationStatus, user.user_metadata?.role),
      reason: `Verification incomplete: ${verificationStatus.nextStep}`,
    };
  }

  return {
    canAccess: true,
    reason: 'User fully verified',
  };
}

/**
 * Check if user is accessing from email confirmation
 */
export function isEmailConfirmationContext(searchParams: URLSearchParams): boolean {
  const code = searchParams.get('code');
  const type = searchParams.get('type');
  
  // Check if there's a confirmation code in the URL
  return Boolean(code) || type === 'signup';
}

/**
 * Format user's full name from metadata
 */
export function formatUserName(user: User | null): string {
  if (!user?.user_metadata) return 'User';
  
  const firstName = user.user_metadata.first_name || '';
  const lastName = user.user_metadata.last_name || '';
  const fullName = `${firstName} ${lastName}`.trim();
  
  return fullName || 'User';
}

/**
 * Check if user needs to complete verification steps
 */
export function getVerificationMessage(verificationStatus: VerificationStatus): string {
  if (verificationStatus.isFullyVerified) {
    return 'Account fully verified';
  }

  switch (verificationStatus.nextStep) {
    case 'email_verification':
      return 'Please verify your email address to continue';
    case 'password_setup':
      return 'Please set up your password to complete registration';
    default:
      return 'Please complete account verification';
  }
}

/**
 * Constants for verification requirements
 */
export const VERIFICATION_REQUIREMENTS = {
  EMAIL_VERIFIED: 'email_verified',
  PASSWORD_SET: 'password_set',
  FULLY_VERIFIED: 'fully_verified',
} as const;

export const VERIFICATION_ROUTES = {
  SIGN_IN: '/sign-in',
  PASSWORD_SETUP: '/auth/set-password',
  DASHBOARD: '/dashboard',
  DOWNLOAD_APP: '/download-app',
} as const;

/**
 * Manually update password_set status for a user
 */
export async function updatePasswordSetStatus(userId: string, passwordSet: boolean = true): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('profiles')
      .update({ 
        password_set: passwordSet,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating password_set status:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Unexpected error updating password_set status:', error);
    return false;
  }
}

/**
 * Verify and sync password setup status
 */
export async function verifyPasswordSetup(userId: string): Promise<{
  hasPassword: boolean;
  dbStatus: boolean;
  synced: boolean;
}> {
  try {
    // Check if user has encrypted password in auth.users
    const { data: authUser } = await supabase.auth.getUser();
    const hasPassword = Boolean(authUser?.user?.id === userId);

    // Check password_set status in profiles
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('password_set')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching password status:', error);
      return { hasPassword: false, dbStatus: false, synced: false };
    }

    const dbStatus = profile?.password_set || false;
    
    // If user has password but db says false, sync it
    if (hasPassword && !dbStatus) {
      const updated = await updatePasswordSetStatus(userId, true);
      return { hasPassword, dbStatus: true, synced: updated };
    }

    return { hasPassword, dbStatus, synced: hasPassword === dbStatus };
  } catch (error) {
    console.error('Error verifying password setup:', error);
    return { hasPassword: false, dbStatus: false, synced: false };
  }
}

/**
 * Complete password setup process with optimized performance and timeout handling
 */
export async function completePasswordSetup(userId: string, password: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    console.log('Starting password setup for user:', userId);

    // Update user password with timeout
    console.log('Updating user password...');
    const { error: passwordError } = await Promise.race([
      supabase.auth.updateUser({ password: password }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Password update timeout')), 10000)
      )
    ]) as { error?: { message?: string } };

    if (passwordError) {
      console.error('Password update failed:', passwordError);
      return { success: false, error: passwordError.message };
    }
    console.log('Password updated successfully');

    // Force refresh the session to ensure we have the latest state
    console.log('Refreshing session...');
    try {
      await Promise.race([
        supabase.auth.refreshSession(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Session refresh timeout')), 5000)
        )
      ]);
      console.log('Session refreshed successfully');
    } catch (refreshError) {
      console.warn('Session refresh failed, continuing anyway:', refreshError);
      // Don't fail the entire process if session refresh fails
    }

    // Ensure password_set is updated in database
    console.log('Updating database status...');
    const updated = await updatePasswordSetStatus(userId, true);

    if (!updated) {
      console.warn('Password updated but failed to update database status');
    } else {
      console.log('Database status updated successfully');
    }

    console.log('Password setup completed successfully');
    return { success: true };
  } catch (error) {
    console.error('Password setup failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Enhanced session refresh with error handling
 */
export async function refreshAuthSession(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const { error } = await supabase.auth.refreshSession();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to refresh session'
    };
  }
}

/**
 * Direct authentication check that bypasses auth provider state
 */
export async function getDirectAuthState(): Promise<{
  user: User | null;
  session: Session | null;
  isAuthenticated: boolean;
  error?: string;
}> {
  try {
    console.log('[DirectAuth] Performing direct authentication check...');
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('[DirectAuth] Session error:', sessionError);
      return {
        user: null,
        session: null,
        isAuthenticated: false,
        error: sessionError.message
      };
    }
    
    if (!session) {
      console.log('[DirectAuth] No session found');
      return {
        user: null,
        session: null,
        isAuthenticated: false
      };
    }
    
    // Verify user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('[DirectAuth] User verification error:', userError);
      return {
        user: null,
        session: null,
        isAuthenticated: false,
        error: userError.message
      };
    }
    
    console.log('[DirectAuth] Authentication successful:', user?.id);
    return {
      user,
      session,
      isAuthenticated: !!user,
    };
  } catch (error) {
    console.error('[DirectAuth] Unexpected error:', error);
    return {
      user: null,
      session: null,
      isAuthenticated: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Force auth state synchronization with timeout
 */
export async function forceAuthSync(timeoutMs: number = 5000): Promise<{
  success: boolean;
  user: User | null;
  error?: string;
}> {
  try {
    console.log('[ForceSync] Starting forced auth synchronization...');
    
    const syncPromise = async () => {
      // Refresh session first
      await supabase.auth.refreshSession();
      
      // Get fresh auth state
      const { user } = await getDirectAuthState();
      return user;
    };
    
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Auth sync timeout')), timeoutMs)
    );
    
    const user = await Promise.race([syncPromise(), timeoutPromise]);
    
    console.log('[ForceSync] Sync successful:', user?.id);
    return {
      success: true,
      user
    };
  } catch (error) {
    console.error('[ForceSync] Sync failed:', error);
    return {
      success: false,
      user: null,
      error: error instanceof Error ? error.message : 'Sync failed'
    };
  }
}

/**
 * Debug auth state and log comprehensive information
 */
export async function debugAuthState(context: string = 'unknown'): Promise<void> {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [DebugAuth] Context: ${context}`);
  
  try {
    // Check session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log(`[${timestamp}] [DebugAuth] Session:`, {
      hasSession: !!session,
      userId: session?.user?.id,
      error: sessionError?.message
    });
    
    // Check user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log(`[${timestamp}] [DebugAuth] User:`, {
      hasUser: !!user,
      userId: user?.id,
      metadata: user?.user_metadata,
      error: userError?.message
    });
    
    // Check profile if user exists
    if (user) {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('email_verified, password_set, email, first_name, last_name')
        .eq('id', user.id)
        .single();
        
      console.log(`[${timestamp}] [DebugAuth] Profile:`, {
        profile,
        error: profileError?.message
      });
    }
    
    // Check current URL
    if (typeof window !== 'undefined') {
      console.log(`[${timestamp}] [DebugAuth] Current URL:`, window.location.href);
    }
    
  } catch (error) {
    console.error(`[${timestamp}] [DebugAuth] Error during debug:`, error);
  }
}

/**
 * Create a manual auth state override for emergency situations
 */
export function createAuthStateOverride() {
  return {
    forceUserState: async (userId: string) => {
      try {
        console.log('[AuthOverride] Forcing user state for:', userId);
        const { data: { user }, error } = await supabase.auth.getUser();
        
        if (error || !user || user.id !== userId) {
          throw new Error('User mismatch or not authenticated');
        }
        
        return user;
      } catch (error) {
        console.error('[AuthOverride] Failed to force user state:', error);
        throw error;
      }
    },
    
    clearAuthCache: () => {
      try {
        console.log('[AuthOverride] Clearing auth cache...');
        // Clear any cached auth data
        if (typeof window !== 'undefined') {
          const keys = Object.keys(localStorage).filter(key => 
            key.startsWith('supabase.auth.') || key.startsWith('processed_code_')
          );
          keys.forEach(key => localStorage.removeItem(key));
        }
      } catch (error) {
        console.error('[AuthOverride] Failed to clear cache:', error);
      }
    }
  };
}

/**
 * Wait for auth state to be ready with configurable timeout
 */
export async function waitForAuthReady(timeoutMs: number = 10000): Promise<{
  ready: boolean;
  user: User | null;
  timedOut: boolean;
}> {
  const startTime = Date.now();
  const pollInterval = 500; // Check every 500ms
  
  return new Promise((resolve) => {
    const checkAuth = async () => {
      try {
        const { user, isAuthenticated } = await getDirectAuthState();
        
        if (isAuthenticated && user) {
          resolve({
            ready: true,
            user,
            timedOut: false
          });
          return;
        }
        
        // Check timeout
        if (Date.now() - startTime >= timeoutMs) {
          resolve({
            ready: false,
            user: null,
            timedOut: true
          });
          return;
        }
        
        // Continue polling
        setTimeout(checkAuth, pollInterval);
      } catch (error) {
        console.error('[WaitForAuth] Error checking auth:', error);
        setTimeout(checkAuth, pollInterval);
      }
    };
    
    checkAuth();
  });
}