import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import { updateSession } from '@/supabase/middleware'

// Add this to your middleware.ts
function cleanupBadCookies(request: NextRequest, response: NextResponse) {
  // Remove the bad cookie with undefined project ID
  const badCookieName = 'sb-undefined-auth-token-code-verifier';
  if (request.cookies.get(badCookieName)) {
    response.cookies.delete(badCookieName);
    console.log('Middleware: Removed bad cookie:', badCookieName);
  }
}

export async function middleware(req: NextRequest) {
  // First, run the Supabase session management to handle cookies
  const res = await updateSession(req);
  console.log('Middleware: updateSession response cookies:', res.cookies.getAll());

  // Call the cleanup function
  cleanupBadCookies(req, res);

  // Create a Supabase client to get the session
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get: (name: string) => {
          const value = req.cookies.get(name)?.value;
          console.log(`Middleware: Cookie get - Name: ${name}, Value: ${value ? "exists" : "undefined"}`);
          return value;
        },
        set: (name: string, value: string, options: CookieOptions) => {
          // For auth-related cookies, preserve them with consistent options
          const isAuthCookie = name.includes('auth-token') || 
                             name.includes('code-verifier') || 
                             name.includes('flow-state');
          
          const cookieOptions = {
            name,
            value,
            ...options,
            secure: process.env.NODE_ENV === 'production',
            httpOnly: true,
            sameSite: 'lax' as const,
            path: '/',
            maxAge: isAuthCookie ? 60 * 60 : options.maxAge // 1 hour for auth cookies
          };

          res.cookies.set(cookieOptions);
          console.log(`Middleware: Cookie ${isAuthCookie ? 'preserved' : 'set'} - Name: ${name}`);
        },
        remove: (name: string, options: CookieOptions) => {
          // Don't remove auth-related cookies during PKCE flow
          const isAuthCookie = name.includes('auth-token') || 
                             name.includes('code-verifier') || 
                             name.includes('flow-state');
          
          if (isAuthCookie && req.nextUrl.pathname === '/auth/set-password') {
            console.log(`Middleware: Preserving auth cookie - Name: ${name}`);
            return;
          }

          res.cookies.set({
            name,
            value: '',
            ...options,
            secure: process.env.NODE_ENV === 'production',
            httpOnly: true,
            sameSite: 'lax' as const,
            path: '/'
          });
          console.log(`Middleware: Cookie removed - Name: ${name}`);
        },
      },
    }
  );

  const { data: { session } } = await supabase.auth.getSession();
  const path = req.nextUrl.pathname;

  // Define route types
  const isProtectedRoute = path.startsWith('/dashboard');
  const isAuthRoute = ['/sign-in', '/sign-up', '/login'].includes(path);
  const isPasswordResetRoute = path === '/auth/set-password';

  // If on the password reset page, ensure PKCE flow state is preserved
  if (isPasswordResetRoute) {
    // Log all cookies for debugging
    console.log('Middleware: Processing /auth/set-password');
    const projectId = process.env.NEXT_PUBLIC_SUPABASE_PROJECT_ID || 'default';
    const codeVerifierCookie = req.cookies.get(`sb-${projectId}-auth-token-code-verifier`)?.value;
    console.log('Request cookies:', req.cookies.getAll());
    console.log('Response cookies:', res.cookies.getAll());
    console.log('Middleware: Code verifier cookie value:', codeVerifierCookie ? codeVerifierCookie : "Not found");

    // Get all auth-related cookies
    const allCookies = req.cookies.getAll();
    const authCookies = allCookies.filter(cookie =>
      cookie.name === `sb-${projectId}-auth-token` ||
      cookie.name === `sb-${projectId}-auth-token-code-verifier` ||
      cookie.name.startsWith(`sb-${projectId}-auth-token.`) ||
      cookie.name.includes('code-verifier') ||
      cookie.name.includes('flow-state')
    );

    // Preserve all auth-related cookies
    authCookies.forEach(cookie => {
      const cookieOptions = {
        name: cookie.name,
        value: cookie.value,
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax' as const,
        httpOnly: true,
        maxAge: 60 * 60 // 1 hour
      };
      res.cookies.set(cookieOptions);
      console.log(`Middleware: Preserved auth cookie - Name: ${cookie.name}`);
    });

    // Return early to avoid any other cookie modifications
    return res;
  }

  if (session) {
    // If the user is logged in, redirect away from auth routes
    if (isAuthRoute) {
      return NextResponse.redirect(new URL('/dashboard', req.url));
    }

    // Determine user role for route protection
    let userRole = 'expert'; // default

    // First priority: Check user metadata (most reliable during development)
    const metadataRole = session.user.user_metadata?.role;
    if (metadataRole) {
      userRole = metadataRole.toLowerCase();
      console.log("Middleware: Role found in user metadata:", userRole);
    } else {
      // Second priority: Try database tables (handle RLS errors gracefully)
      try {
        const { data: roleData, error: roleError } = await supabase
          .from('user_roles')
          .select(`
            roles (
              name,
              is_admin_role
            )
          `)
          .eq('user_id', session.user.id)
          .maybeSingle();

        if (!roleError && roleData?.roles) {
          const roles = roleData.roles as unknown as { name: string; is_admin_role: boolean };
          userRole = roles.name?.toLowerCase() || 'expert';
          console.log("Middleware: Role found in user_roles table:", userRole);
        } else {
          // Fallback: check if user is an expert (handle RLS errors)
          try {
            const { data: expertData, error: expertError } = await supabase
              .from('expert_profiles')
              .select('id')
              .eq('id', session.user.id)
              .maybeSingle();

            if (!expertError && expertData) {
              userRole = 'expert';
              console.log("Middleware: User identified as expert");
            }
          } catch (expertError) {
            console.log("Middleware: Could not access expert_profiles table");
          }
        }
      } catch (error) {
        console.log("Middleware: Could not access user_roles table, using default role");
      }
    }

    // Define admin-only routes
    const adminOnlyRoutes = [
      '/dashboard/users',
      '/dashboard/management',
      '/dashboard/analytics',
      '/dashboard/settings'
    ];

    // Check if current path is admin-only
    const isAdminOnlyRoute = adminOnlyRoutes.some(route => path.startsWith(route));

    // Block non-admin users from accessing admin routes
    if (isAdminOnlyRoute && userRole !== 'admin') {
      console.log(`Access denied: ${userRole} user trying to access admin route: ${path}`);
      return NextResponse.redirect(new URL('/dashboard', req.url));
    }

    // Handle expert verification requirements (agriculture_expert, industrial_expert, or expert)
    if ((userRole === 'agriculture_expert' || userRole === 'industrial_expert' || userRole === 'expert') && isProtectedRoute) {
      // Get user profile to check account_activated status (handle RLS errors gracefully)
      let isAccountActivated = false;
      
      try {
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('account_activated')
          .eq('id', session.user.id)
          .maybeSingle();

        if (!profileError && profile) {
          isAccountActivated = profile.account_activated === true;
          console.log("Middleware: Account activated status:", isAccountActivated);
        } else {
          console.log("Middleware: Could not check account_activated status, assuming false");
          // If we can't check the database, assume not activated for security
          isAccountActivated = false;
        }
      } catch (error) {
        console.log("Middleware: Error checking account_activated status, assuming false");
        // If we can't check the database, assume not activated for security
        isAccountActivated = false;
      }

      // Define routes accessible to unverified experts
      const expertAllowedRoutes = [
        '/dashboard',
        '/dashboard/my-profile',
        '/dashboard/verification-pending',
        '/dashboard/settings'
      ];

      // Define routes that require verification
      const expertRestrictedRoutes = [
        '/dashboard/consultations',
        '/dashboard/requests',
        '/dashboard/analytics',
        '/dashboard/management'
      ];

      const isAllowedRoute = expertAllowedRoutes.some(route => 
        path === route || path.startsWith(route)
      );

      const isRestrictedRoute = expertRestrictedRoutes.some(route => 
        path.startsWith(route)
      );

      // If account is not activated
      if (!isAccountActivated) {
        // If trying to access a restricted route, redirect to verification pending
        if (isRestrictedRoute) {
          return NextResponse.redirect(new URL('/dashboard/verification-pending', req.url));
        }

        // If not on an allowed route and not already on verification pending, redirect there
        if (!isAllowedRoute && path !== '/dashboard/verification-pending') {
          return NextResponse.redirect(new URL('/dashboard/verification-pending', req.url));
        }
      }
    }
  } else if (isProtectedRoute) {
    // If no session and trying to access a protected route, redirect to sign-in
    return NextResponse.redirect(new URL('/sign-in', req.url));
  }

  return res;
}

export const config = {
  matcher: [
    // Match auth-related paths
    '/auth/:path*',
    '/sign-in',
    '/sign-up',
    '/forgot-password',
    '/dashboard/:path*',
    // Match all paths except static files and images
    '/((?!_next/static|_next/image|favicon.ico|public|images|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
