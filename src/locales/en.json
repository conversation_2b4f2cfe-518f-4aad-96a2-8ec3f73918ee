{"pageTitles": {"consultations": "Active Consultations", "consultationDetail": "Consultation Detail", "dashboard": "Dashboard"}, "common": {"searchPlaceholder": "Search by farmer or village...", "back": "Back", "loading": "Loading...", "error": "An error occurred."}, "validation": {"required": "This field is required"}, "consultationCard": {"lastUpdate": "Last update", "ago": "ago", "hour": "hr", "hours": "hrs", "day": "day"}, "consultationDetailTabs": {"details": "Consultation Details", "farmData": "Farm Data", "communication": "Communication"}, "consultationDetails": {"price": "Price", "startDate": "Start Date", "endDate": "End Date", "duration": "Duration", "consultationId": "Consultation ID", "extend": "Extend Consultation", "markComplete": "Mark as Complete"}, "sidebar": {"dashboard": "Dashboard", "users": "Users", "management": "Assets Management", "analytics": "Analytics", "settings": "Settings", "requests": "Requests", "consultations": "Consultations", "myProfile": "My Profile", "support": "Support"}, "usersPage": {"title": "Users Management", "description": "Manage and view all registered users on the platform", "cardTitle": "Platform Users", "cardDescription": "View and manage all registered experts and farmers", "searchPlaceholder": "Search users...", "expertsTab": "Experts", "farmersTab": "Farmers & Workers", "expertColumn": "Expert", "expertiseColumn": "Expertise", "statusColumn": "Status", "userType": "User Type", "joinedColumn": "Joined", "actionsColumn": "Actions", "noExpertsTitle": "No experts found", "noExpertsDescription": "Try adjusting your search criteria", "generalAgriculture": "General Agriculture", "available": "Available", "unavailable": "Unavailable", "viewAction": "View", "farmerColumn": "<PERSON>", "locationColumn": "Location", "noFarmersTitle": "No farmers found", "noFarmersDescription": "Try adjusting your search criteria", "notSpecified": "Not specified", "noFarmersFound": "No farmers found. Try adjusting your search criteria.", "loading": "Loading users...", "unauthorized": "You are not authorized to view this page.", "approveAction": "Approve", "rejectAction": "Reject", "errorTitle": "Error", "approveError": "Failed to approve expert. Please try again.", "successTitle": "Success", "approveSuccess": "Expert approved successfully.", "verificationQueueTab": "Expert Verification Queue", "allExpertsTab": "All Experts", "documentStatus": "Document Status", "documentsUploaded": "Documents Uploaded", "documentsRequired": "Documents Required", "requestDocuments": "Request Documents", "viewDocuments": "View Documents", "experienceColumn": "Experience", "documentsColumn": "Documents", "submittedColumn": "Submitted", "filters": "Filters", "allStatuses": "All Statuses", "allAvailability": "All Availability", "availability": "Availability", "verificationStatus": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "in_review": "In Review", "active": "Active", "documents_required": "Documents Required", "under_review": "Under Review"}, "noExpertsFound": "No experts found. Try adjusting your search criteria.", "status": "Status", "expert": "Expert", "farmer": "Farmer/Worker", "experience": "Experience", "becomeVerifiedExpert": "Become a Verified Expert", "verificationInitialDescription": "To access all features and build trust with clients, please complete your expert verification.", "startVerification": "Start Verification", "expertVerificationForm": "Expert Verification Form", "verificationFormDescription": "Please provide the required information and documents below. This helps us ensure the authenticity and quality of our expert community.", "verificationSubmittedTitle": "Verification Submitted for Review", "verificationSubmittedDescription": "Thank you. Our team is currently reviewing your verification request. You will be notified via email once the review is complete.", "verificationActionRequired": "Verification Action Required"}, "managementPage": {"title": "Asset Registration Management", "description": "Review and manage asset registration requests from users", "cardTitle": "Registration Requests", "cardDescription": "Review pending asset registration requests and approve or reject them", "searchPlaceholder": "Search by user name, asset name, or location...", "filterByType": "Type", "filterByStatus": "Status", "allTypes": "All Types", "allStatuses": "All Statuses", "farm": "Farm", "factory": "Factory", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "resubmitted": "Resubmitted", "userColumn": "User", "assetColumn": "Asset Name", "typeColumn": "Type", "statusColumn": "Status", "submittedColumn": "Submitted", "actionsColumn": "Actions", "noRequestsTitle": "No registration requests found", "noRequestsDescription": "Try adjusting your search criteria or filters", "viewDetailsAction": "View Details", "approveAction": "Approve", "rejectAction": "Reject", "loading": "Loading registration requests...", "totalRequests": "Total Requests"}, "analyticsPage": {"title": "Analytics", "description": "Platform insights and performance metrics", "totalExperts": "Total Experts", "totalFarmers": "Total Farmers", "totalRevenue": "Total Revenue", "consultations": "Consultations", "fromLastMonth": "from last month", "fromYesterday": "from yesterday", "userActivityTitle": "User Activity", "userActivityDescription": "Daily new users over the past 30 days"}, "settingsPage": {"title": "Account <PERSON><PERSON>", "description": "Manage your account preferences and profile", "profileTab": "Profile", "languageCardTitle": "Language", "languageCardDescription": "Choose your preferred language for the application.", "verificationCardTitle": "Verification Status", "verificationCardDescription": "Your current verification status as an agricultural expert.", "statusLabel": "Status:", "verifiedStatus": "Verified Expert", "pendingStatus": "Pending Verification", "rejectedStatus": "Rejected", "pictureCardTitle": "Profile Picture", "pictureCardDescription": "Update your profile picture. This will be displayed on your profile.", "uploadAction": "Upload", "personalInfoCardTitle": "Personal Information", "personalInfoCardDescription": "Update your personal information. This information will be displayed on your profile.", "expertVerificationLabel": "Expert Verification Status", "fullNameLabel": "Full Name", "fullNamePlaceholder": "<PERSON>", "emailLabel": "Email", "emailPlaceholder": "<EMAIL>", "roleLabel": "Role", "administratorRole": "Administrator", "expertRole": "Expert", "locationLabel": "Location", "locationPlaceholder": "City, Country", "adminDetailsLabel": "Admin Details", "adminDetailsPlaceholder": "Describe your administrative role and responsibilities", "saveChangesAction": "Save Changes"}, "requestsPage": {"title": "Consultation Requests", "description": "Manage incoming consultation requests from farmers", "searchPlaceholder": "Search by farmer name, request title, or category...", "allStatus": "All Status", "pendingStatus": "Pending", "acceptedStatus": "Accepted", "declinedStatus": "Declined", "noRequestsTitle": "No requests found", "noRequestsFilterDescription": "Try adjusting your search or filter", "noRequestsDescription": "You don't have any consultation requests yet", "preferredDate": "Preferred Date", "budget": "Budget", "currency": "EGP", "duration": "Duration", "days": "days", "type": "Type", "crops": "Crops", "received": "Received", "declineAction": "Decline", "acceptAction": "Accept", "startConsultationAction": "Start Consultation", "viewConsultation": "View Consultation", "requestDeclined": "Request Declined"}, "consultationDetail": {"farmerName": "Farmer Name", "farmerPhone": "Farmer Phone", "farmProfile": "Farm Profile", "farmName": "Name", "farmLocation": "Location", "farmSize": "Size", "soilType": "Soil Type", "waterSource": "Water Source", "waterAvailability": "Water Availability", "tools": "Tools", "crops": "Crops", "cropName": "Name", "plantingDate": "Planting Date", "cropArea": "Area", "historicalData": "Historical Data", "yieldHistory": "Yield History", "crop": "Crop", "year": "Year", "amount": "Amount", "fertilizerHistory": "Fertilizer History", "date": "Date", "type": "Type", "pestHistory": "Pest Incidents History", "pest": "Pest", "severity": "Severity", "pesticideHistory": "Pesticide History", "name": "Name", "realTimeEvents": "Real-Time Events", "event": "Event", "category": "Category", "description": "Description", "details": "Details", "images": "Images", "chat": "Cha<PERSON>", "typeMessagePlaceholder": "Type your message...", "sendAction": "Send", "internalNotes": "Internal Notes", "internalNotesDescription": "(these notes are only visible to you)", "saveNotesAction": "Save Notes", "recommendations": "Recommendations", "recommendationPlaceholder": "Enter your recommendation...", "sendRecommendationAction": "Send Recommendation"}, "myProfile": {"title": "Professional Profile", "description": "Manage your professional profile details, expertise, and specializations.", "editProfile": "Edit Profile", "noProfile": "No professional profile information saved yet. Click 'Edit Profile' to add your details.", "professionalInfo": "Professional Information", "available": "Available", "notAvailable": "Not Available", "editDescription": "Edit your professional details, education, and experience.", "viewDescription": "View your professional details, education, and experience.", "professionalBio": "Professional Bio", "bioPlaceholder": "Share your professional background and expertise...", "availabilityTitle": "Available for Consultations", "availabilityDescription": "Toggle this to show/hide your profile from farmers looking for consultations", "specialization": "Agricultural Specialization", "education": "Education", "university": "University", "selectUniversity": "Select University", "college": "College", "selectCollege": "Select College", "degree": "Degree", "degreePlaceholder": "e.g., Bachelor of Agricultural Sciences", "graduationYear": "Graduation Year", "yearPlaceholder": "YYYY", "grade": "Grade/GPA", "gradePlaceholder": "e.g., 3.5 or Excellent", "addEducation": "Add Education", "workExperience": "Work Experience", "company": "Company", "companyPlaceholder": "Company name", "position": "Position", "positionPlaceholder": "Job title/position", "startDate": "Start Date", "endDate": "End Date", "current": "Current", "currentlyWorking": "Currently working here", "workDescriptionPlaceholder": "Describe your responsibilities and achievements...", "addWorkExperience": "Add Work Experience", "pricingTitle": "Consultation Services & Pricing", "pricePerSession": "Price per Session (EGP)", "supportDuration": "Support Duration (days)", "oneDay": "1 day", "threeDays": "3 days", "sevenDays": "7 days", "fourteenDays": "14 days", "thirtyDays": "30 days", "serviceAreas": "Service Areas", "offerOnsite": "Offer Onsite Consultations", "serviceAreasPlaceholder": "e.g., Cairo, Alexandria, Delta", "serviceAreasDescription": "Specify the areas where you provide onsite consultation services", "enableOnsiteDescription": "Enable onsite consultations to specify service areas", "onsitePrice": "Onsite Price per Visit (EGP)", "onsiteDuration": "Onsite Support Duration (days)", "ownTransportation": "I have my own transportation", "addBio": "Add professional bio", "addSpecialization": "Add specialization", "present": "Present", "addServiceAreas": "Add service areas", "onsiteNotAvailable": "Onsite consultations not available", "addConsultationServices": "Add consultation services", "cancel": "Cancel", "saveChanges": "Save Changes", "currency": "EGP", "day": "day", "days": "days", "onsiteConsultation": "Onsite Consultation", "hasTransportation": "Has own transportation", "expertiseOptions": {"crop_production": "Crop Production", "livestock_management": "Livestock Management", "irrigation_systems": "Irrigation Systems", "soil_science": "Soil Science", "agricultural_economics": "Agricultural Economics", "pest_management": "Pest Management", "horticulture": "Horticulture", "aquaculture": "Aquaculture", "food_processing": "Food Processing", "sustainable_agriculture": "Sustainable Agriculture", "agricultural_engineering": "Agricultural Engineering", "veterinary_science": "Veterinary Science", "rural_development": "Rural Development", "agribusiness_management": "Agribusiness Management", "climate_change_adaptation_in_agriculture": "Climate Change Adaptation in Agriculture"}}, "dashboard": {"welcome": "Welcome back,", "admin": "Admin", "expert": "Expert", "consultationRequests": "Consultation Requests", "requestsDescription": "Farmers requesting your expertise", "new": "New", "noPendingRequests": "No pending consultation requests", "soilAnalysis": "Soil Analysis Help", "pestControl": "Pest Control Advice", "wheat": "Wheat", "tomatoes": "Tomatoes", "acres": "acres", "locale": "en-US"}, "supportPage": {"title": "Support", "description": "Get help and manage support tickets", "newTicket": "New Ticket", "openTickets": "Open Tickets", "highPriority": "high priority", "resolvedToday": "Resolved Today", "avgResponse": "Average response:", "totalTickets": "Total Tickets", "fromLastWeek": "from last week", "tickets": "Tickets", "latestRequests": "Your latest support requests", "searchPlaceholder": "Search tickets...", "priority": "Priority", "viewAllTickets": "View All Tickets", "contactSupport": "Contact Support", "submitRequestDescription": "Submit a new support request or get help", "subject": "Subject", "subjectPlaceholder": "Brief description of your issue", "category": "Category", "technicalIssue": "Technical Issue", "accountProblem": "Account Problem", "featureRequest": "Feature Request", "billingQuestion": "Billing Question", "other": "Other", "descriptionLabel": "Description", "descriptionPlaceholder": "Please provide detailed information about your issue...", "submitTicket": "Submit Ticket", "otherWaysToHelp": "Other Ways to Get Help", "phoneSupport": "Phone Support", "emailSupport": "Email Support", "documentation": "Documentation", "browseHelpCenter": "Browse our help center", "low": "Low", "medium": "Medium", "high": "High", "critical": "Critical", "open": "Open", "inprogress": "In Progress"}, "resetPassword": {"title": "Reset password", "description": "Please enter your new password below.", "newPassword": "New password", "newPasswordPlaceholder": "New password", "confirmPassword": "Confirm password", "confirmPasswordPlaceholder": "Confirm password", "pendingText": "Resetting password...", "submitButton": "Reset password"}, "consultationStatus": {"inProgress": "In Progress", "finished": "Finished", "active": "Active", "pending": "Pending", "accepted": "Accepted", "declined": "Declined", "viewConsultation": "View Consultation"}, "consultationsPage": {"title": "My Consultations", "searchPlaceholder": "Search consultations...", "description": "View and manage your active consultations"}, "verificationPendingPage": {"pendingTitle": "Verification Submitted for Review", "pendingDescription": "Thank you. Our team is currently reviewing your verification request. You will be notified via email once the review is complete.", "rejectedTitle": "Verification Rejected", "rejectedDescription": "Your expert profile did not meet the verification criteria. Please review your profile and resubmit if necessary.", "inReviewTitle": "Profile Under Review", "inReviewDescription": "Your profile is currently under review. This process usually takes 1-2 business days.", "nextSteps": "You can update your profile information while you wait.", "goToSettings": "Go to Settings", "submissionFailed": "Submission Failed", "submissionFailedDescription": "Failed to submit your application. Please try again.", "submissionSuccess": "Application Submitted", "submissionSuccessDescription": "Your expert verification application has been submitted successfully and is now under review."}, "verification": {"becomeVerifiedExpert": "Become a Verified Expert", "verificationInitialDescription": "To access all features and build trust with clients, please complete your expert verification.", "startVerification": "Start Verification", "expertVerificationForm": "Expert Verification Form", "verificationFormDescription": "Please provide the required information and documents below. This helps us ensure the authenticity and quality of our expert community.", "verificationSubmittedTitle": "Verification Submitted for Review", "verificationSubmittedDescription": "Thank you. Our team is currently reviewing your verification request. You will be notified via email once the review is complete.", "verificationRejected": "Verification Application Rejected", "verificationActionRequired": "Verification Action Required", "rejectionDescription": "Your application was not approved. Please review the feedback and resubmit.", "additionalInfoRequired": "Additional Information Required", "additionalInfoDescription": "Please provide additional information or documents as requested.", "viewStatus": "View Status", "reviewResubmit": "Review & Resubmit", "completeApplication": "Complete Application", "rejectionReasons": "Rejection Reasons:", "adminNotes": "Admin Notes:", "uploadAllDocuments": "Upload All Supporting Documents", "documentsInstructions": "Please upload all required and optional documents below. Ensure all documents are clear, authenticated, and in a supported format.", "requiredDocuments": "Required Documents:", "nationalIdRequired": "Clear image of your National ID or Passport.", "professionalCertificatesRequired": "Professional certificates or licenses relevant to your expertise.", "educationalDegreesRequired": "Highest educational degrees or diplomas.", "optionalDocuments": "Optional Documents:", "workExperienceLettersOptional": "Work experience letters or employment verification.", "awardsHonorsOptional": "Awards, honors, or significant achievements.", "otherRelevantDocuments": "Any other documents supporting your expertise.", "documentQualityNote": "Please ensure all documents are high-resolution and clearly legible. Unclear documents may lead to delays in verification.", "dragAndDrop": "Drag and drop your files here, or", "chooseFiles": "<PERSON><PERSON>", "supportedFormats": "Supported formats: PDF, JPG, PNG (Max 10MB per file)", "documents": "Documents", "expertType": "Expert Type", "expertTypeRequired": "Please select your area of expertise", "expertTypeAgriculture": "Agriculture Expert", "expertTypeIndustrial": "Industrial Expert", "selectExpertType": "Select Expert Type", "basicInformation": "Basic Information", "education": "Education", "experience": "Experience", "expertiseArea": "Area of Expertise", "expertiseAreaPlaceholder": "e.g., Crop Management, Soil Analysis, Food Processing", "yearsOfExperience": "Years of Experience", "professionalBio": "Professional Biography", "bioPlaceholder": "Describe your professional background, expertise, and experience...", "educationPlaceholder": "List your degrees, certifications, and educational background...", "currentPosition": "Current Position", "currentPositionPlaceholder": "e.g., Senior Agricultural Consultant", "organization": "Organization/Company", "organizationPlaceholder": "e.g., Ministry of Agriculture, ABC Consulting", "certifications": "Professional Certifications", "certificationsPlaceholder": "List your professional certifications and licenses...", "professionalMemberships": "Professional Memberships", "membershipsPlaceholder": "Professional associations and organizations you belong to...", "languagesSpoken": "Languages Spoken", "languagesPlaceholder": "e.g., Arabic, English, French", "accuracyNotice": "Please ensure all information provided is accurate and authentic to expedite the review process", "featureAvailableAfterVerification": "{{feature}} will be available once your verification is approved."}, "userDetailPage": {"title": "User Details", "notFound": "User not found", "backButton": "Back", "loading": "Loading user details...", "personalInfoTitle": "Personal Information", "phoneLabel": "Phone", "languageLabel": "Language", "joinedLabel": "Joined", "notProvided": "Not provided", "expertProfileTitle": "Expert Profile", "bioLabel": "Biography", "experienceLabel": "Years of Experience", "years": "years", "educationLabel": "Education", "verificationTitle": "Verification Status", "documentsTitle": "Verification Documents", "historyTitle": "Verification History", "overviewTab": "Expert Information", "documentsTab": "Documents", "historyTab": "History", "approveButton": "Approve", "rejectButton": "Reject", "requestDocumentsButton": "Request Documents", "noDocumentsTitle": "No Documents Uploaded", "noDocumentsDescription": "The expert hasn't uploaded any verification documents yet.", "accountStatusTitle": "Account Status", "roleLabel": "Role", "accountStatusLabel": "Account Status", "verificationProcessTitle": "Verification Process", "verificationProcessDescription": "Track the expert verification progress and status", "downloadDocument": "Download", "viewDocument": "View", "documentType": "Type", "documentSize": "Size", "uploadDate": "Uploaded", "verificationStatus": "Status", "personalInformationTitle": "Expert Details", "personalInformationDescription": "Basic personal details and contact information", "professionalInformationTitle": "Professional Information", "professionalInformationDescription": "Expert qualifications, experience, and professional details", "educationQualificationsTitle": "Education & Qualifications", "educationQualificationsDescription": "Educational background, certifications, and professional achievements", "fullNameLabel": "Full Name", "emailLabel": "Email", "joinedDateLabel": "Joined Date", "verificationStatusLabel": "Verification Status", "expertTypeLabel": "Expert Type", "areaOfExpertiseLabel": "Area of Expertise", "yearsOfExperienceLabel": "Years of Experience", "currentPositionLabel": "Current Position", "organizationLabel": "Organization", "languagesSpokenLabel": "Languages Spoken", "educationBackgroundLabel": "Education Background", "qualificationsLabel": "Qualifications", "professionalCertificationsLabel": "Professional Certifications", "professionalMembershipsLabel": "Professional Memberships", "awardsHonorsLabel": "Awards & Honors", "professionalBioLabel": "Professional Bio", "notSpecified": "Not specified", "agricultureExpert": "Agriculture Expert", "industrialExpert": "Industrial Expert", "lastUpdated": "Last updated", "uploaded": "Uploaded", "documentsCount": "documents", "documentCount": "document"}, "registrationDetailsModal": {"title": "Registration Request Details", "description": "Review complete registration request information", "loadingText": "Loading registration details...", "failedToLoad": "Failed to load registration details", "requestId": "Request ID:", "checkConsole": "Please check the browser console for more details.", "retryButton": "Retry", "closeButton": "Close", "approveButton": "Approve", "rejectButton": "Reject", "requestSummary": "Request Summary", "basicInfoTab": "Basic Info", "locationTab": "Location", "contactTab": "Contact", "mediaTab": "Media", "basicInfo": {"title": "Basic Information", "noInfoAvailable": "No basic information available", "name": "Name", "ownerName": "Owner Name", "size": "Size", "description": "Description", "notAvailable": "Not Available"}, "locationInfo": {"title": "Location Information", "address": "Address", "city": "City", "governorate": "Governorate", "coordinates": "Coordinates"}, "contactInfo": {"title": "Contact Information", "contactPerson": "Contact Person", "phone": "Phone", "email": "Email", "alternativePhone": "Alternative Phone"}, "images": {"title": "Images", "noImages": "No images uploaded", "imageAlt": "Image"}, "approvalHistory": {"title": "Approval History", "noHistory": "No approval history available"}, "userInfo": {"submitted": "Submitted:", "type": "Type:", "asset": "Asset:", "location": "Location:"}, "entityTypes": {"FARM": "Farm", "FACTORY": "Factory"}, "statuses": {"PENDING": "Pending", "APPROVED": "Approved", "REJECTED": "Rejected", "RESUBMITTED": "Resubmitted"}, "actions": {"APPROVED": "Approved", "REJECTED": "Rejected"}}, "historyTab": {"title": "Verification History", "description": "Track of all verification status changes and admin actions", "noHistory": "No verification history available", "signupTitle": "Account Created", "signupDescription": "Expert registered on the platform", "formSubmissionTitle": "Verification Form Submitted", "formSubmissionDescription": "<PERSON><PERSON> submitted the verification form", "statusChanged": "Status changed from", "to": "to", "statusRegistered": "Registered"}}