import { createClient } from "@/supabase/server";
import { unstable_noStore as noStore } from 'next/cache';

export async function fetchCardData() {
  noStore();
  const supabase = createClient();
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

    const [
      { count: totalUsers },
      { count: lastMonthUsers },
      { count: previousMonthUsers },
      { count: totalConsultations },
    ] = await Promise.all([
      supabase.from("profiles").select("*", { count: "exact", head: true }),
      supabase.from("profiles").select("*", { count: "exact", head: true }).gte('created_at', thirtyDaysAgo.toISOString()),
      supabase.from("profiles").select("*", { count: "exact", head: true }).gte('created_at', sixtyDaysAgo.toISOString()).lt('created_at', thirtyDaysAgo.toISOString()),
      supabase.from("consultations").select("*", { count: "exact", head: true }),
    ]);

    const growthRate = lastMonthUsers && previousMonthUsers ? ((lastMonthUsers - previousMonthUsers) / previousMonthUsers) * 100 : 0;

    return {
      totalUsers,
      totalConsultations,
      growthRate: growthRate.toFixed(2),
    };
  } catch (error) {
    console.error("Database Error:", error);
    throw new Error("Failed to fetch card data.");
  }
}

export async function fetchChartData() {
    noStore();
    const supabase = createClient();
    try {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const { data, error } = await supabase
            .from('profiles')
            .select('created_at')
            .gte('created_at', thirtyDaysAgo.toISOString());

        if (error) {
            console.error('Database Error:', error);
            throw new Error('Failed to fetch chart data.');
        }

        const dailyCounts = data.reduce((acc, { created_at }) => {
            const date = new Date(created_at).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
            });
            acc[date] = (acc[date] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const chartData = Array.from({ length: 30 }, (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const formattedDate = date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
            });
            return {
                date: formattedDate,
                count: dailyCounts[formattedDate] || 0,
            };
        }).reverse();

        return chartData;
    } catch (error) {
        console.error('Database Error:', error);
        throw new Error('Failed to fetch chart data.');
    }
}