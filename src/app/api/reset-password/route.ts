import { NextResponse } from 'next/server'
import { createClient } from '@/supabase/server'
import { headers } from 'next/headers'

export async function POST(request: Request) {
  try {
    const { email } = await request.json()
    const origin = headers().get("origin")

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${origin}/auth/confirm`,
    })

    if (error) {
      console.error("Password reset error:", error.message)
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json({ 
      message: 'If an account exists, a password reset link has been sent to your email.' 
    })

  } catch (error) {
    console.error("Unexpected error during password reset:", error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
