import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/supabase/server';
import { getUserRole } from '@/utils/get-user-role';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userRole = await getUserRole(user);
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, adminNotes, rejectionReasons } = body;

    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "approve" or "reject"' },
        { status: 400 }
      );
    }

    const expertId = params.id;
    
    // Validate expert exists
    const { data: expertProfile, error: expertError } = await supabase
      .from('expert_profiles')
      .select('id, verification_status')
      .eq('id', expertId)
      .single();

    if (expertError || !expertProfile) {
      return NextResponse.json(
        { error: 'Expert not found' },
        { status: 404 }
      );
    }

    // Prepare parameters for the database function
    const newStatus = action === 'approve' ? 'approved' : 'rejected';
    const functionParams: any = {
      p_expert_id: expertId,
      p_new_status: newStatus,
      p_admin_notes: adminNotes || null
    };

    // Add rejection reasons if rejecting
    if (action === 'reject' && rejectionReasons) {
      if (Array.isArray(rejectionReasons)) {
        functionParams.p_rejection_reasons = rejectionReasons;
      } else if (typeof rejectionReasons === 'string') {
        functionParams.p_rejection_reasons = [rejectionReasons];
      }
    }

    // Call the database function to update verification status
    const { data, error } = await supabase.rpc(
      'update_expert_verification_status',
      functionParams
    );

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to update verification status' },
        { status: 500 }
      );
    }

    // If approved, also update the account_activated flag in profiles table
    if (action === 'approve') {
      const { error: profileUpdateError } = await supabase
        .from('profiles')
        .update({ account_activated: true })
        .eq('id', expertId);

      if (profileUpdateError) {
        console.error('Error updating account_activated:', profileUpdateError);
        // Don't fail the request as the main verification was successful
      }
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: `Expert ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
      data: data
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}