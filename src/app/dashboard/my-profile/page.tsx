"use client";

import { useRouter } from "next/navigation";
import { supabase } from "@/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useEffect, useState } from "react";
import { User } from "@supabase/supabase-js";
import { useTranslation } from "@/components/i18n-provider";
import { usePageTitle } from "@/components/page-title-provider";
import { v4 as uuidv4 } from 'uuid'; // For unique IDs for education entries

// Define types for the user profile and expert-specific data
interface UserProfile {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  profile_picture_url?: string;
  user_metadata?: { [key: string]: unknown; expertise?: string };
}

interface EducationEntry {
  id: string; // Unique ID for each entry, for keying and removal
  university: string | null;
  college: string | null;
  graduationYear: string;
  degree: string;
  grade: string; // New field for grade/GPA
  // details: string; // Removed
}

interface WorkExperienceEntry {
  id: string; // Unique ID for each entry, for keying and removal
  company: string;
  position: string;
  startDate: string;
  endDate: string;
  isCurrent: boolean;
  description: string;
}

interface ConsultationPricing {
  online_consultation_price: number;
  online_consultation_duration: number; // in days
  onsite_consultation_price: number;
  onsite_consultation_duration: number; // in days
}

interface ExpertProfile {
  id?: string;
  user_id: string;
  bio?: string;
  expertise_summary?: string;
  education?: EducationEntry[];
  work_experience?: WorkExperienceEntry[];
  verification_status?: string;
  is_available?: boolean;
  consultation_pricing?: ConsultationPricing;
  onsite_consultation_available?: boolean;
  has_car?: boolean;
  service_areas?: string[];
  primary_specialization?: string; // Changed from primary_specialization_id
  created_at?: string;
  updated_at?: string;
}

interface Specialization {
  id: string;
  name: string;
  description: string;
}

interface ExpertSpecialization {
  id?: string;
  expert_id: string;
  specialization_id: string;
  experience_level: string;
  description?: string;
  is_primary: boolean;
}

// New interface for expert specialization with related specialization data
interface ExpertSpecializationWithSpecialization extends ExpertSpecialization {
  specializations?: Specialization;
}

export default function MyProfilePage() {
  const router = useRouter();
  const { t } = useTranslation();
 const { setTitle } = usePageTitle();
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false); // New state for edit mode
  
  // Expert profile data states
  const [expertProfile, setExpertProfile] = useState<ExpertProfile | null>(null);
  const [isExpertProfileLoading, setIsExpertProfileLoading] = useState(false);
  const [educationEntries, setEducationEntries] = useState<EducationEntry[]>([]); // New state for education
  const [workExperienceEntries, setWorkExperienceEntries] = useState<WorkExperienceEntry[]>([]); // New state for work experience

  // Consultation pricing states
  const [isAvailable, setIsAvailable] = useState(false);
  const [consultationPricing, setConsultationPricing] = useState<ConsultationPricing>({
    online_consultation_price: 0,
    online_consultation_duration: 7,
    onsite_consultation_price: 0,
    onsite_consultation_duration: 7
  });
  const [onsiteConsultationAvailable, setOnsiteConsultationAvailable] = useState(false);
  const [hasCar, setHasCar] = useState(false);

  useEffect(() => {
   setTitle("myProfile.title");
    async function fetchUserData() {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          router.push("/sign-in");
          return;
        }

        setUser(user);
        
        // Get user profile data
        const { data: profileData } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", user.id)
          .maybeSingle();

        if (profileData) {
          setProfile(profileData as UserProfile);
        }
        
        // Get user role from user metadata instead of querying user_roles
        const userMetaRole = user.user_metadata?.role;

        // If user is an expert, fetch expert-specific data
        if (userMetaRole === "expert") {
          setIsExpertProfileLoading(true);
          
          // Fetch expert profile
          const { data: expertData } = await supabase
            .from("expert_profiles")
            .select("*")
            .eq("id", user.id)
            .maybeSingle();
          
          if (expertData) {
            setExpertProfile(expertData as ExpertProfile);
            // Initialize education entries from fetched data - ensure it's always an array
            setEducationEntries(Array.isArray(expertData.education) ? expertData.education : []);
            // Initialize work experience entries from fetched data - ensure it's always an array
            setWorkExperienceEntries(Array.isArray(expertData.work_experience) ? expertData.work_experience : []);
            // Initialize consultation data
            setIsAvailable(expertData.is_available || false);
            setConsultationPricing(expertData.consultation_pricing || {
              online_consultation_price: 0,
              online_consultation_duration: 7,
              onsite_consultation_price: 0,
              onsite_consultation_duration: 7
            });
            setOnsiteConsultationAvailable(expertData.onsite_consultation_available || false);
            setHasCar(expertData.has_car || false);
            
            // Fetch expert specializations with related specialization data
            if (expertData.id) {
              const { data: specializationsData } = await supabase
                .from("expert_specializations")
                .select(`
                  *,
                  specializations(*)
                `)
                .eq("expert_id", expertData.id);
              
              if (specializationsData) {
                // Extract specializations and expert specializations
                const extractedSpecializations: Specialization[] = [];
                const extractedExpertSpecializations: ExpertSpecialization[] = [];
                
                specializationsData.forEach((item: ExpertSpecializationWithSpecialization) => {
                  if (item.specializations) {
                    if (!extractedSpecializations.some(spec => spec.id === item.specializations!.id)) {
                      extractedSpecializations.push(item.specializations);
                    }
                    extractedExpertSpecializations.push({
                      id: item.id,
                      expert_id: item.expert_id,
                      specialization_id: item.specialization_id,
                      experience_level: item.experience_level || '',
                      description: item.description,
                      is_primary: item.is_primary || false,
                    });
                  }
                });
              }
            }
          } else {
            // If no expert profile exists, create an empty one
            setExpertProfile({
              user_id: user.id,
              expertise_summary: user.user_metadata?.expertise || "",
              verification_status: "pending",
              education: [], // Initialize education as empty array
              work_experience: [], // Initialize work experience as empty array
              is_available: false,
              consultation_pricing: {
                online_consultation_price: 0,
                online_consultation_duration: 7,
                onsite_consultation_price: 0,
                onsite_consultation_duration: 7
              },
              onsite_consultation_available: false,
              has_car: false,
              service_areas: []
            });
          }
          
          setIsExpertProfileLoading(false);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error fetching user data:", error);
        setLoading(false);
        setIsExpertProfileLoading(false);
      }
    }

    fetchUserData();
  }, [router, setTitle, t]);

  const handleSave = async (formData: FormData) => {
    try {
      // Update expert profile
      const { error: expertError } = await supabase
        .from("expert_profiles")
        .upsert({
          user_id: user?.id,
          bio: formData.get("bio")?.toString(),
          education: educationEntries,
          work_experience: workExperienceEntries,
          is_available: isAvailable,
          consultation_pricing: consultationPricing,
          onsite_consultation_available: onsiteConsultationAvailable,
          has_car: hasCar,
          service_areas: formData.get("service_areas")?.toString().split(",").map(area => area.trim()),
          primary_specialization: formData.get("expertise")?.toString(),
          updated_at: new Date().toISOString(),
        }, { onConflict: "user_id" });
        
      if (expertError) {
        console.error("Error updating professional info:", expertError);
        return;
      }

      // Update user_metadata.expertise in profiles table
      const expertise = formData.get("expertise")?.toString();
      if (user && expertise !== undefined) {
        const { error: profileError } = await supabase
          .from("profiles")
          .update({
            user_metadata: { ...profile?.user_metadata, expertise: expertise },
          })
          .eq("id", user.id);

        if (profileError) {
          console.error("Error updating user expertise:", profileError);
          return;
        }
      }
      
      setIsEditing(false);
      window.location.reload(); // Reload data to show updated info
    } catch (error) {
      console.error("Error in form submission:", error);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Optionally, reload data to discard unsaved changes
    window.location.reload();
  };

  // Define Egyptian agricultural expertise options
  const expertiseOptions = [
    "Crop Production",
    "Livestock Management",
    "Irrigation Systems",
    "Soil Science",
    "Agricultural Economics",
    "Pest Management",
    "Horticulture",
    "Aquaculture",
    "Food Processing",
    "Sustainable Agriculture",
    "Agricultural Engineering",
    "Veterinary Science",
    "Rural Development",
    "Agribusiness Management",
    "Climate Change Adaptation in Agriculture",
  ];



  // Define Egyptian universities and colleges
  const egyptianUniversities = [
    "Cairo University",
    "Ain Shams University",
    "Alexandria University",
    "American University in Cairo (AUC)",
    "Helwan University",
    "Mansoura University",
    "Assiut University",
    "Zagazig University",
    "Tanta University",
    "Suez Canal University",
  ];

  const egyptianColleges: { [key: string]: string[] } = {
    "Cairo University": [
      "Faculty of Agriculture",
      "Faculty of Engineering",
      "Faculty of Science",
      "Faculty of Arts",
      "Faculty of Commerce",
      "Faculty of Law",
      "Faculty of Medicine",
      "Faculty of Pharmacy",
      "Faculty of Dentistry",
      "Faculty of Veterinary Medicine",
    ],
    "Ain Shams University": [
      "Faculty of Agriculture",
      "Faculty of Engineering",
      "Faculty of Science",
      "Faculty of Arts",
      "Faculty of Commerce",
      "Faculty of Law",
      "Faculty of Medicine",
      "Faculty of Pharmacy",
      "Faculty of Dentistry",
      "Faculty of Education",
    ],
    "Alexandria University": [
      "Faculty of Agriculture",
      "Faculty of Engineering",
      "Faculty of Science",
      "Faculty of Arts",
      "Faculty of Commerce",
      "Faculty of Law",
      "Faculty of Medicine",
      "Faculty of Pharmacy",
      "Faculty of Dentistry",
      "Faculty of Fine Arts",
    ],
    "American University in Cairo (AUC)": [
      "School of Sciences and Engineering",
      "School of Business",
      "School of Humanities and Social Sciences",
      "Graduate School of Education",
    ],
    "Helwan University": [
      "Faculty of Agriculture",
      "Faculty of Engineering",
      "Faculty of Science",
      "Faculty of Arts",
      "Faculty of Commerce and Business Administration",
    ],
    "Mansoura University": [
      "Faculty of Agriculture",
      "Faculty of Engineering",
      "Faculty of Science",
      "Faculty of Medicine",
      "Faculty of Pharmacy",
    ],
    "Assiut University": [
      "Faculty of Agriculture",
      "Faculty of Engineering",
      "Faculty of Science",
      "Faculty of Medicine",
      "Faculty of Pharmacy",
    ],
    "Zagazig University": [
      "Faculty of Agriculture",
      "Faculty of Engineering",
      "Faculty of Science",
      "Faculty of Veterinary Medicine",
    ],
    "Tanta University": [
      "Faculty of Agriculture",
      "Faculty of Engineering",
      "Faculty of Science",
      "Faculty of Medicine",
    ],
    "Suez Canal University": [
      "Faculty of Agriculture",
      "Faculty of Engineering",
      "Faculty of Science",
      "Faculty of Medicine",
    ],
  };

  // Removed unused MultiSelectExpertise component

  const addEducationEntry = () => {
    setEducationEntries((prevEntries) => [
      ...prevEntries,
      { id: uuidv4(), university: null, college: null, graduationYear: "", degree: "", grade: "" }, // Initialize with null for select values
    ]);
  };

  const updateEducationEntry = (id: string, field: keyof EducationEntry, value: string) => {
    setEducationEntries((prevEntries) =>
      prevEntries.map((entry) =>
        entry.id === id ? { ...entry, [field]: value } : entry
      )
    );
  };

  const removeEducationEntry = (id: string) => {
    setEducationEntries((prevEntries) => prevEntries.filter((entry) => entry.id !== id));
  };

  // Work Experience management functions
  const addWorkExperienceEntry = () => {
    setWorkExperienceEntries((prevEntries) => [
      ...prevEntries,
      {
        id: uuidv4(),
        company: "",
        position: "",
        startDate: "",
        endDate: "",
        isCurrent: false,
        description: ""
      } as WorkExperienceEntry,
    ]);
  };

  const updateWorkExperienceEntry = (id: string, field: keyof WorkExperienceEntry, value: string | boolean) => {
    setWorkExperienceEntries((prevEntries) =>
      prevEntries.map((entry) =>
        entry.id === id ? { ...entry, [field]: value } : entry
      )
    );
  };

  const removeWorkExperienceEntry = (id: string) => {
    setWorkExperienceEntries((prevEntries) => prevEntries.filter((entry) => entry.id !== id));
  };

  // Consultation pricing helper functions
  const updateConsultationPricing = (field: keyof ConsultationPricing, value: string | number | boolean) => {
    setConsultationPricing(prev => ({
      ...prev,
      [field]: value
    }));
  };



  const hasProfileInfo = expertProfile?.bio || profile?.user_metadata?.expertise || (expertProfile?.education && expertProfile.education.length > 0) || (expertProfile?.work_experience && expertProfile.work_experience.length > 0) || (expertProfile?.service_areas && expertProfile.service_areas.length > 0) || expertProfile?.is_available || expertProfile?.consultation_pricing;

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex h-96 items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            <div className="text-lg font-medium">Loading profile...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="container mx-auto">
            <div className="mb-8 flex justify-between items-center rtl:flex-row-reverse">
              <div className="text-start">
                <p className="text-muted-foreground">
                  {t("myProfile.description")}
                </p>
              </div>
              {!isEditing && (
                <Button onClick={() => setIsEditing(true)}>{t("myProfile.editProfile")}</Button>
              )}
            </div>

            {isExpertProfileLoading ? (
              <div className="flex justify-center p-4">
                <div className="animate-spin h-6 w-6 border-t-2 border-blue-500 rounded-full"></div>
              </div>
            ) : (
              <>
                {!hasProfileInfo && !isEditing ? (
                  <Card>
                    <CardContent className="p-6 text-center text-muted-foreground">
                      {t("myProfile.noProfile")}
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between rtl:flex-row-reverse">
                        <CardTitle>{t("myProfile.professionalInfo")}</CardTitle>
                        {!isEditing && (
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <div className={`w-3 h-3 rounded-full ${isAvailable ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <span className={`text-sm font-medium ${isAvailable ? 'text-green-700' : 'text-red-700'}`}>
                              {isAvailable ? t("myProfile.available") : t("myProfile.notAvailable")}
                            </span>
                          </div>
                        )}
                      </div>
                      <CardDescription>
                        {isEditing ? t("myProfile.editDescription") : t("myProfile.viewDescription")}
                      </CardDescription>
                    </CardHeader>
                    <form action={handleSave}>
                      <CardContent className="space-y-4">
                        {isEditing ? (
                          <>
                            {/* Professional Bio - moved to top */}
                            <div className="space-y-2">
                              <Label htmlFor="bio">{t("myProfile.professionalBio")}</Label>
                              <textarea
                                id="bio"
                                name="bio"
                                defaultValue={expertProfile?.bio || ""}
                                placeholder={t("myProfile.bioPlaceholder")}
                                className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2 text-sm text-start"
                              />
                            </div>

                            {/* Availability Toggle */}
                            <div className="space-y-2">
                              <div className="flex items-center justify-between rtl:flex-row-reverse">
                                <div className="text-start">
                                  <Label htmlFor="is_available" className="font-medium">
                                    {t("myProfile.availabilityTitle")}
                                  </Label>
                                  <p className="text-sm text-gray-600">
                                    {t("myProfile.availabilityDescription")}
                                  </p>
                                </div>
                                <button
                                  type="button"
                                  id="is_available"
                                  onClick={() => setIsAvailable(!isAvailable)}
                                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                                    isAvailable ? 'bg-blue-600' : 'bg-gray-200'
                                  }`}
                                >
                                  <span
                                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                      isAvailable ? 'translate-x-6' : 'translate-x-1'
                                    }`}
                                  />
                                </button>
                              </div>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="expertise">{t("myProfile.specialization")}</Label>
                              <select
                                id="expertise"
                                name="expertise"
                                defaultValue={profile?.user_metadata?.expertise || ""}
                                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-start"
                              >
                                {expertiseOptions.map((option) => (
                                  <option key={option} value={option}>
                                    {t(`myProfile.expertiseOptions.${option.toLowerCase().replace(/ /g, "_")}`)}
                                  </option>
                                ))}
                              </select>
                            </div>

                            {/* Divider */}
                            <div className="border-t border-gray-200 my-6"></div>

                            {/* Education Section */}
                            <div className="space-y-4">
                              <div className="space-y-4">
                                <h3 className="font-semibold leading-none tracking-tight text-start">{t("myProfile.education")}</h3>

                                {Array.isArray(educationEntries) && educationEntries.map((entry) => (
                                  <div key={entry.id} className="space-y-4 p-4 border rounded-lg bg-white">
                                    <div className="flex justify-end rtl:justify-start">
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeEducationEntry(entry.id)}
                                        className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
                                      >
                                        ✕
                                      </Button>
                                    </div>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-start">
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.university")}</Label>
                                      <select
                                        value={entry.university || ""}
                                        onChange={(e) => updateEducationEntry(entry.id, "university", e.target.value)}
                                        className="w-full rounded-md border bg-background px-3 py-2 text-sm"
                                      >
                                        <option value="">{t("myProfile.selectUniversity")}</option>
                                        {egyptianUniversities.map((uni) => (
                                          <option key={uni} value={uni}>
                                            {uni}
                                          </option>
                                        ))}
                                      </select>
                                    </div>
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.college")}</Label>
                                      <select
                                        value={entry.college || ""}
                                        onChange={(e) => updateEducationEntry(entry.id, "college", e.target.value)}
                                        className="w-full rounded-md border bg-background px-3 py-2 text-sm"
                                        disabled={!entry.university}
                                      >
                                        <option value="">{t("myProfile.selectCollege")}</option>
                                        {entry.university &&
                                          egyptianColleges[entry.university]?.map((college) => (
                                            <option key={college} value={college}>
                                              {college}
                                            </option>
                                          ))}
                                      </select>
                                    </div>
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.degree")}</Label>
                                      <Input
                                        value={entry.degree || ""}
                                        onChange={(e) => updateEducationEntry(entry.id, "degree", e.target.value)}
                                        placeholder={t("myProfile.degreePlaceholder")}
                                      />
                                    </div>
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.graduationYear")}</Label>
                                      <Input
                                        type="number"
                                        value={entry.graduationYear || ""}
                                        onChange={(e) => updateEducationEntry(entry.id, "graduationYear", e.target.value)}
                                        placeholder={t("myProfile.yearPlaceholder")}
                                        min="1950"
                                        max={new Date().getFullYear()}
                                      />
                                    </div>
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.grade")}</Label>
                                      <Input
                                        value={entry.grade || ""}
                                        onChange={(e) => updateEducationEntry(entry.id, "grade", e.target.value)}
                                        placeholder={t("myProfile.gradePlaceholder")}
                                      />
                                    </div>
                                  </div>
                                </div>
                              ))}

                                {/* Add Education Button - Centered */}
                                <div className="flex justify-center pt-4">
                                  <Button
                                    type="button"
                                    variant="outline"
                                    onClick={addEducationEntry}
                                    className="flex items-center gap-2"
                                  >
                                    <span className="text-lg">+</span>
                                    {t("myProfile.addEducation")}
                                  </Button>
                                </div>
                              </div>
                            </div>

                            {/* Divider */}
                            <div className="border-t border-gray-200 my-6"></div>

                            {/* Work Experience Section */}
                            <div className="space-y-4">
                              <div className="space-y-4">
                                <h3 className="font-semibold leading-none tracking-tight text-start">{t("myProfile.workExperience")}</h3>

                                {workExperienceEntries.map((entry) => (
                                  <div key={entry.id} className="space-y-4 p-4 border rounded-lg bg-white">
                                    <div className="flex justify-end rtl:justify-start">
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeWorkExperienceEntry(entry.id)}
                                        className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
                                      >
                                        ✕
                                      </Button>
                                    </div>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-start">
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.company")}</Label>
                                      <Input
                                        value={entry.company || ""}
                                        onChange={(e) => updateWorkExperienceEntry(entry.id, "company", e.target.value)}
                                        placeholder={t("myProfile.companyPlaceholder")}
                                      />
                                    </div>
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.position")}</Label>
                                      <Input
                                        value={entry.position || ""}
                                        onChange={(e) => updateWorkExperienceEntry(entry.id, "position", e.target.value)}
                                        placeholder={t("myProfile.positionPlaceholder")}
                                      />
                                    </div>
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.startDate")}</Label>
                                      <Input
                                        type="month"
                                        value={entry.startDate || ""}
                                        onChange={(e) => updateWorkExperienceEntry(entry.id, "startDate", e.target.value)}
                                      />
                                    </div>
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.endDate")}</Label>
                                      <Input
                                        type="month"
                                        value={entry.endDate || ""}
                                        onChange={(e) => updateWorkExperienceEntry(entry.id, "endDate", e.target.value)}
                                        disabled={entry.isCurrent}
                                        placeholder={entry.isCurrent ? t("myProfile.current") : ""}
                                      />
                                    </div>
                                    <div className="space-y-2 md:col-span-2">
                                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                        <input
                                          type="checkbox"
                                          id={`current-${entry.id}`}
                                          checked={entry.isCurrent}
                                          onChange={(e) => updateWorkExperienceEntry(entry.id, "isCurrent", e.target.checked)}
                                          className="rounded border-gray-300"
                                        />
                                        <Label htmlFor={`current-${entry.id}`}>{t("myProfile.currentlyWorking")}</Label>
                                      </div>
                                    </div>
                                    <div className="space-y-2 md:col-span-2">
                                      <Label>{t("myProfile.description")}</Label>
                                      <textarea
                                        value={entry.description || ""}
                                        onChange={(e) => updateWorkExperienceEntry(entry.id, "description", e.target.value)}
                                        placeholder={t("myProfile.workDescriptionPlaceholder")}
                                        className="w-full min-h-[80px] rounded-md border border-input bg-background px-3 py-2 text-sm"
                                      />
                                    </div>
                                  </div>
                                </div>
                              ))}

                                {/* Add Work Experience Button - Centered */}
                                <div className="flex justify-center pt-4">
                                  <Button
                                    type="button"
                                    variant="outline"
                                    onClick={addWorkExperienceEntry}
                                    className="flex items-center gap-2"
                                  >
                                    <span className="text-lg">+</span>
                                    {t("myProfile.addWorkExperience")}
                                  </Button>
                                </div>
                              </div>
                            </div>

                            {/* Divider */}
                            <div className="border-t border-gray-200 my-6"></div>

                            {/* Consultation Pricing Section */}
                            <div className="space-y-4 text-start">
                              <h3 className="font-semibold leading-none tracking-tight">{t("myProfile.pricingTitle")}</h3>

                              {/* Price Section */}
                              <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-2">
                                    <Label>{t("myProfile.pricePerSession")}</Label>
                                    <Input
                                      type="number"
                                      value={consultationPricing.online_consultation_price}
                                      onChange={(e) => updateConsultationPricing('online_consultation_price', parseInt(e.target.value) || 0)}
                                      placeholder="0"
                                      min="0"
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <Label>{t("myProfile.supportDuration")}</Label>
                                    <select
                                      value={consultationPricing.online_consultation_duration}
                                      onChange={(e) => updateConsultationPricing('online_consultation_duration', parseInt(e.target.value))}
                                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                                    >
                                      <option value={1}>{t("myProfile.oneDay")}</option>
                                      <option value={3}>{t("myProfile.threeDays")}</option>
                                      <option value={7}>{t("myProfile.sevenDays")}</option>
                                      <option value={14}>{t("myProfile.fourteenDays")}</option>
                                      <option value={30}>{t("myProfile.thirtyDays")}</option>
                                    </select>
                                  </div>
                                </div>
                              </div>



                              {/* Service Areas with Onsite Consultation */}
                              <div className="space-y-3">
                                <div className="flex items-center justify-between rtl:flex-row-reverse">
                                  <Label htmlFor="service_areas">{t("myProfile.serviceAreas")}</Label>
                                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                    <input
                                      type="checkbox"
                                      id="onsite_available"
                                      checked={onsiteConsultationAvailable}
                                      onChange={(e) => setOnsiteConsultationAvailable(e.target.checked)}
                                      className="rounded border-gray-300"
                                    />
                                    <Label htmlFor="onsite_available" className="text-sm">
                                      {t("myProfile.offerOnsite")}
                                    </Label>
                                  </div>
                                </div>
                                <Input
                                  id="service_areas"
                                  name="service_areas"
                                  defaultValue={expertProfile?.service_areas?.join(", ") || ""}
                                  placeholder={t("myProfile.serviceAreasPlaceholder")}
                                  disabled={!onsiteConsultationAvailable}
                                />
                                <p className="text-sm text-gray-600">
                                  {onsiteConsultationAvailable
                                    ? t("myProfile.serviceAreasDescription")
                                    : t("myProfile.enableOnsiteDescription")
                                  }
                                </p>

                                {/* Onsite Consultation Details */}
                                {onsiteConsultationAvailable && (
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.onsitePrice")}</Label>
                                      <Input
                                        type="number"
                                        value={consultationPricing.onsite_consultation_price}
                                        onChange={(e) => updateConsultationPricing('onsite_consultation_price', parseInt(e.target.value) || 0)}
                                        placeholder="0"
                                        min="0"
                                      />
                                    </div>
                                    <div className="space-y-2">
                                      <Label>{t("myProfile.onsiteDuration")}</Label>
                                      <select
                                        value={consultationPricing.onsite_consultation_duration}
                                        onChange={(e) => updateConsultationPricing('onsite_consultation_duration', parseInt(e.target.value))}
                                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                                      >
                                        <option value={1}>{t("myProfile.oneDay")}</option>
                                        <option value={3}>{t("myProfile.threeDays")}</option>
                                        <option value={7}>{t("myProfile.sevenDays")}</option>
                                        <option value={14}>{t("myProfile.fourteenDays")}</option>
                                        <option value={30}>{t("myProfile.thirtyDays")}</option>
                                      </select>
                                    </div>
                                    <div className="flex items-center space-x-2 md:col-span-2 rtl:space-x-reverse">
                                      <input
                                        type="checkbox"
                                        id="has_car"
                                        checked={hasCar}
                                        onChange={(e) => setHasCar(e.target.checked)}
                                        className="rounded border-gray-300"
                                      />
                                      <Label htmlFor="has_car">
                                        {t("myProfile.ownTransportation")}
                                      </Label>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </>
                        ) : (
                          <>
                            {/* View Mode */}
                            <div className="space-y-4">
                              {/* Professional Bio - moved to top */}
                              <div className="space-y-3">
                                <h4 className="text-base font-medium text-gray-800 text-start">{t("myProfile.professionalBio")}</h4>
                                <p className="text-gray-600 leading-relaxed whitespace-pre-wrap text-start">{expertProfile?.bio || t("myProfile.addBio")}</p>
                              </div>

                              {/* Divider */}
                              <div className="border-t border-gray-200 my-8"></div>

                              <div className="space-y-3 text-start">
                                <h4 className="text-base font-medium text-gray-800">{t("myProfile.specialization")}</h4>
                                <p className="text-gray-600 leading-relaxed">{profile?.user_metadata?.expertise || t("myProfile.addSpecialization")}</p>
                              </div>

                              {/* Divider */}
                              <div className="border-t border-gray-200 my-8"></div>

                              <div className="space-y-3 text-start">
                                <h4 className="text-base font-medium text-gray-800">{t("myProfile.education")}</h4>
                                {educationEntries.length > 0 ? (
                                  <div className="space-y-4">
                                    {Array.isArray(educationEntries) && educationEntries.map((entry) => (
                                      <div key={entry.id} className="p-5 border rounded-lg bg-white shadow-sm">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                          <div>
                                            <p className="font-medium text-gray-900 text-sm">{entry.university}</p>
                                            <p className="text-gray-600 mt-1 text-xs">{entry.college}</p>
                                          </div>
                                          <div className="text-end">
                                            <p className="font-medium text-gray-900 text-sm">{entry.degree}</p>
                                            <p className="text-gray-600 mt-1 text-xs">
                                              {entry.graduationYear} - {entry.grade}
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <p className="text-gray-600 leading-relaxed">{t("myProfile.addEducation")}</p>
                                )}
                              </div>

                              {/* Divider */}
                              <div className="border-t border-gray-200 my-8"></div>

                              {/* Work Experience Display */}
                              <div className="space-y-3 text-start">
                                <h4 className="text-base font-medium text-gray-800">{t("myProfile.workExperience")}</h4>
                                {workExperienceEntries.length > 0 ? (
                                  <div className="space-y-4">
                                    {workExperienceEntries.map((entry) => (
                                      <div key={entry.id} className="p-5 border rounded-lg bg-white shadow-sm">
                                        <div className="space-y-3">
                                          <div className="flex justify-between items-start rtl:flex-row-reverse">
                                            <div>
                                              <h4 className="font-medium text-gray-900 text-sm">{entry.position}</h4>
                                              <p className="text-gray-600 mt-1 text-xs">{entry.company}</p>
                                            </div>
                                            <div className="text-xs text-gray-500 font-medium">
                                              {entry.startDate} - {entry.isCurrent ? t("myProfile.present") : entry.endDate}
                                            </div>
                                          </div>
                                          {entry.description && (
                                            <p className="text-gray-600 leading-relaxed whitespace-pre-wrap text-xs">{entry.description}</p>
                                          )}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <p className="text-gray-600 leading-relaxed">{t("myProfile.addWorkExperience")}</p>
                                )}
                              </div>

                              {/* Divider */}
                              <div className="border-t border-gray-200 my-8"></div>

                              {/* Consultation Services Display */}
                              <div className="space-y-3 text-start">
                                <h4 className="text-base font-medium text-gray-800">{t("myProfile.pricingTitle")}</h4>

                                {isAvailable ? (
                                  <div className="space-y-6">
                                    {/* Price Section */}
                                    <div className="space-y-4">
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-1">
                                          <span className="text-gray-600 text-xs">{t("myProfile.pricePerSession")}</span>
                                          <p className="text-gray-900 font-medium text-sm">{consultationPricing.online_consultation_price} {t("myProfile.currency")}</p>
                                        </div>
                                        <div className="space-y-1">
                                          <span className="text-gray-600 text-xs">{t("myProfile.supportDuration")}</span>
                                          <p className="text-gray-900 font-medium text-sm">{consultationPricing.online_consultation_duration} {consultationPricing.online_consultation_duration === 1 ? t("myProfile.day") : t("myProfile.days")}</p>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Onsite Consultation */}
                                    {onsiteConsultationAvailable && (
                                      <div className="space-y-4">
                                        <h5 className="text-sm font-medium text-gray-700">{t("myProfile.onsiteConsultation")}</h5>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                          <div className="space-y-1">
                                            <span className="text-gray-600 text-xs">{t("myProfile.onsitePrice")}</span>
                                            <p className="text-gray-900 font-medium text-sm">{consultationPricing.onsite_consultation_price} {t("myProfile.currency")}</p>
                                          </div>
                                          <div className="space-y-1">
                                            <span className="text-gray-600 text-xs">{t("myProfile.onsiteDuration")}</span>
                                            <p className="text-gray-900 font-medium text-sm">{consultationPricing.onsite_consultation_duration} {consultationPricing.onsite_consultation_duration === 1 ? t("myProfile.day") : t("myProfile.days")}</p>
                                          </div>
                                        </div>
                                        {hasCar && (
                                          <div className="text-green-600 font-medium text-xs">
                                            ✓ {t("myProfile.hasTransportation")}
                                          </div>
                                        )}
                                      </div>
                                    )}



                                    {/* Service Areas */}
                                    <div className="space-y-3">
                                      <h5 className="text-sm font-medium text-gray-700">{t("myProfile.serviceAreas")}</h5>
                                      <p className="text-gray-600 leading-relaxed">
                                        {onsiteConsultationAvailable
                                          ? (expertProfile?.service_areas?.join(", ") || t("myProfile.addServiceAreas"))
                                          : t("myProfile.onsiteNotAvailable")
                                        }
                                      </p>
                                    </div>
                                  </div>
                                ) : (
                                  <p className="text-gray-600 leading-relaxed">{t("myProfile.addConsultationServices")}</p>
                                )}
                              </div>
                            </div>
                          </>
                        )}
                      </CardContent>
                      <CardFooter className="flex justify-end gap-2 rtl:flex-row-reverse">
                        {isEditing && (
                          <>
                            <Button type="button" variant="outline" onClick={handleCancel}>
                              {t("myProfile.cancel")}
                            </Button>
                            <Button type="submit">{t("myProfile.saveChanges")}</Button>
                          </>
                        )}
                      </CardFooter>
                    </form>
                  </Card>
                )}
              </>
            )}
          </div>
    </div>
  );
}
