"use client";

import { useTranslation } from "@/components/i18n-provider";
import { resetPasswordAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import Navbar from "@/components/navbar";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function ResetPassword(props: {
  searchParams: Message;
}) {
  const { t } = useTranslation();
  const searchParams = props.searchParams;
  if ("message" in searchParams) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={searchParams} />
      </div>
    );
  }

  return (
    <>
      <Navbar />
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
          <form className="flex flex-col space-y-6 text-start">
            <div className="space-y-2 text-center">
              <h1 className="text-3xl font-semibold tracking-tight">{t("resetPassword.title")}</h1>
              <p className="text-sm text-muted-foreground">
                {t("resetPassword.description")}
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium">
                  {t("resetPassword.newPassword")}
                </Label>
                <Input
                  id="password"
                  type="password"
                  name="password"
                  placeholder={t("resetPassword.newPasswordPlaceholder")}
                  required
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-sm font-medium">
                  {t("resetPassword.confirmPassword")}
                </Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  name="confirmPassword"
                  placeholder={t("resetPassword.confirmPasswordPlaceholder")}
                  required
                  className="w-full"
                />
              </div>
            </div>

            <SubmitButton
              formAction={async (formData: FormData) => {
                await resetPasswordAction(formData);
              }}
              pendingText={t("resetPassword.pendingText")}
              className="w-full"
            >
              {t("resetPassword.submitButton")}
            </SubmitButton>

            <FormMessage message={searchParams} />
          </form>
        </div>
      </div>
    </>
  );
}
