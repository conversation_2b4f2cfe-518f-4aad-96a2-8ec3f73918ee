"use client";

import { useTranslation } from "@/components/i18n-provider";
import { usePageTitle } from "@/components/page-title-provider";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { supabase } from "@/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Eye, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import Pagination from "@/app/dashboard/pagination";
import RegistrationDetailsModal from "@/components/registration-details-modal";
import RejectionModal from "@/components/rejection-modal";
import { useToast } from "@/components/ui/use-toast";

interface RegistrationRequest {
  id: string;
  user_id: string;
  entity_type: 'FARM' | 'FACTORY';
  registration_data: any;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'RESUBMITTED';
  submitted_at: string;
  reviewed_at?: string;
  reviewed_by?: string;
  admin_notes?: string;
  rejection_reason?: string;
  created_at: string;
  updated_at: string;
  user_email: string;
  user_first_name?: string;
  user_last_name?: string;
  user_phone_number?: string;
  asset_name: string;
  asset_location: string;
  total_count: number;
}

export default function ManagementPage() {
  const { t, locale } = useTranslation();
  const { setTitle } = usePageTitle();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const searchTerm = searchParams.get("q") || "";
  const page = Number(searchParams.get("page")) || 1;
  const typeFilter = searchParams.get("type") || "";
  const statusFilter = searchParams.get("status") || "";
  const itemsPerPage = 10;
  
  const [registrationRequests, setRegistrationRequests] = useState<RegistrationRequest[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRequestId, setSelectedRequestId] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRejectionModalOpen, setIsRejectionModalOpen] = useState(false);
  const [requestToReject, setRequestToReject] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();
  const [searchInput, setSearchInput] = useState(searchTerm);

  useEffect(() => {
    setTitle("managementPage.title");
    fetchRegistrationRequests();
  }, [searchTerm, page, typeFilter, statusFilter, setTitle]);

  const fetchRegistrationRequests = async () => {
    setIsLoading(true);
    try {
      // Call the database function we created
      const { data, error } = await supabase.rpc('get_registration_requests', {
        p_limit: itemsPerPage,
        p_offset: (page - 1) * itemsPerPage,
        p_entity_type: typeFilter || null,
        p_status: statusFilter || null,
        p_search_term: searchTerm || null
      });

      if (error) {
        console.error("Error fetching registration requests:", error);
        return;
      }

      if (data && data.length > 0) {
        setRegistrationRequests(data);
        setTotalCount(data[0].total_count || 0);
      } else {
        setRegistrationRequests([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error calling get_registration_requests:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale === 'ar' ? "ar-EG" : "en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      weekday: "short"
    });
  };



  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'PENDING':
      case 'RESUBMITTED':
        return 'secondary';
      case 'APPROVED':
        return 'default';
      case 'REJECTED':
        return 'destructive';
      default:
        return 'secondary';
    }
  };



  const handleViewDetails = (requestId: string) => {
    console.log("Opening modal for request ID:", requestId);
    setSelectedRequestId(requestId);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedRequestId(null);
  };

  const handleApprove = async (requestId: string) => {
    setIsProcessing(true);
    try {
      // Get current user ID for audit logging
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast({
          title: "Error",
          description: "You must be logged in to perform this action",
          variant: "destructive",
        });
        return;
      }

      // Call the existing approve_registration_request function
      const { error } = await supabase.rpc('execute_approve_registration', {
        p_request_id: requestId,
        p_admin_user_id: user.id,
        p_admin_notes: 'Approved via admin dashboard'
      });

      if (error) {
        console.error("Error approving request:", error);
        toast({
          title: "Error",
          description: "Failed to approve registration request. Please try again.",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Success",
        description: "Registration request approved successfully. Asset has been created.",
        variant: "default",
      });

      // Refresh the list
      fetchRegistrationRequests();
      handleCloseModal();
    } catch (error) {
      console.error("Error approving request:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async (requestId: string) => {
    setRequestToReject(requestId);
    setIsRejectionModalOpen(true);
  };

  const handleConfirmReject = async (rejectionReason: string, adminNotes?: string) => {
    if (!requestToReject) return;

    setIsProcessing(true);
    try {
      // Get current user ID for audit logging
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast({
          title: "Error",
          description: "You must be logged in to perform this action",
          variant: "destructive",
        });
        return;
      }

      // Call the reject_registration_request function we created
      const { error } = await supabase.rpc('reject_registration_request', {
        p_request_id: requestToReject,
        p_performed_by: user.id,
        p_rejection_reason: rejectionReason,
        p_admin_notes: adminNotes
      });

      if (error) {
        console.error("Error rejecting request:", error);
        toast({
          title: "Error",
          description: "Failed to reject registration request. Please try again.",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Success",
        description: "Registration request rejected successfully. User has been notified.",
        variant: "default",
      });

      // Refresh the list
      fetchRegistrationRequests();
      setIsRejectionModalOpen(false);
      setRequestToReject(null);
      handleCloseModal();
    } catch (error) {
      console.error("Error rejecting request:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCloseRejectionModal = () => {
    setIsRejectionModalOpen(false);
    setRequestToReject(null);
  };

  return (
    <div className="p-6">
      {/* Asset Registration Management Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between text-left rtl:text-right mb-0">
            <div className="flex-1">
              <CardTitle className="text-2xl font-bold rtl:text-right ltr:text-left">
                {t("managementPage.cardTitle")}
              </CardTitle>
              <CardDescription className="rtl:text-right ltr:text-left">
                {t("managementPage.cardDescription")}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse shrink-0">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={t("managementPage.searchPlaceholder")}
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      const params = new URLSearchParams(searchParams);
                      if (searchInput) {
                        params.set('q', searchInput);
                      } else {
                        params.delete('q');
                      }
                      params.set('page', '1');
                      router.push(`/dashboard/management?${params.toString()}`);
                    }
                  }}
                  className="pl-10 w-64"
                />
              </div>
              <Select value={typeFilter} onValueChange={(value) => {
                const params = new URLSearchParams(searchParams);
                if (value && value !== 'all') {
                  params.set('type', value);
                } else {
                  params.delete('type');
                }
                params.set('page', '1');
                router.push(`/dashboard/management?${params.toString()}`);
              }}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder={t("managementPage.filterByType")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("managementPage.allTypes")}</SelectItem>
                  <SelectItem value="FARM">{t("managementPage.farm")}</SelectItem>
                  <SelectItem value="FACTORY">{t("managementPage.factory")}</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={(value) => {
                const params = new URLSearchParams(searchParams);
                if (value && value !== 'all') {
                  params.set('status', value);
                } else {
                  params.delete('status');
                }
                params.set('page', '1');
                router.push(`/dashboard/management?${params.toString()}`);
              }}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder={t("managementPage.filterByStatus")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("managementPage.allStatuses")}</SelectItem>
                  <SelectItem value="PENDING">{t("managementPage.pending")}</SelectItem>
                  <SelectItem value="APPROVED">{t("managementPage.approved")}</SelectItem>
                  <SelectItem value="REJECTED">{t("managementPage.rejected")}</SelectItem>
                  <SelectItem value="RESUBMITTED">{t("managementPage.resubmitted")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
              <div className="text-muted-foreground">{t("managementPage.loading")}</div>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-start">{t("managementPage.userColumn")}</TableHead>
                    <TableHead className="text-start">{t("managementPage.assetColumn")}</TableHead>
                    <TableHead className="text-start">{t("managementPage.typeColumn")}</TableHead>
                    <TableHead className="text-start">{t("managementPage.statusColumn")}</TableHead>
                    <TableHead className="text-start">{t("managementPage.submittedColumn")}</TableHead>
                    <TableHead className="text-start">{t("managementPage.actionsColumn")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {registrationRequests.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-12">
                        <div className="text-muted-foreground">
                          <div className="text-4xl mb-2">📋</div>
                          <div className="font-medium">
                            {t("managementPage.noRequestsTitle")}
                          </div>
                          <div className="text-sm">
                            {t("managementPage.noRequestsDescription")}
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    registrationRequests.map((request: RegistrationRequest) => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <div className="font-medium">
                            {request.user_first_name && request.user_last_name
                              ? `${request.user_first_name} ${request.user_last_name}`
                              : request.user_email}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{request.asset_name}</div>
                        </TableCell>
                        <TableCell>
                          <span>{t(`managementPage.${request.entity_type.toLowerCase()}`)}</span>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(request.status)}>
                            {t(`managementPage.${request.status.toLowerCase()}`)}
                          </Badge>
                        </TableCell>
                        <TableCell className="min-w-[200px]">
                          <div className="text-sm">
                            {formatDate(request.submitted_at)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2 rtl:space-x-reverse">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleViewDetails(request.id)}
                            >
                              <Eye className="h-4 w-4 rtl:ml-1 ltr:mr-1" />
                              {t("managementPage.viewDetailsAction")}
                            </Button>
                            {request.status === 'PENDING' && (
                              <>
                                <Button 
                                  variant="default" 
                                  size="sm"
                                  onClick={() => handleApprove(request.id)}
                                  disabled={isProcessing}
                                >
                                  {isProcessing ? "Processing..." : t("managementPage.approveAction")}
                                </Button>
                                <Button 
                                  variant="destructive" 
                                  size="sm"
                                  onClick={() => handleReject(request.id)}
                                  disabled={isProcessing}
                                >
                                  {isProcessing ? "Processing..." : t("managementPage.rejectAction")}
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
      <Pagination totalPages={Math.ceil(totalCount / itemsPerPage)} />
      
      {/* Registration Details Modal */}
      <RegistrationDetailsModal
        requestId={selectedRequestId}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onApprove={handleApprove}
        onReject={handleReject}
      />

      {/* Rejection Modal */}
      <RejectionModal
        isOpen={isRejectionModalOpen}
        onClose={handleCloseRejectionModal}
        onConfirm={handleConfirmReject}
        isLoading={isProcessing}
      />
    </div>
  );
}
