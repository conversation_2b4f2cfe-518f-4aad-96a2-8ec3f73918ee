import { createClient } from "@/supabase/server";
import { redirect } from "next/navigation";
import { getUserRole } from "@/utils/get-user-role";
import { Suspense } from "react";
import { User } from "@supabase/supabase-js";

// Import the views with dynamic loading for better performance
import dynamic from "next/dynamic";
const DashboardClientPage = dynamic(() => import("./dashboard-client-page"), {
  ssr: false,
  loading: () => (
    <div className="h-screen w-full flex items-center justify-center">
      <div className="h-32 w-32 rounded-lg bg-muted animate-pulse" />
    </div>
  ),
});

// Define the profile type for better type safety
interface Profile {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  avatar_url?: string;
  location?: string;
  expertise?: string;
  phone?: string;
  created_at?: string;
  updated_at?: string;
}

// Admin View with loading state
const AdminView = dynamic(
  () => import("@/components/dashboard/views/admin-view"),
  {
    loading: () => (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-24 rounded-lg bg-card animate-pulse" />
          ))}
        </div>
        <div className="h-[300px] w-full rounded-md bg-muted animate-pulse" />
      </div>
    ),
  }
);

// Expert View with loading state
const ExpertView = dynamic(
  () => import("@/components/dashboard/views/expert-view"),
  {
    loading: () => (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="h-64 rounded-lg bg-card animate-pulse" />
          ))}
        </div>
        <div className="h-64 w-full rounded-lg bg-muted animate-pulse" />
      </div>
    ),
  }
);

// Function to fetch user profile
async function getUserProfile(user: User) {
  const supabase = createClient();
  const { data: profile, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", user.id)
    .maybeSingle();

  if (error) {
    console.error("Error fetching user profile:", error.message);
    // Return a minimal profile if there's an error
    return {
      id: user.id,
      email: user.email,
      first_name: user.email?.split('@')[0] || 'User',
    } as Profile;
  }

  return profile as Profile;
}

export default async function Dashboard() {
  const supabase = createClient();
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  // Redirect to sign-in if not authenticated
  if (authError || !user) {
    return redirect("/sign-in");
  }

  try {
    // Fetch user role and profile in parallel
    const [userRole, profile] = await Promise.all([
      getUserRole(user),
      getUserProfile(user)
    ]);

    // Log the role for debugging
    console.log(`User role: ${userRole}`);

    return (
      <DashboardClientPage>
        <div className="p-6">
          <Suspense fallback={<div className="h-64 w-full bg-muted animate-pulse rounded-lg" />}>
            {userRole === "admin" ? (
              <AdminView profile={profile} />
            ) : (
              <ExpertView profile={profile} />
            )}
          </Suspense>
        </div>
      </DashboardClientPage>
    );
  } catch (error) {
    console.error("Error in dashboard page:", error);
    // Show error state
    return (
      <div className="p-6">
        <div className="container mx-auto">
          <div className="rounded-lg border border-destructive bg-destructive/10 p-6 text-destructive">
            <h2 className="text-xl font-semibold mb-2">Error Loading Dashboard</h2>
            <p>There was an error loading your dashboard. Please try refreshing the page or contact support if the issue persists.</p>
          </div>
        </div>
      </div>
    );
  }
}
