"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "@/components/i18n-provider";
import { usePageTitle } from "@/components/page-title-provider";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Search,
  MapPin,
  Clock,
  DollarSign,
  MessageSquare,
  CheckCircle,
  XCircle,
  Briefcase,
} from "lucide-react";
import { VerificationOverlay } from "@/components/verification-overlay";

interface ConsultationRequest {
  id: string;
  farmer: {
    id: string;
    name: string;
    email: string;
    phone: string;
    location: string;
    farmSize: string;
  };
  request: {
    title: string;
    description: string;
    category: string;
    preferredDate: string;
    consultationType: "online" | "onsite";
    budget: number;
    duration: number;
  };
  createdAt: string;
  status: "pending" | "accepted" | "declined";
}

export default function RequestsPage() {
  const { t } = useTranslation();
  const { setTitle } = usePageTitle();
  const router = useRouter();
  const [requests, setRequests] = useState<ConsultationRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterUrgency, ] = useState<string>("all");

  useEffect(() => {
   setTitle("sidebar.requests");
    // Simulate loading requests data
    setTimeout(() => {
      setRequests(dummyRequests);
      setLoading(false);
    }, 1000);
  }, [setTitle, t]);

  const handleAcceptRequest = (requestId: string) => {
    setRequests(prev => 
      prev.map(req => 
        req.id === requestId ? { ...req, status: "accepted" } : req
      )
    );
  };

  const handleDeclineRequest = (requestId: string) => {
    setRequests(prev => 
      prev.map(req => 
        req.id === requestId ? { ...req, status: "declined" } : req
      )
    );
  };

  const filteredRequests = requests.filter(request => {
    const matchesSearch = request.farmer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.request.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === "all" || request.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "accepted": return "bg-green-100 text-green-800 border-green-200";
      case "declined": return "bg-red-100 text-red-800 border-red-200";
      case "pending": return "bg-yellow-100 text-yellow-700 border-yellow-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-8">
          <p className="text-muted-foreground">{t("requestsPage.description")}</p>
        </div>
        
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="flex-1 space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                    <div className="h-8 bg-gray-200 rounded"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <VerificationOverlay>
      <div className="p-6 rtl:text-right">
      {/* Filters and Search */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t("requestsPage.searchPlaceholder")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rtl:pl-3 rtl:pr-10"
          />
        </div>
        <div className="flex space-x-2 rtl:space-x-reverse w-40">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="rounded-md border border-input bg-background px-3 py-2 text-sm text-left rtl:text-right"
          >
            <option value="all">{t("requestsPage.allStatus")}</option>
            <option value="pending">{t("requestsPage.pendingStatus")}</option>
            <option value="accepted">{t("requestsPage.acceptedStatus")}</option>
            <option value="declined">{t("requestsPage.declinedStatus")}</option>
          </select>
        </div>
      </div>

      {/* Requests List */}
      <div className="space-y-4">
        {filteredRequests.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">{t("requestsPage.noRequestsTitle")}</h3>
              <p className="text-gray-500">
                {searchTerm || filterStatus !== "all" || filterUrgency !== "all" ? t("requestsPage.noRequestsFilterDescription")
              : t("requestsPage.noRequestsDescription")}
          </p>
        </CardContent>
      </Card>
    ) : (
          filteredRequests.map((request) => (
            <Card key={request.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-3 rtl:flex-row-reverse">
                      <div className="rtl:text-right">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {request.request.title}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 rtl:space-x-reverse">
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <MapPin className="h-4 w-4 ltr:mr-2 rtl:ml-2" />
                            <span>{request.farmer.location}</span>
                          </div>
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Briefcase className="h-4 w-4 ltr:mr-2 rtl:ml-2" />
                            <span>{request.farmer.farmSize}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse rtl:order-first">
                        <Badge variant="outline" className={getStatusColor(request.status)}>
                          {t(`consultationStatus.${request.status}`)}
                        </Badge>
                      </div>
                    </div>

                    <p className="text-gray-700 mb-4 line-clamp-2">
                      {request.request.description}
                    </p>

                    {/* Request Metadata */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <DollarSign className="h-4 w-4 text-gray-400 ltr:mr-2 rtl:ml-2" />
                        <div>
                          <p className="text-gray-500">{t("requestsPage.budget")}</p>
                          <p className="font-medium">{request.request.budget} {t("requestsPage.currency")}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Clock className="h-4 w-4 text-gray-400 ltr:mr-2 rtl:ml-2" />
                        <div>
                          <p className="text-gray-500">{t("requestsPage.duration")}</p>
                          <p className="font-medium">{request.request.duration} {t("requestsPage.days")}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <MessageSquare className="h-4 w-4 text-gray-400 ltr:mr-2 rtl:ml-2" />
                        <div>
                          <p className="text-gray-500">{t("requestsPage.type")}</p>
                          <p className="font-medium capitalize">{request.request.consultationType}</p>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center ltr:justify-end rtl:justify-start">
                      {request.status === "pending" && (
                        <div className="flex space-x-2 rtl:space-x-reverse ltr:justify-end rtl:justify-start">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeclineRequest(request.id)}
                            className="border-red-200 text-red-700 hover:bg-red-50"
                          >
                            <XCircle className="h-4 w-4 me-1" />
                            {t("requestsPage.declineAction")}
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleAcceptRequest(request.id)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <CheckCircle className="h-4 w-4 me-1" />
                            {t("requestsPage.acceptAction")}
                          </Button>
                        </div>
                      )}
                      
                      {request.status === "accepted" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/dashboard/consultations/${request.id}`)}
                        >
                          <MessageSquare className="h-4 w-4 me-1" />
                          {t("consultationStatus.viewConsultation")}
                        </Button>
                      )}
                      
                      {request.status === "declined" && (
                        <span className="text-sm text-red-600 font-medium">
                          {t("requestsPage.requestDeclined")}
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
        )}
      </div>
    </div>
    </VerificationOverlay>
  );
}

// Dummy data for demonstration
const dummyRequests: ConsultationRequest[] = [
  {
    id: "req-1",
    farmer: {
      id: "farmer-1",
      name: "أحمد حسن",
      email: "<EMAIL>",
      phone: "+20 ************",
      location: "Nile Delta, Egypt",
      farmSize: "20 feddan"
    },
    request: {
      title: "استشارة تحليل التربة والتسميد",
      description: "أعاني من انخفاض إنتاجية محصول القمح في حقولي. يبدو أن التربة تفقد خصوبتها وأحتاج إلى مشورة خبير في تحليل التربة واستراتيجيات التسميد المناسبة للموسم القادم.",
      category: "إدارة التربة",
      preferredDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      consultationType: "onsite",
      budget: 1500,
      duration: 7
    },
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    status: "pending"
  },
  {
    id: "req-2",
    farmer: {
      id: "farmer-2",
      name: "فاطمة الرشيد",
      email: "<EMAIL>",
      phone: "+20 ************",
      location: "Upper Egypt",
      farmSize: "12 feddan"
    },
    request: {
      title: "استراتيجية مكافحة الآفات للمحاصيل",
      description: "تتأثر محاصيل الطماطم والفلفل لدي بما يبدو أنه حشرات المن والذبابة البيضاء. أحتاج إلى توجيه فوري حول استراتيجيات مكافحة الآفات المتكاملة التي تكون فعالة وصديقة للبيئة.",
      category: "مكافحة الآفات",
      preferredDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
      consultationType: "online",
      budget: 800,
      duration: 3
    },
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    status: "pending"
  },
  {
    id: "req-3",
    farmer: {
      id: "farmer-3",
      name: "محمد عبد الرحمن",
      email: "<EMAIL>",
      phone: "+20 ************",
      location: "Alexandria",
      farmSize: "30 feddan"
    },
    request: {
      title: "تحسين نظام الري",
      description: "أريد تطوير نظام الري لدي ليكون أكثر كفاءة في استخدام المياه. أبحث عن استشارة حول إعداد نظام الري بالتنقيط واستراتيجيات إدارة المياه لحقول الذرة وفول الصويا.",
      category: "الري",
      preferredDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      consultationType: "onsite",
      budget: 2000,
      duration: 14
    },
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
    status: "accepted"
  }
];
