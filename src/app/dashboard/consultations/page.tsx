"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useTranslation } from "@/components/i18n-provider";
import { usePageTitle } from "@/components/page-title-provider";
import { Input } from "@/components/ui/input";
import { Search, MapPin, Briefcase } from "lucide-react";
import styles from "./consultations.module.css";
import { Badge } from "@/components/ui/badge";
import { VerificationOverlay } from "@/components/verification-overlay";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface Consultation {
  id: string;
  farmer: {
    id: string;
    name: string;
    email: string;
    phone: string;
    location: string;
    farmSize: string;
  };
  request: {
    title: string;
    description: string;
    category: string;
    consultationType: "online" | "onsite";
    budget: number;
    duration: number;
  };
  createdAt: string;
  status: "accepted" | "inProgress" | "completed";
}

export default function ConsultationsPage() {
  const { t, locale } = useTranslation();
  const { setTitle } = usePageTitle();
  const [search, setSearch] = useState("");
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState<string>("all");
  const isRTL = locale === "ar";
  const itemsPerPage = 10;

  useEffect(() => {
    setTitle(t("consultationsPage.title"));
  }, [setTitle, t]);

  // Using the accepted requests as consultations
  const dummyConsultations: Consultation[] = [
    {
      id: "req-3",
      farmer: {
        id: "farmer-3",
        name: "محمد عبد الرحمن",
        email: "<EMAIL>",
        phone: "+20 ************",
        location: "Alexandria",
        farmSize: "30 feddan"
      },
      request: {
        title: "تحسين نظام الري",
        description: "أريد تطوير نظام الري لدي ليكون أكثر كفاءة في استخدام المياه. أبحث عن استشارة حول إعداد نظام الري بالتنقيط واستراتيجيات إدارة المياه لحقول الذرة وفول الصويا.",
        category: "الري",
        consultationType: "onsite",
        budget: 2000,
        duration: 14
      },
      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      status: "accepted"
    }
  ];

  // Filter consultations based on search and status
  const filteredConsultations = dummyConsultations.filter(consultation =>
    (consultation.farmer.name.toLowerCase().includes(search.toLowerCase()) ||
    consultation.request.title.toLowerCase().includes(search.toLowerCase())) &&
    (status === "all" || consultation.status === status)
  );

  // Calculate pagination
  const totalPages = Math.ceil(filteredConsultations.length / itemsPerPage);
  const currentItems = filteredConsultations.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage
  );

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "inProgress":
      case "active":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <VerificationOverlay
      requireVerification={true}
      message={t("verification.consultationRestrictedMessage") || "Access to consultations is restricted to verified experts only. Please complete your verification to manage consultations."}
    >
      <div>
      {/* Search and Filter Section */}
      <div className={styles.searchSection}>
        {/* Status Filter */}
        <div className={styles.statusFilterContainer}>
          <select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
          >
            <option value="all">{t("requestsPage.allStatus")}</option>
            <option value="active">{t("consultationsPage.activeStatus")}</option>
            <option value="inProgress">{t("consultationsPage.inProgressStatus")}</option>
            <option value="completed">{t("consultationsPage.completedStatus")}</option>
          </select>
        </div>

        {/* Search Input */}
        <div className={styles.searchInputContainer}>
          <Search className="absolute ltr:left-3 rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder={t("consultationsPage.searchPlaceholder")}
            className="w-full ltr:pl-10 ltr:pr-3 rtl:pr-10 rtl:pl-3"
          />
        </div>
      </div>

      {/* Consultation Cards */}
      <div className="space-y-4">
        {currentItems.map((consultation) => (
          <Link
            key={consultation.id}
            href={`/dashboard/consultations/${consultation.id}`}
            className={styles.card}
          >
            <div className={styles.contentSection}>
              <div className={styles.farmerName}>
                {consultation.farmer.name}
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                {consultation.request.title}
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600 rtl:space-x-reverse">
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <MapPin className="h-4 w-4" />
                  <span>{consultation.farmer.location}</span>
                </div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <Briefcase className="h-4 w-4" />
                  <span>{consultation.farmer.farmSize}</span>
                </div>
              </div>
            </div>

            <div className={styles.metaSection}>
              <div className="flex items-center space-x-2 rtl:space-x-reverse mt-2">
                <Badge className={getStatusBadgeClass(consultation.status)}>
                  {t(`consultationStatus.${consultation.status}`)}
                </Badge>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* RTL-aware Pagination - Only show if there are multiple pages */}
      {totalPages > 1 && (
        <div className="mt-4 flex justify-center" dir={isRTL ? "rtl" : "ltr"}>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setPage((prev) => Math.max(prev - 1, 1));
                  }}
                  aria-disabled={page === 1}
                  tabIndex={page === 1 ? -1 : 0}
                  className={page === 1 ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
              {[...Array(totalPages)].map((_, index) => (
                <PaginationItem key={index + 1}>
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      setPage(index + 1);
                    }}
                    isActive={page === index + 1}
                  >
                    {index + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setPage((prev) => Math.min(prev + 1, totalPages));
                  }}
                  aria-disabled={page === totalPages}
                  tabIndex={page === totalPages ? -1 : 0}
                  className={page === totalPages ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
      </div>
    </VerificationOverlay>
  );
}
