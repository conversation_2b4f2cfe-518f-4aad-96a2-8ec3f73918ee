/* src/app/dashboard/consultations/consultations.module.css */

.pageTitle {
  text-align: start; /* Ensures title aligns to start (left in LTR, right in RTL) */
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.searchSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (min-width: 640px) {
  .searchSection {
    flex-direction: row;
    align-items: center;
  }
}

.searchInputContainer {
  position: relative;
  flex: 1;
  order: 1;
}

.statusFilterContainer {
  width: 100%;
  order: 2;
}

@media (min-width: 640px) {
  .statusFilterContainer {
    width: 12rem;
  }
}

[dir="rtl"] .searchInputContainer {
  order: 1;
}

[dir="rtl"] .statusFilterContainer {
  order: 2;
}

.card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  transition: background-color 0.3s ease-in-out;
  cursor: pointer;
  margin-bottom: 1rem;
}

html[dir="rtl"] .card {
  flex-direction: row-reverse;
}

/* Basic dark mode support, can be expanded */
html[data-theme="dark"] .card {
  background-color: #1f2937; /* bg-gray-800 */
  border-color: #374151; /* border-gray-700 */
}

html[data-theme="dark"] .card:hover {
  background-color: #374151; /* hover:bg-gray-700 (adjust as needed) */
}

.contentSection {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-width: 0;
  text-align: left;
  order: 1;
}

html[dir="rtl"] .contentSection {
  text-align: right;
  order: 2;
}

.metaSection {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 0.5rem;
  margin-inline-start: auto;
  margin-inline-end: 0;
  order: 2;
}

html[dir="rtl"] .metaSection {
  flex-direction: row;
  justify-content: flex-end;
  margin-inline-start: 0;
  margin-inline-end: auto;
  order: 1;
}

.titleRow {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 0.25rem;
  gap: 0.75rem;
}

[dir="rtl"] .titleRow {
  flex-direction: row;
  justify-content: flex-start;
}

/* Text alignment for all relevant text elements within the card */
.farmerName,
.farmerLocation,
.cropText,
.timeText,
.consultationTitle, /* If there's a specific title for the consultation */
.consultationDescription /* If there's a description */ {
  text-align: start;
}

.farmerName {
  font-weight: bold;
  font-size: 1.125rem; /* text-lg */
}

.farmerLocation {
  font-size: 0.875rem; /* text-sm */
  color: #6b7280; /* text-gray-500 */
}

html[data-theme="dark"] .farmerLocation {
  color: #9ca3af; /* dark:text-gray-400 */
}

.cropInfoContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* space-x-2 */
  margin-block-start: 0.75rem; /* Add some space above crop info */
}

html[dir="rtl"] .cropInfoContainer {
  flex-direction: row-reverse; /* Reverse order of items in RTL */
}

.timeInfoContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* space-x-2 */
  font-size: 0.875rem; /* text-sm */
  color: #6b7280; /* text-gray-500 */
  margin-block-start: 0.75rem; /* For meta section, space from top or other elements */
}

html[dir="rtl"] .timeInfoContainer {
  flex-direction: row-reverse; /* Reverse order of items in RTL */
}

html[data-theme="dark"] .timeInfoContainer {
  color: #9ca3af; /* dark:text-gray-400 */
}

.searchInput {
  width: 100%;
  padding-block: 0.625rem;
  padding-inline: 3rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: white;
  font-size: 0.875rem;
  color: #1f2937;
  text-align: start;
}

.searchInput::placeholder {
  color: #6b7280;
}

[data-theme="dark"] .searchInput {
  background-color: #374151;
  border-color: #4b5563;
  color: #f3f4f6;
}

[data-theme="dark"] .searchInput::placeholder {
  color: #9ca3af;
}

[data-theme="dark"] .searchIcon {
  color: #9ca3af;
}

.avatar {
  width: 5rem; /* h-20 w-20 */
  height: 5rem;
  border-radius: 9999px; /* rounded-full */
  border: 2px solid transparent; /* Example, can be primary color */
  /* border-color: #86A74F; /* Example primary color */
}
