"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useTranslation } from "@/components/i18n-provider";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Copy, Clock, Calendar, Tag, User, DollarSign } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";

// Dummy data structure - replace with actual data fetching
interface Consultation {
  id: string;
  status: "inProgress" | "completed" | "active";
  farmer: {
    name: string;
  };
  details: {
    price: string;
    startDate: string;
    endDate: string;
    duration: string;
    consultationId: string;
    farmerProfile: {
      name: string;
      phone: string;
    };
  };
  farmData: {
    farmProfile: {
      name: string;
      location: string;
      size: string;
      soilType: string;
      waterSourceType: string;
      waterAvailability: string;
      tools: string[];
    };
    crops: {
      name: string;
      plantingDate: string;
      area: string;
    }[];
    yieldHistory: { crop: string; year: number; amount: string; }[];
    fertilizerHistory: { date: string; type: string; amount: string; }[];
    pestHistory: { date: string; pest: string; severity: string; }[];
    pesticideHistory: { date: string; name: string; amount: string; }[];
    realTimeEvents: {
      id: number;
      eventName: string;
      description: string;
      category: 'Water Irrigation' | 'Water Issues' | 'Pest Encountering' | 'Crop Condition Update' | 'General Farm Update' | 'Other';
      details: string;
      images: string[];
    }[];
  };
  communication: {
    messages: {
      sender: 'farmer' | 'expert';
      content: string;
      timestamp: string;
    }[];
    internalNotes: string;
  };
}

const dummyConsultation: Consultation = {
  id: "1",
  farmer: { name: "Mohamed Ali" },
  status: "inProgress",
  details: {
    price: "50 EGP/hour",
    startDate: "Jun 1, 2025",
    endDate: "Jul 1, 2025",
    duration: "30 days",
    consultationId: "CONS-1234",
    farmerProfile: {
      name: "Mohamed Ali",
      phone: "+20 ************",
    },
  },
  farmData: {
    farmProfile: {
      name: "Hassan's Farm",
      location: "Minya, Egypt",
      size: "50 feddan",
      soilType: "Clay Loam",
      waterSourceType: "Canal",
      waterAvailability: "Daily",
      tools: ["Hoe", "Pump"],
    },
    crops: [
      { name: "Wheat", plantingDate: "2024-11-15", area: "30 feddan" },
      { name: "Tomatoes", plantingDate: "2025-03-01", area: "20 feddan" },
    ],
    yieldHistory: [
      { crop: 'Wheat', year: 2024, amount: '500 kg' },
      { crop: 'Corn', year: 2023, amount: '700 kg' },
    ],
    fertilizerHistory: [
      { date: '2024-03-15', type: 'Urea', amount: '50 kg' },
    ],
    pestHistory: [
      { date: '2023-07-20', pest: 'Tomato Blight', severity: 'High' },
    ],
    pesticideHistory: [
      { date: '2023-07-22', name: 'Copper Sulphate', amount: '5 L' },
    ],
    realTimeEvents: [
      {
        id: 1,
        eventName: 'Spotted Yellow Leaves',
        description: 'Yellowing leaves in the wheat field, sector 4.',
        category: 'Crop Condition Update',
        details: 'Observed significant yellowing on lower leaves of wheat plants. Appears to be a nitrogen deficiency.',
        images: ['https://via.placeholder.com/150', 'https://via.placeholder.com/150'],
      },
      {
        id: 2,
        eventName: 'Canal Water Level Low',
        description: 'Main irrigation canal water level is lower than usual.',
        category: 'Water Issues',
        details: 'The water level in the main canal has dropped by about 50% overnight. May affect irrigation schedule for the next few days.',
        images: ['https://via.placeholder.com/150'],
      },
    ],
  },
  communication: {
    messages: [
      { sender: 'farmer', content: 'The new irrigation system is working great!', timestamp: '10:30 AM' },
      { sender: 'expert', content: 'Excellent! Let\'s monitor the soil moisture for the next 48 hours.', timestamp: '10:35 AM' },
    ],
    internalNotes: 'Follow up on soil moisture levels on Friday.',
  },
};

export default function ConsultationDetailPage() {
  const { t } = useTranslation();
  const router = useRouter();
  const params = useParams();
  const { id } = params;

  const [consultation, setConsultation] = useState<Consultation | null>(null);
  const [loading, setLoading] = useState(true);
  const [expandedRow, setExpandedRow] = useState<number | null>(null);

  useEffect(() => {
    // Simulate fetching data
    setTimeout(() => {
      setConsultation({
        ...dummyConsultation,
      });
      setLoading(false);
    }, 1000);
  }, [id]);

  if (loading) {
    return <div>{t("common.loading")}</div>;
  }

  if (!consultation) {
    return <div>{t("common.error")}</div>;
  }

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "inProgress":
      case "active":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8">
      <Button variant="ghost" onClick={() => router.back()} className="mb-6">
        <ArrowLeft className="mr-2 h-4 w-4 rtl:mr-0 rtl:ml-2" />
        {t("common.back")}
      </Button>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">{t("consultationDetailTabs.details")}</TabsTrigger>
          <TabsTrigger value="farmData">{t("consultationDetailTabs.farmData")}</TabsTrigger>
          <TabsTrigger value="communication">{t("consultationDetailTabs.communication")}</TabsTrigger>
        </TabsList>
        <TabsContent value="details">
          <Card className="bg-white dark:bg-gray-800 shadow-md rounded-lg">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-xl font-bold text-gray-800 dark:text-white">
                  {consultation.farmer.name}
                </CardTitle>
                <Badge className={getStatusBadgeClass(consultation.status)}>
                  {t(`consultationStatus.${consultation.status}`)}
                </Badge>
              </div>
              <CardDescription>{t('consultationDetailTabs.details')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-base">
                <div className="flex items-center rtl:flex-row-reverse">
                  <DollarSign className="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary" />
                  <div>
                    <p className="font-semibold text-gray-700 dark:text-gray-300">{t('consultationDetails.price')}</p>
                    <p className="text-gray-600 dark:text-gray-400">{consultation.details.price}</p>
                  </div>
                </div>
                <div className="flex items-center rtl:flex-row-reverse">
                  <User className="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary" />
                  <div>
                    <p className="font-semibold text-gray-700 dark:text-gray-300">{t('consultationDetail.farmerName')}</p>
                    <p className="text-gray-600 dark:text-gray-400">{consultation.details.farmerProfile.name}</p>
                  </div>
                </div>
                <div className="flex items-center rtl:flex-row-reverse">
                  <User className="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary" />
                  <div>
                    <p className="font-semibold text-gray-700 dark:text-gray-300">{t('consultationDetail.farmerPhone')}</p>
                    <p className="text-gray-600 dark:text-gray-400">{consultation.details.farmerProfile.phone}</p>
                  </div>
                </div>
                <div className="flex items-center rtl:flex-row-reverse">
                  <Calendar className="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary" />
                  <div>
                    <p className="font-semibold text-gray-700 dark:text-gray-300">{t('consultationDetails.startDate')}</p>
                    <p className="text-gray-600 dark:text-gray-400">{consultation.details.startDate}</p>
                  </div>
                </div>
                <div className="flex items-center rtl:flex-row-reverse">
                  <Calendar className="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary" />
                  <div>
                    <p className="font-semibold text-gray-700 dark:text-gray-300">{t('consultationDetails.endDate')}</p>
                    <p className="text-gray-600 dark:text-gray-400">{consultation.details.endDate}</p>
                  </div>
                </div>
                <div className="flex items-center rtl:flex-row-reverse">
                  <Clock className="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary" />
                  <div>
                    <p className="font-semibold text-gray-700 dark:text-gray-300">{t('consultationDetails.duration')}</p>
                    <p className="text-gray-600 dark:text-gray-400">{consultation.details.duration}</p>
                  </div>
                </div>
                <div className="flex items-center col-span-1 md:col-span-2 rtl:flex-row-reverse">
                  <Tag className="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary" />
                  <div>
                    <p className="font-semibold text-gray-700 dark:text-gray-300">{t('consultationDetails.consultationId')}</p>
                    <div className="flex items-center rtl:flex-row-reverse">
                      <p className="text-gray-600 dark:text-gray-400">{consultation.details.consultationId}</p>
                      <Button variant="ghost" size="sm" onClick={() => navigator.clipboard.writeText(consultation.details.consultationId)}>
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-4 rtl:space-x-reverse pt-4">
                <Button variant="outline">{t('consultationDetails.extend')}</Button>
                <Button>{t('consultationDetails.markComplete')}</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="farmData">
          <div className="space-y-6">
            <Accordion type="multiple" defaultValue={['farm-profile', 'crops', 'historical-data', 'real-time-events']}>
              <AccordionItem value="farm-profile" className="border-b-0">
                <AccordionTrigger className="text-lg font-bold hover:no-underline">{t("consultationDetail.farmProfile")}</AccordionTrigger>
                <AccordionContent>
                  <Card>
                    <CardContent className="p-4 space-y-2 text-start">
                      <p><strong>{t("consultationDetail.farmName")}:</strong> {consultation.farmData.farmProfile.name}</p>
                      <p><strong>{t("consultationDetail.farmLocation")}:</strong> {consultation.farmData.farmProfile.location}</p>
                      <p><strong>{t("consultationDetail.farmSize")}:</strong> {consultation.farmData.farmProfile.size}</p>
                      <p><strong>{t("consultationDetail.soilType")}:</strong> {consultation.farmData.farmProfile.soilType}</p>
                      <p><strong>{t("consultationDetail.waterSource")}:</strong> {consultation.farmData.farmProfile.waterSourceType}</p>
                      <p><strong>{t("consultationDetail.waterAvailability")}:</strong> {consultation.farmData.farmProfile.waterAvailability}</p>
                      <p><strong>{t("consultationDetail.tools")}:</strong> {consultation.farmData.farmProfile.tools.join(', ')}</p>
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="crops" className="border-b-0">
                <AccordionTrigger className="text-lg font-bold hover:no-underline">{t("consultationDetail.crops")}</AccordionTrigger>
                <AccordionContent>
                  <Card>
                    <CardContent className="p-4 space-y-4 text-start">
                      {consultation.farmData.crops.map((crop, index) => (
                        <div key={index} className="space-y-1">
                          <p><strong>{t("consultationDetail.cropName")}:</strong> {crop.name}</p>
                          <p><strong>{t("consultationDetail.plantingDate")}:</strong> {crop.plantingDate}</p>
                          <p><strong>{t("consultationDetail.cropArea")}:</strong> {crop.area}</p>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="historical-data" className="border-b-0">
                <AccordionTrigger className="text-lg font-bold hover:no-underline">{t("consultationDetail.historicalData")}</AccordionTrigger>
                <AccordionContent>
                    <div className="space-y-4 p-2">
                      <Card>
                        <CardHeader><CardTitle className="text-base font-semibold text-start">{t("consultationDetail.yieldHistory")}</CardTitle></CardHeader>
                        <CardContent>
                          <Table>
                            <TableHeader><TableRow><TableHead className="text-start">{t("consultationDetail.crop")}</TableHead><TableHead className="text-start">{t("consultationDetail.year")}</TableHead><TableHead className="text-start">{t("consultationDetail.amount")}</TableHead></TableRow></TableHeader>
                            <TableBody>
                              {consultation.farmData.yieldHistory.map((entry, i) => <TableRow key={i}><TableCell>{entry.crop}</TableCell><TableCell>{entry.year}</TableCell><TableCell>{entry.amount}</TableCell></TableRow>)}
                            </TableBody>
                          </Table>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader><CardTitle className="text-base font-semibold text-start">{t("consultationDetail.fertilizerHistory")}</CardTitle></CardHeader>
                        <CardContent>
                          <Table>
                            <TableHeader><TableRow><TableHead className="text-start">{t("consultationDetail.date")}</TableHead><TableHead className="text-start">{t("consultationDetail.type")}</TableHead><TableHead className="text-start">{t("consultationDetail.amount")}</TableHead></TableRow></TableHeader>
                            <TableBody>
                              {consultation.farmData.fertilizerHistory.map((entry, i) => <TableRow key={i}><TableCell>{entry.date}</TableCell><TableCell>{entry.type}</TableCell><TableCell>{entry.amount}</TableCell></TableRow>)}
                            </TableBody>
                          </Table>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader><CardTitle className="text-base font-semibold text-start">{t("consultationDetail.pestHistory")}</CardTitle></CardHeader>
                        <CardContent>
                          <Table>
                            <TableHeader><TableRow><TableHead className="text-start">{t("consultationDetail.date")}</TableHead><TableHead className="text-start">{t("consultationDetail.pest")}</TableHead><TableHead className="text-start">{t("consultationDetail.severity")}</TableHead></TableRow></TableHeader>
                            <TableBody>
                              {consultation.farmData.pestHistory.map((entry, i) => <TableRow key={i}><TableCell>{entry.date}</TableCell><TableCell>{entry.pest}</TableCell><TableCell>{entry.severity}</TableCell></TableRow>)}
                            </TableBody>
                          </Table>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader><CardTitle className="text-base font-semibold text-start">{t("consultationDetail.pesticideHistory")}</CardTitle></CardHeader>
                        <CardContent>
                          <Table>
                            <TableHeader><TableRow><TableHead className="text-start">{t("consultationDetail.date")}</TableHead><TableHead className="text-start">{t("consultationDetail.name")}</TableHead><TableHead className="text-start">{t("consultationDetail.amount")}</TableHead></TableRow></TableHeader>
                            <TableBody>
                              {consultation.farmData.pesticideHistory.map((entry, i) => <TableRow key={i}><TableCell>{entry.date}</TableCell><TableCell>{entry.name}</TableCell><TableCell>{entry.amount}</TableCell></TableRow>)}
                            </TableBody>
                          </Table>
                        </CardContent>
                      </Card>
                    </div>
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="real-time-events" className="border-b-0">
                <AccordionTrigger className="text-lg font-bold hover:no-underline">{t("consultationDetail.realTimeEvents")}</AccordionTrigger>
                <AccordionContent>
                  <Card>
                    <CardContent className="p-4">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="text-start">{t("consultationDetail.event")}</TableHead>
                            <TableHead className="text-start">{t("consultationDetail.category")}</TableHead>
                            <TableHead className="text-start">{t("consultationDetail.description")}</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {consultation.farmData.realTimeEvents.map((event) => (
                            <>
                              <TableRow key={event.id} onClick={() => setExpandedRow(expandedRow === event.id ? null : event.id)} className="cursor-pointer">
                                <TableCell className="font-medium">{event.eventName}</TableCell>
                                <TableCell>{event.category}</TableCell>
                                <TableCell>{event.description}</TableCell>
                              </TableRow>
                              {expandedRow === event.id && (
                                <TableRow>
                                  <TableCell colSpan={3}>
                                    <div className="p-4 bg-muted rounded-md text-start">
                                      <h4 className="font-semibold mb-2">{t("consultationDetail.details")}:</h4>
                                      <p className="mb-4">{event.details}</p>
                                      {event.images.length > 0 && (
                                        <>
                                          <h4 className="font-semibold mb-2">{t("consultationDetail.images")}:</h4>
                                          <div className="flex space-x-2 rtl:space-x-reverse">
                                            {event.images.map((image, imgIndex) => (
                                              <Image
                                                key={imgIndex}
                                                src={image}
                                                alt="Event"
                                                width={96}
                                                height={96}
                                                className="w-24 h-24 object-cover rounded-md"
                                              />
                                            ))}
                                          </div>
                                        </>
                                      )}
                                    </div>
                                  </TableCell>
                                </TableRow>
                              )}
                            </>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </TabsContent>
        <TabsContent value="communication">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-start">{t("consultationDetail.chat")}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {consultation.communication.messages.map((message, index) => (
                    <div key={index} className={`flex ${message.sender === 'expert' ? 'justify-end' : 'justify-start'}`}>
                      <div className={`p-3 rounded-lg ${message.sender === 'expert' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
                        <p>{message.content}</p>
                        <p className={`text-xs text-muted-foreground mt-1 ${message.sender === 'expert' ? 'text-left' : 'text-right'}`}>{message.timestamp}</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
                <CardFooter>
                  <div className="flex w-full items-center space-x-2 rtl:space-x-reverse">
                    <Textarea placeholder={t("consultationDetail.typeMessagePlaceholder")} />
                    <Button>{t("consultationDetail.sendAction")}</Button>
                  </div>
                </CardFooter>
              </Card>
            </div>
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-start">{t("consultationDetail.internalNotes")}</CardTitle>
                  <p className="text-xs text-muted-foreground text-start">{t("consultationDetail.internalNotesDescription")}</p>
                </CardHeader>
                <CardContent>
                  <Textarea defaultValue={consultation.communication.internalNotes} />
                  <Button className="mt-2 w-full">{t("consultationDetail.saveNotesAction")}</Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-start">{t("consultationDetail.recommendations")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea placeholder={t("consultationDetail.recommendationPlaceholder")} />
                  <Button className="mt-2 w-full">{t("consultationDetail.sendRecommendationAction")}</Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}