"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/supabase/client";
import dynamic from "next/dynamic";
import UserProfile from "@/components/user-profile";
import DashboardHeader from "@/components/dashboard-header";
import { useAuth } from "@/components/auth-provider";
import { useTranslation } from "@/components/i18n-provider";
import type { User as SupabaseUser } from "@supabase/supabase-js";
import { PageTitleProvider } from "@/components/page-title-provider";
import { cn } from "@/lib/utils";

// Dynamically import dashboard components to avoid SSR issues
const DashboardSidebar = dynamic(
  () => import("@/components/dashboard-sidebar"),
  {
    ssr: false,
  },
);

interface UserProfile {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  role?: string;
  profile_picture_url?: string;
  location?: string;
  expertise?: string;
  is_available?: boolean;
  created_at: string;
  updated_at: string;
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth() as {
    user: SupabaseUser | null;
    isLoading: boolean;
  };
  const { locale } = useTranslation();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [userRole, setUserRole] = useState("expert");

  // Function to fetch full profile data and determine role
  const fetchFullProfile = useCallback(async (userId: string) => {
    try {
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .maybeSingle();

      if (profileData) {
        console.log("Profile data fetched successfully");
        setProfile(profileData as UserProfile);
      } else if (profileError) {
        console.warn("Error fetching profile:", profileError.message);
        // Create a fallback profile from user metadata if database fetch fails
        if (user) {
          const fallbackProfile: UserProfile = {
            id: userId,
            first_name: user.user_metadata?.first_name || user.email?.split('@')[0] || 'User',
            last_name: user.user_metadata?.last_name || '',
            email: user.email || '',
            role: user.user_metadata?.role || 'expert',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          console.log("Using fallback profile from user metadata");
          setProfile(fallbackProfile);
        }
      }
    } catch (error) {
      console.error("Error fetching profile data:", error);
      // Create a fallback profile from user metadata if database fetch fails
      if (user) {
        const fallbackProfile: UserProfile = {
          id: userId,
          first_name: user.user_metadata?.first_name || user.email?.split('@')[0] || 'User',
          last_name: user.user_metadata?.last_name || '',
          email: user.email || '',
          role: user.user_metadata?.role || 'expert',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        console.log("Using fallback profile from user metadata");
        setProfile(fallbackProfile);
      }
    }
  }, [user]);

  // Function to determine user role based on database tables
  const determineUserRole = useCallback(async (userId: string) => {
    try {
      // First priority: Check user metadata (most reliable during development)
      const metadataRole = user?.user_metadata?.role;
      if (metadataRole) {
        console.log("Role found in user metadata:", metadataRole);
        setUserRole(metadataRole.toLowerCase());
        return;
      }

      // Second priority: Try to check user_roles table (handle RLS errors gracefully)
      try {
        const { data: userRoleData, error: userRoleError } = await supabase
          .from("user_roles")
          .select(`
            roles (
              name,
              is_admin_role
            )
          `)
          .eq("user_id", userId)
          .maybeSingle();

        if (!userRoleError && userRoleData?.roles) {
          const roles = userRoleData.roles as unknown as { name: string; is_admin_role: boolean };
          const roleName = roles.name?.toLowerCase();
          console.log("Role found in user_roles table:", roleName);
          setUserRole(roleName || "expert");
          return;
        }
      } catch (roleError) {
        console.log("Could not access user_roles table, continuing with fallbacks...");
      }

      // Third priority: Check expert_profiles table (handle RLS errors gracefully)
      try {
        const { data: expertData, error: expertError } = await supabase
          .from("expert_profiles")
          .select("id")
          .eq("id", userId)
          .maybeSingle();

        if (!expertError && expertData) {
          console.log("User identified as expert");
          setUserRole("expert");
          return;
        }
      } catch (expertError) {
        console.log("Could not access expert_profiles table, continuing with fallbacks...");
      }

      // Fourth priority: Check farmer_profiles table (handle RLS errors gracefully)
      try {
        const { data: farmerData, error: farmerError } = await supabase
          .from("farmer_profiles")
          .select("id")
          .eq("id", userId)
          .maybeSingle();

        if (!farmerError && farmerData) {
          console.log("User identified as farmer");
          setUserRole("farmer");
          return;
        }
      } catch (farmerError) {
        console.log("Could not access farmer_profiles table, continuing with fallbacks...");
      }

      // Final fallback to expert
      console.log("No specific role found, defaulting to expert");
      setUserRole("expert");

    } catch (error) {
      console.error("Error determining user role:", error);
      // Ultimate fallback: check user metadata again
      const metadataRole = user?.user_metadata?.role;
      if (metadataRole) {
        setUserRole(metadataRole.toLowerCase());
      } else {
        setUserRole("expert");
      }
    }
  }, [user]);

  // Main initialization function
  const initializeUser = useCallback(async () => {
    if (authLoading) return;

    if (!user) {
      console.log("No user found, redirecting to sign-in");
      router.push("/sign-in");
      return;
    }

    console.log("User found, initializing dashboard");

    // Determine user role from database
    await determineUserRole(user.id);

    // Fetch full profile data
    await fetchFullProfile(user.id);
  }, [authLoading, user, router, determineUserRole, fetchFullProfile]);

  useEffect(() => {
    initializeUser();
  }, [initializeUser]);

  // Show loading state while auth is loading
  if (authLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <div className="text-lg font-medium">Loading...</div>
        </div>
      </div>
    );
  }

  // Redirect if no user
  if (!user) {
    return null;
  }

  // Return null if no profile yet
  if (!profile) {
    return null;
  }

  return (
    <div
      className="flex h-screen bg-gray-50"
      dir={locale === "ar" ? "rtl" : "ltr"}
    >
      {/* Sidebar - Always first in DOM, positioned via CSS */}
      <div className={cn(
        "order-1 w-64",
        locale === "ar" ? "border-l margin-inline-start-auto" : "border-r margin-inline-start-0"
      )}>
        <DashboardSidebar userRole={userRole} />
      </div>
      
      {/* Main content - Always second in DOM */}
      <div className="order-2 flex-1 overflow-auto">
        <PageTitleProvider>
          <DashboardHeader />
          <main className="flex-1 p-6 ltr:text-left rtl:text-right">{children}</main>
        </PageTitleProvider>
      </div>
    </div>
  );
}
