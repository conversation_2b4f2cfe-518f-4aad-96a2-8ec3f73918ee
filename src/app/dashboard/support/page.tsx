import { createClient } from "@/supabase/server";
import { redirect } from "next/navigation";
import { getUserRole } from "@/utils/get-user-role";
import SupportClientPage from "./support-client-page";

export default async function SupportPage() {
  const supabase = createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  const role = await getUserRole(user);
  const isAdmin = role === "admin";

  return (
    <div className="p-6">
      <SupportClientPage isAdmin={isAdmin} />
    </div>
  );
}
