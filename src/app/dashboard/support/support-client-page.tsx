"use client";

import { useEffect } from "react";
import { useTranslation } from "@/components/i18n-provider";
import { usePageTitle } from "@/components/page-title-provider";
import AdminSupportView from "@/components/dashboard/views/admin-support-view";
import ExpertSupportView from "@/components/dashboard/views/expert-support-view";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function SupportClientPage({ isAdmin }: { isAdmin: boolean }) {
  const { t } = useTranslation();
 const { setTitle } = usePageTitle();

  useEffect(() => {
   setTitle("supportPage.title");
 }, [setTitle, t]);

  if (!isAdmin) {
    return <ExpertSupportView />;
  }

  // Data for admin view
  const openTickets = 12;
  const resolvedToday = 8;
  const totalTickets = "20";
  const recentTickets = [
    {
      id: "TK-001",
      subject: "Login issues with mobile app",
      status: "Open",
      priority: "High",
      created: "2 hours ago",
      statusColor: "bg-orange-500",
    },
    {
      id: "TK-002",
      subject: "Feature request: Export data",
      status: "In Progress",
      priority: "Medium",
      created: "1 day ago",
      statusColor: "bg-blue-500",
    },
  ];

  return (
    <>
      <div className="mb-8">
        <div className="flex items-center justify-between text-left rtl:text-right mb-0">
          <div className="flex-1">
            <p className="text-muted-foreground text-sm rtl:text-right ltr:text-left">
              {t("supportPage.description")}
            </p>
          </div>
          <div className="shrink-0">
            <Button variant="default">
              <Plus className="h-4 w-4 rtl:ml-2 ltr:mr-2" />
              {t("supportPage.title")}
            </Button>
          </div>
        </div>
      </div>
      <AdminSupportView
        openTickets={openTickets}
        resolvedToday={resolvedToday}
        totalTickets={totalTickets}
        recentTickets={recentTickets}
      />
    </>
  );
}