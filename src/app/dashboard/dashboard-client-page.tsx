"use client";

import { usePageTitle } from "@/components/page-title-provider";
import { useTranslation } from "@/components/i18n-provider";
import { useAuth } from "@/components/auth-provider";
import { useEffect, useState } from "react";

export default function DashboardClientPage({ children }: { children: React.ReactNode }) {
  const { t } = useTranslation();
  const { setTitle } = usePageTitle();
  const { user, isLoading } = useAuth();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    setTitle("pageTitles.dashboard");
  }, [setTitle, t]);

  // Handle loading state
  if (!isClient || isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center">
        <div className="h-32 w-32 rounded-lg bg-muted animate-pulse" />
      </div>
    );
  }

  // Handle unauthenticated state
  if (!user) {
    if (typeof window !== 'undefined') {
      window.location.href = '/sign-in';
    }
    return null;
  }

  return <>{children}</>;
}
