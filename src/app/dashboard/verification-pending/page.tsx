"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "@/components/i18n-provider";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useUserProfile } from "@/hooks/use-user-profile";
import { supabase } from "@/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { AlertCircle, CheckCircle, Upload, FileText, Loader2 } from "lucide-react";

interface VerificationData {
  verification_status?: string;
  rejection_reasons?: string[];
  admin_notes?: string;
}

export default function VerificationPendingPage() {
  const { t, locale } = useTranslation();
  const { user, expertProfile, loading } = useUserProfile();
  const { toast } = useToast();
  const [verificationData, setVerificationData] = useState<VerificationData>({});
  const [formData, setFormData] = useState({
    bio: '',
    years_of_experience: '',
    education: '',
    expertise_area: '',
    expert_type: '',
    certifications: '',
    current_position: '',
    organization: '',
    languages_spoken: '',
    professional_memberships: '',
    awards_honors: ''
  });
  const [filesToUpload, setFilesToUpload] = useState<File[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (user?.id) {
      fetchVerificationStatus();
    }
  }, [user?.id]);

  const fetchVerificationStatus = async () => {
    try {
      const { data, error } = await supabase.rpc('get_my_verification_status');
      if (error) {
        console.error('Error fetching verification status:', error);
        return;
      }
      if (data) {
        setVerificationData(data);
      }
    } catch (error) {
      console.error('Error fetching verification status:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setFilesToUpload(prevFiles => [...prevFiles, ...Array.from(event.target.files!)]);
    }
  };

  const handleFileDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (event.dataTransfer.files) {
      setFilesToUpload(prevFiles => [...prevFiles, ...Array.from(event.dataTransfer.files!)]);
    }
  };

  const removeFile = (index: number) => {
    setFilesToUpload(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Basic Information validation
    if (!formData.bio) newErrors.bio = t('validation.required');
    if (!formData.expertise_area) newErrors.expertise_area = t('validation.required');
    if (!formData.expert_type) newErrors.expert_type = t('validation.required');

    // Education validation
    if (!formData.education) newErrors.education = t('validation.required');

    // Experience validation - all fields in الخبرة section
    if (!formData.years_of_experience) newErrors.years_of_experience = t('validation.required');
    if (!formData.certifications) newErrors.certifications = t('validation.required');
    if (!formData.current_position) newErrors.current_position = t('validation.required');
    if (!formData.organization) newErrors.organization = t('validation.required');
    if (!formData.languages_spoken) newErrors.languages_spoken = t('validation.required');
    if (!formData.professional_memberships) newErrors.professional_memberships = t('validation.required');

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const uploadFiles = async (files: File[], userId: string) => {
    const uploadedFileUrls: string[] = [];
    for (const file of files) {
      const filePath = `${userId}/${Date.now()}-${file.name}`;
      const { error } = await supabase.storage
        .from('expert_verification_documents')
        .upload(filePath, file, { cacheControl: '3600', upsert: false });

      if (error) {
        throw new Error(`Failed to upload ${file.name}: ${error.message}`);
      }
      
      const { data } = supabase.storage.from('expert_verification_documents').getPublicUrl(filePath);
      if (data?.publicUrl) {
        uploadedFileUrls.push(data.publicUrl);
      }
    }
    return uploadedFileUrls;
  };

  const handleSubmitForm = async () => {
    if (!user?.id || !validateForm()) return;

    setSubmitting(true);
    try {
      // Upload files and store in expert_documents table
      if (filesToUpload.length > 0) {
        const uploadedFileUrls = await uploadFiles(filesToUpload, user.id);

        // Insert documents into expert_documents table
        for (let i = 0; i < uploadedFileUrls.length; i++) {
          const file = filesToUpload[i];
          const fileUrl = uploadedFileUrls[i];

          const { error: docError } = await supabase
            .from('expert_documents')
            .insert({
              expert_id: user.id,
              document_type: 'verification', // General type for verification documents
              document_url: fileUrl,
              document_name: file.name,
              file_size: file.size,
              file_type: file.type,
              mime_type: file.type
            });

          if (docError) {
            console.error('Error inserting document:', docError);
            throw new Error(`Failed to save document ${file.name}: ${docError.message}`);
          }
        }
      }

      const { error: profileError } = await supabase
        .from('expert_profiles')
        .upsert({
          id: user.id,
          bio: formData.bio,
          years_of_experience: parseInt(formData.years_of_experience) || null,
          education: formData.education,
          expertise_area: formData.expertise_area,
          expert_type: formData.expert_type as 'AGRICULTURE' | 'INDUSTRIAL',
          certifications: formData.certifications,
          current_position: formData.current_position,
          organization: formData.organization,
          languages_spoken: formData.languages_spoken,
          professional_memberships: formData.professional_memberships,
          awards_honors: formData.awards_honors,
          verification_status: 'pending',
          updated_at: new Date().toISOString()
        });

      if (profileError) throw profileError;

      toast({
        title: t("verification.verificationSubmittedTitle"),
        description: t("verification.verificationSubmittedDescription"),
      });

      // Redirect to dashboard after successful submission
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 2000); // Wait 2 seconds to show the success message
    } catch (error) {
      console.error('Error submitting form:', error);
      toast({
        variant: "destructive",
        title: t("verificationPendingPage.submissionFailed"),
        description: (error as Error).message || t("verificationPendingPage.submissionFailedDescription"),
      });
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusInfo = () => {
    const status = verificationData.verification_status || expertProfile?.verification_status || 'not_submitted';
    switch (status) {
      case 'approved':
        return {
          title: t("verificationPendingPage.submissionSuccess") || "Verification Approved",
          description: t("verificationPendingPage.submissionSuccessDescription") || "Your expert account has been verified and approved!",
          icon: <CheckCircle className="h-6 w-6 text-green-500" />,
        };
      case 'rejected':
        return {
          title: t("verificationPendingPage.rejectedTitle"),
          description: t("verificationPendingPage.rejectedDescription"),
          icon: <AlertCircle className="h-6 w-6 text-red-500" />,
        };
      default:
        return {
          title: t("verification.expertVerificationForm") || "Expert Verification Form",
          description: t("verification.verificationFormDescription") || "Please provide the required information and documents below.",
          icon: <FileText className="h-6 w-6 text-blue-500" />,
        };
    }
  };

  const { title, description, icon } = getStatusInfo();
  const status = verificationData.verification_status || expertProfile?.verification_status || 'not_submitted';

  if (loading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }

  if (status === 'approved') {
    return (
      <div className="container mx-auto p-6">
        <Card className="max-w-2xl mx-auto">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">{icon}</div>
            <CardTitle className="text-2xl">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={() => window.location.href = '/dashboard'}>
              Go to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6" dir={locale === "ar" ? "rtl" : "ltr"}>
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl">{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
          {['not_submitted', 'rejected', 'resubmission_required'].includes(status) && (
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mt-4">
              <p className="text-blue-800 text-sm">{t("verification.accuracyNotice")}</p>
            </div>
          )}
        </CardHeader>

        <CardContent>
          {status === 'rejected' && verificationData.rejection_reasons && (
            <div className="mb-6 p-4 border border-red-200 rounded-lg bg-red-50">
              <h4 className="font-semibold text-red-800 mb-2">Rejection Reasons:</h4>
              <ul className="list-disc list-inside text-red-700">
                {verificationData.rejection_reasons.map((reason, index) => (
                  <li key={index}>{reason}</li>
                ))}
              </ul>
              {verificationData.admin_notes && (
                <div className="mt-3">
                  <p className="font-semibold text-red-800">Admin Notes:</p>
                  <p className="text-red-700">{verificationData.admin_notes}</p>
                </div>
              )}
            </div>
          )}

          {['pending', 'rejected', 'not_submitted', 'resubmission_required'].includes(status) && (
            <form className="space-y-8" onSubmit={(e) => { e.preventDefault(); handleSubmitForm(); }}>
              <section>
                <h3 className="text-lg font-semibold mb-4 border-b pb-2">{t("verification.basicInformation")}</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="expertise_area">{t("verification.expertiseArea")}</Label>
                      <Input id="expertise_area" value={formData.expertise_area} onChange={(e) => handleInputChange('expertise_area', e.target.value)} placeholder={t("verification.expertiseAreaPlaceholder")} />
                      {errors.expertise_area && <p className="text-red-500 text-sm mt-1">{errors.expertise_area}</p>}
                    </div>
                    <div>
                      <Label htmlFor="expert_type">{t("verification.expertType")}</Label>
                      <Select value={formData.expert_type} onValueChange={(value) => handleInputChange('expert_type', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder={t("verification.selectExpertType")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="AGRICULTURE">{t("verification.expertTypeAgriculture")}</SelectItem>
                          <SelectItem value="INDUSTRIAL">{t("verification.expertTypeIndustrial")}</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.expert_type && <p className="text-red-500 text-sm mt-1">{errors.expert_type}</p>}
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="years_of_experience">{t("verification.yearsOfExperience")}</Label>
                      <Input id="years_of_experience" type="number" value={formData.years_of_experience} onChange={(e) => handleInputChange('years_of_experience', e.target.value)} placeholder="5" />
                      {errors.years_of_experience && <p className="text-red-500 text-sm mt-1">{errors.years_of_experience}</p>}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="bio">{t("verification.professionalBio")}</Label>
                    <Textarea id="bio" value={formData.bio} onChange={(e) => handleInputChange('bio', e.target.value)} placeholder={t("verification.bioPlaceholder")} rows={4} />
                    {errors.bio && <p className="text-red-500 text-sm mt-1">{errors.bio}</p>}
                  </div>
                  <div>
                    <Label htmlFor="education">{t("verification.education")}</Label>
                    <Textarea id="education" value={formData.education} onChange={(e) => handleInputChange('education', e.target.value)} placeholder={t("verification.educationPlaceholder")} rows={3} />
                    {errors.education && <p className="text-red-500 text-sm mt-1">{errors.education}</p>}
                  </div>
                </div>
              </section>

              <section>
                <h3 className="text-lg font-semibold mb-4 border-b pb-2">{t("verification.experience")}</h3>
                <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="current_position">{t("verification.currentPosition")}</Label>
                            <Input id="current_position" value={formData.current_position} onChange={(e) => handleInputChange('current_position', e.target.value)} placeholder={t("verification.currentPositionPlaceholder")} />
                            {errors.current_position && <p className="text-red-500 text-sm mt-1">{errors.current_position}</p>}
                        </div>
                        <div>
                            <Label htmlFor="organization">{t("verification.organization")}</Label>
                            <Input id="organization" value={formData.organization} onChange={(e) => handleInputChange('organization', e.target.value)} placeholder={t("verification.organizationPlaceholder")} />
                            {errors.organization && <p className="text-red-500 text-sm mt-1">{errors.organization}</p>}
                        </div>
                    </div>
                    <div>
                        <Label htmlFor="certifications">{t("verification.certifications")}</Label>
                        <Textarea id="certifications" value={formData.certifications} onChange={(e) => handleInputChange('certifications', e.target.value)} placeholder={t("verification.certificationsPlaceholder")} rows={3} />
                        {errors.certifications && <p className="text-red-500 text-sm mt-1">{errors.certifications}</p>}
                    </div>
                    <div>
                        <Label htmlFor="professional_memberships">{t("verification.professionalMemberships")}</Label>
                        <Textarea id="professional_memberships" value={formData.professional_memberships} onChange={(e) => handleInputChange('professional_memberships', e.target.value)} placeholder={t("verification.membershipsPlaceholder")} rows={2} />
                        {errors.professional_memberships && <p className="text-red-500 text-sm mt-1">{errors.professional_memberships}</p>}
                    </div>
                    <div>
                        <Label htmlFor="languages_spoken">{t("verification.languagesSpoken")}</Label>
                        <Input id="languages_spoken" value={formData.languages_spoken} onChange={(e) => handleInputChange('languages_spoken', e.target.value)} placeholder={t("verification.languagesPlaceholder")} />
                        {errors.languages_spoken && <p className="text-red-500 text-sm mt-1">{errors.languages_spoken}</p>}
                    </div>
                </div>
              </section>

              <section>
                <h3 className="text-lg font-semibold mb-4 border-b pb-2">{t("verification.documents")}</h3>
                <div className="space-y-4">
                  <div className="p-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-2 text-right">{t("verification.uploadAllDocuments")}</h4>
                    <p className="text-gray-500 mb-4 text-right">{t("verification.documentsInstructions")}</p>
                    <div 
                      className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors"
                      onDragOver={(e) => e.preventDefault()}
                      onDrop={handleFileDrop}
                      onDragEnter={(e) => e.preventDefault()}
                      onDragLeave={(e) => e.preventDefault()}
                    >
                      <Upload className="h-8 w-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600 mb-2">{t("verification.dragAndDrop")}</p>
                      <Label htmlFor="document_upload" className="cursor-pointer">
                        <Button variant="outline" asChild>
                           <span>{t("verification.chooseFiles")}</span>
                        </Button>
                      </Label>
                      <Input id="document_upload" type="file" accept=".pdf,.jpg,.png" multiple className="sr-only" onChange={handleFileChange} />
                      <p className="text-xs text-gray-500 mt-2 text-center">{t("verification.supportedFormats")}</p>
                    </div>
                    <div className="mt-4 space-y-2">
                      {filesToUpload.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">{file.name}</span>
                          <Button variant="ghost" size="sm" onClick={() => removeFile(index)}>X</Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </section>

              <div className="flex justify-center pt-6">
                <Button type="submit" disabled={submitting} className="bg-black text-white hover:bg-gray-800 px-8 py-3 text-lg">
                  {submitting ? (
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" />{t("verification.submitting")}</>
                  ) : (
                    t("verification.submitForReview")
                  )}
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}