"use client";

import { useTranslation } from "@/components/i18n-provider";
import { usePageTitle } from "@/components/page-title-provider";
import { useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Users, Activity, UserCheck, DollarSign, BarChart3  } from "lucide-react";
import AnalyticsChart from "@/components/dashboard/analytics-chart";

export default function AnalyticsPage() {
  const { t } = useTranslation();
  const { setTitle } = usePageTitle();

  useEffect(() => {
    setTitle("analyticsPage.title");
  }, [setTitle, t]);
  // NOTE: In a real app, chartData would be fetched in a useEffect hook.
  // For this translation exercise, we'll assume it's available.
  interface ChartDataPoint {
    date: string;
    count: number;
  }
  const chartData: ChartDataPoint[] = [];

  const metrics = [
    {
      title: t("analyticsPage.totalExperts"),
      value: "247",
      icon: <UserCheck className="h-4 w-4 text-muted-foreground" />,
      change: `+12% ${t("analyticsPage.fromLastMonth")}`,
      changeType: "positive" as const,
    },
    {
      title: t("analyticsPage.totalFarmers"),
      value: "1,234",
      icon: <Users className="h-4 w-4 text-muted-foreground" />,
      change: `+23% ${t("analyticsPage.fromLastMonth")}`,
      changeType: "positive" as const,
    },
    {
      title: t("analyticsPage.totalRevenue"),
      value: "$12,345",
      icon: <DollarSign className="h-4 w-4 text-muted-foreground" />,
      change: `+5% ${t("analyticsPage.fromLastMonth")}`,
      changeType: "positive" as const,
    },
    {
      title: t("analyticsPage.consultations"),
      value: "89",
      icon: <Activity className="h-4 w-4 text-muted-foreground" />,
      change: `-2% ${t("analyticsPage.fromYesterday")}`,
      changeType: "negative" as const,
    },
  ];

  return (
    <div className="p-6">
      {/* Page Header */}
      <div className="mb-8">
        <p className="text-muted-foreground">
          {t("analyticsPage.description")}
        </p>
      </div>

      {/* Analytics Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 rtl:flex-row-reverse">
              <CardTitle className="text-sm font-medium">
                {metric.title}
              </CardTitle>
              <div className="h-4 w-4 text-muted-foreground">{metric.icon}</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <p
                className={`text-xs text-muted-foreground ${
                  metric.changeType === "positive"
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                {metric.change}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Analytics Charts Section */}
      <div className="grid gap-6 md:grid-cols-2 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 rtl:flex-row-reverse">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              {t("analyticsPage.userActivityTitle")}
            </CardTitle>
            <CardDescription>
              {t("analyticsPage.userActivityDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* TODO: The AnalyticsChart component needs to be adapted for RTL.
                This may involve reversing the x-axis, adjusting tooltips,
                and ensuring labels are correctly positioned. */}
            <AnalyticsChart data={chartData} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
