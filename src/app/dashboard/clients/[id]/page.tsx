"use client";

import { useEffect, useState } from "react";
import { supabase } from "@/supabase/client";
import { useParams, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslation } from "@/components/i18n-provider";
import { usePageTitle } from "@/components/page-title-provider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Building,
  ArrowLeft,
  Factory,
  Tractor,
} from "lucide-react";

interface ClientProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  role: string;
  account_activated: boolean;
  created_at: string;
  updated_at: string;
  client_type?: string;
}

interface Asset {
  id: string;
  name: string;
  asset_type: string;
  location_address?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export default function ClientDetailPage() {
  const params = useParams();
  const { id } = params;
  const { t, locale } = useTranslation();
  const { setTitle } = usePageTitle();
  const router = useRouter();
  const [client, setClient] = useState<ClientProfile | null>(null);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);
  const [assetsLoading, setAssetsLoading] = useState(false);

  useEffect(() => {
    if (id) {
      const fetchClient = async () => {
        setLoading(true);
        try {
          // Fetch client profile
          const { data: profileData, error: profileError } = await supabase
            .from("profiles")
            .select("*")
            .eq("id", id)
            .maybeSingle();

          if (profileError) {
            console.error("Error fetching client details:", profileError);
            return;
          }

          if (!profileData) {
            console.error("Client not found");
            return;
          }

          setClient(profileData);
          setTitle(`${profileData.first_name} ${profileData.last_name}`);

          // Fetch associated assets
          setAssetsLoading(true);
          const { data: assetsData, error: assetsError } = await supabase
            .from("assets")
            .select("*")
            .eq("owner_id", id)
            .order("created_at", { ascending: false });

          if (assetsError) {
            console.error("Error fetching assets:", assetsError);
          } else {
            setAssets(assetsData || []);
          }
          setAssetsLoading(false);
        } catch (error) {
          console.error("Error in fetchClient:", error);
        } finally {
          setLoading(false);
        }
      };
      fetchClient();
    }
  }, [id, setTitle]);

  const getAssetIcon = (assetType: string) => {
    switch (assetType?.toLowerCase()) {
      case 'farm':
        return <Tractor className="h-5 w-5 text-green-600" />;
      case 'factory':
        return <Factory className="h-5 w-5 text-blue-600" />;
      default:
        return <Building className="h-5 w-5 text-gray-600" />;
    }
  };

  const getAssetStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return <Badge variant="default">Approved</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  if (!client) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">
              {t("clientDetailPage.notFound") || "Client Not Found"}
            </h2>
            <p className="text-gray-600 mb-4">
              {t("clientDetailPage.notFoundDescription") || "The requested client could not be found."}
            </p>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("clientDetailPage.backButton") || "Go Back"}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir={locale === "ar" ? "rtl" : "ltr"}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t("clientDetailPage.backButton") || "Back"}
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {client.first_name} {client.last_name}
            </h1>
            <p className="text-gray-600">{client.email}</p>
          </div>
        </div>
        <Badge variant={client.account_activated ? "default" : "secondary"}>
          {client.account_activated 
            ? (t("clientDetailPage.activeStatus") || "Active")
            : (t("clientDetailPage.inactiveStatus") || "Inactive")
          }
        </Badge>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList>
          <TabsTrigger value="profile">{t("clientDetailPage.profileTab") || "Profile"}</TabsTrigger>
          <TabsTrigger value="assets">
            {t("clientDetailPage.assetsTab") || "Assets"} ({assets.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  {t("clientDetailPage.personalInfoTitle") || "Personal Information"}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">
                      {t("clientDetailPage.emailLabel") || "Email"}
                    </p>
                    <p className="font-medium">{client.email}</p>
                  </div>
                </div>

                {client.phone_number && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">
                        {t("clientDetailPage.phoneLabel") || "Phone"}
                      </p>
                      <p className="font-medium">{client.phone_number}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">
                      {t("clientDetailPage.joinedLabel") || "Joined"}
                    </p>
                    <p className="font-medium">{formatDate(client.created_at)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">
                      {t("clientDetailPage.roleLabel") || "Role"}
                    </p>
                    <p className="font-medium capitalize">{client.role}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Account Status */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {t("clientDetailPage.accountStatusTitle") || "Account Status"}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {t("clientDetailPage.accountActivated") || "Account Activated"}
                  </span>
                  <Badge variant={client.account_activated ? "default" : "secondary"}>
                    {client.account_activated ? "Yes" : "No"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {t("clientDetailPage.totalAssets") || "Total Assets"}
                  </span>
                  <span className="font-medium">{assets.length}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {t("clientDetailPage.lastUpdated") || "Last Updated"}
                  </span>
                  <span className="font-medium">{formatDate(client.updated_at)}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="assets">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                {t("clientDetailPage.assetsTitle") || "Associated Assets"}
              </CardTitle>
              <CardDescription>
                {t("clientDetailPage.assetsDescription") || "All assets owned or managed by this client"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {assetsLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
              ) : assets.length === 0 ? (
                <div className="text-center py-8">
                  <Building className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {t("clientDetailPage.noAssetsTitle") || "No Assets"}
                  </h3>
                  <p className="text-gray-600">
                    {t("clientDetailPage.noAssetsDescription") || "This client hasn't registered any assets yet."}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {assets.map((asset) => (
                    <div
                      key={asset.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          {getAssetIcon(asset.asset_type)}
                          <div>
                            <h4 className="font-medium">{asset.name}</h4>
                            <p className="text-sm text-gray-600 capitalize">
                              {asset.asset_type}
                            </p>
                            {asset.location_address && (
                              <div className="flex items-center gap-1 mt-1">
                                <MapPin className="h-3 w-3 text-gray-400" />
                                <span className="text-xs text-gray-500">
                                  {asset.location_address}
                                </span>
                              </div>
                            )}
                            <p className="text-xs text-gray-400 mt-1">
                              {t("clientDetailPage.createdOn") || "Created on"} {formatDate(asset.created_at)}
                            </p>
                          </div>
                        </div>
                        {getAssetStatusBadge(asset.status)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
