"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { updateProfileAction, uploadAvatarAction } from "@/app/actions";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useEffect, useState, useRef } from "react";
import { useTranslation } from "@/components/i18n-provider";
import { usePageTitle } from "@/components/page-title-provider";
import { useUserProfile } from "@/hooks/use-user-profile";
import { Skeleton } from "@/components/ui/skeleton";


export default function SettingsForm() {
  const { user, profile, expertProfile, loading } = useUserProfile();
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { locale, setLocale, t } = useTranslation();
  const { setTitle } = usePageTitle();

  useEffect(() => {
    setTitle("settingsPage.title");
  }, [setTitle]);

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setAvatarPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="container mx-auto">
          <div className="space-y-6">
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="container mx-auto">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("settingsPage.languageCardTitle")}</CardTitle>
              <CardDescription>
                {t("settingsPage.languageCardDescription")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-4 rtl:space-x-reverse">
                <Button
                  variant={locale === "en" ? "default" : "outline"}
                  onClick={() => setLocale("en")}
                >
                  English
                </Button>
                <Button
                  variant={locale === "ar" ? "default" : "outline"}
                  onClick={() => setLocale("ar")}
                >
                  عربي
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Add Verification Status Card */}
          {user?.user_metadata?.role === "expert" && (
            <Card>
              <CardHeader>
                <CardTitle>{t("settingsPage.verificationCardTitle")}</CardTitle>
                <CardDescription>
                  {t("settingsPage.verificationCardDescription")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="flex-1">
                    <p className="font-medium">{t("settingsPage.statusLabel")}</p>
                    <p
                      className={
                        expertProfile?.verification_status === "approved"
                          ? "text-sm text-green-600"
                          : "text-sm text-amber-600"
                      }
                    >
                      {t(
                        expertProfile?.verification_status === "approved"
                          ? "settingsPage.verifiedStatus"
                          : expertProfile?.verification_status === "rejected"
                          ? "settingsPage.rejectedStatus"
                          : "settingsPage.pendingStatus"
                      )}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>{t("settingsPage.pictureCardTitle")}</CardTitle>
              <CardDescription>
                {t("settingsPage.pictureCardDescription")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <Avatar className="h-20 w-20 border">
                  <AvatarImage
                    src={
                      avatarPreview || profile?.profile_picture_url || ""
                    }
                    alt="Profile avatar"
                  />
                  <AvatarFallback>
                    {profile?.first_name?.charAt(0) ||
                      user?.email?.charAt(0) ||
                      "U"}
                  </AvatarFallback>
                </Avatar>
                <form
                  action={uploadAvatarAction}
                  className="flex items-center space-x-2 rtl:space-x-reverse"
                  encType="multipart/form-data"
                >
                  <Input
                    type="file"
                    name="avatar"
                    id="avatar"
                    ref={fileInputRef}
                    accept="image/*"
                    className="max-w-xs"
                    onChange={handleAvatarChange}
                  />
                  <Button type="submit">{t("settingsPage.uploadAction")}</Button>
                </form>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t("settingsPage.personalInfoCardTitle")}</CardTitle>
              <CardDescription>
                {t("settingsPage.personalInfoCardDescription")}
              </CardDescription>
            </CardHeader>
            <form action={updateProfileAction}>
              <CardContent className="space-y-4">
                {/* Add Verification Status field for admins */}
                {user?.user_metadata?.role === "admin" && (
                  <div className="space-y-2">
                    <Label htmlFor="verification_status">
                      {t("settingsPage.expertVerificationLabel")}
                    </Label>
                    <select
                      id="verification_status"
                      name="verification_status"
                      defaultValue={expertProfile?.verification_status || "pending"}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="pending">{t("settingsPage.pendingStatus")}</option>
                      <option value="approved">{t("settingsPage.approvedStatus")}</option>
                      <option value="rejected">{t("settingsPage.rejectedStatus")}</option>
                    </select>
                  </div>
                )}

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="full_name">{t("settingsPage.fullNameLabel")}</Label>
                    <Input
                      id="full_name"
                      name="full_name"
                      defaultValue={`${profile?.first_name || ""} ${
                        profile?.last_name || ""
                      }`.trim()}
                      placeholder={t("settingsPage.fullNamePlaceholder")}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">{t("settingsPage.emailLabel")}</Label>
                    <Input
                      id="email"
                      name="email"
                      defaultValue={profile?.email || user?.email || ""}
                      placeholder={t("settingsPage.emailPlaceholder")}
                      disabled
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="role">{t("settingsPage.roleLabel")}</Label>
                    <select
                      id="role"
                      name="role"
                      defaultValue={user?.user_metadata?.role || "farmer"}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="admin">{t("settingsPage.administratorRole")}</option>
                      <option value="expert">{t("settingsPage.expertRole")}</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">{t("settingsPage.locationLabel")}</Label>
                    <Input
                      id="location"
                      name="location"
                      defaultValue={user?.user_metadata?.location || ""}
                      placeholder={t("settingsPage.locationPlaceholder")}
                    />
                  </div>
                </div>
                {user?.user_metadata?.role === "admin" && (
                  <div className="space-y-2">
                    <Label htmlFor="admin_details">{t("settingsPage.adminDetailsLabel")}</Label>
                    <textarea
                      id="admin_details"
                      name="admin_details"
                      defaultValue={
                        user?.user_metadata?.admin_details || ""
                      }
                      placeholder={t("settingsPage.adminDetailsPlaceholder")}
                      className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    />
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button type="submit">{t("settingsPage.saveChangesAction")}</Button>
              </CardFooter>
            </form>
          </Card>
        </div>
      </div>
    </div>
  );
}
