"use client";

import { useEffect, useState } from "react";
import { supabase } from "@/supabase/client";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { signOutAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { QrCode, Smartphone, Download } from "lucide-react";
import Navbar from "@/components/navbar";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function MobileAppPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [message] = useState<Message | null>(null);

  useEffect(() => {
    async function checkUser() {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          router.push("/sign-in");
          return;
        }

        // Check if user is a farmer
        if (user.user_metadata?.role !== "farmer") {
          router.push("/dashboard");
          return;
        }

        setLoading(false);
      } catch (error) {
        console.error("Error checking user:", error);
        router.push("/sign-in");
      }
    }

    checkUser();
  }, [router]);

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        Loading...
      </div>
    );
  }

  return (
    <>
      <Navbar />
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-3xl">
          <h1 className="text-3xl font-bold text-center mb-8">
            Welcome to AgriConnect Mobile
          </h1>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Download Our Mobile App</CardTitle>
                <CardDescription>
                  Get the full AgriConnect experience on your mobile device
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                <div className="bg-gray-100 p-8 rounded-lg mb-6">
                  <QrCode className="h-32 w-32 text-green-700" />
                </div>
                <p className="text-center text-sm text-muted-foreground mb-4">
                  Scan this QR code with your mobile device to download the app
                </p>
              </CardContent>
              <CardFooter className="flex justify-center gap-4">
                <Button className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  App Store
                </Button>
                <Button className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Google Play
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Why Use Our Mobile App?</CardTitle>
                <CardDescription>
                  Enhanced features for farmers on the go
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <Smartphone className="h-5 w-5 text-green-600 mt-0.5" />
                    <span>Real-time notifications for expert advice</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Smartphone className="h-5 w-5 text-green-600 mt-0.5" />
                    <span>Offline access to agricultural resources</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Smartphone className="h-5 w-5 text-green-600 mt-0.5" />
                    <span>Field data collection tools</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Smartphone className="h-5 w-5 text-green-600 mt-0.5" />
                    <span>Direct messaging with agricultural experts</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Smartphone className="h-5 w-5 text-green-600 mt-0.5" />
                    <span>Weather alerts and forecasts</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <form action={async () => {
                  await signOutAction();
                }} className="w-full">
                  <SubmitButton className="w-full">Sign Out</SubmitButton>
                </form>
              </CardFooter>
            </Card>
          </div>

          {message && <FormMessage message={message} />}
        </div>
      </div>
    </>
  );
}
