"use client";

import React from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { 
  Tabs, 
  <PERSON><PERSON><PERSON>ontent, 
  Ta<PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Leaf, Check, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { useTheme } from "next-themes";

// Component Types
type ColorSwatchProps = {
  color: string;
  name: string;
  hexCode: string;
  className?: string;
};

type ComponentExampleProps = {
  title: string;
  children: React.ReactNode;
  description?: string;
};

// Animations
const fadeIn = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.4 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

// Sub-components
const ColorSwatch: React.FC<ColorSwatchProps> = ({ color, name, hexCode, className }) => (
  <div className={cn("flex flex-col gap-2", className)}>
    <div 
      className="h-24 w-full rounded-md shadow-sm" 
      style={{ backgroundColor: color }}
    />
    <div className="text-sm font-medium">{name}</div>
    <div className="text-xs text-muted-foreground">{hexCode}</div>
  </div>
);

const ComponentExample: React.FC<ComponentExampleProps> = ({ title, description, children }) => (
  <Card className="overflow-hidden">
    <CardHeader className="bg-muted/50 p-4">
      <CardTitle className="text-base font-medium">{title}</CardTitle>
      {description && <p className="text-sm text-muted-foreground">{description}</p>}
    </CardHeader>
    <CardContent className="p-6 flex items-center justify-center bg-background border-t">
      {children}
    </CardContent>
  </Card>
);

const StyleGuidePage: NextPage = () => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // Design tokens based on Vertical Farm branding
  const tokens = {
    colors: {
      primary: "#d2e5a4",
      accent: "#e0f5b1",
      dark: "#121212",
      white: "#ffffff"
    },
    spacing: {
      xs: "0.5rem", // 8px
      sm: "1rem",   // 16px
      md: "1.5rem", // 24px
      lg: "2rem",   // 32px
      xl: "3rem"    // 48px
    },
    borderRadius: {
      sm: "0.25rem", // 4px
      md: "0.5rem",  // 8px
      lg: "0.75rem", // 12px
      pill: "9999px" // Pill shape
    }
  };

  return (
    <>
      <Head>
        <title>Vertical Farm Style Guide</title>
        <meta name="description" content="Design system and style guide for Vertical Farm" />
      </Head>

      <div className="container mx-auto py-12 max-w-7xl">
        <motion.div 
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          className="space-y-12"
        >
          {/* Header */}
          <div className="space-y-4">
            <h1 className="text-4xl font-bold tracking-tight">Vertical Farm Style Guide</h1>
            <p className="text-lg text-muted-foreground max-w-3xl">
              This style guide outlines the design elements and components used throughout the Vertical Farm website to maintain consistency and cohesiveness.
            </p>
          </div>

          {/* Tabs for Style Guide Sections */}
          <Tabs defaultValue="brand" className="w-full">
            <TabsList className="grid grid-cols-3 md:grid-cols-4 mb-8">
              <TabsTrigger value="brand">Brand</TabsTrigger>
              <TabsTrigger value="colors">Colors</TabsTrigger>
              <TabsTrigger value="typography">Typography</TabsTrigger>
              <TabsTrigger value="components">Components</TabsTrigger>
              <TabsTrigger value="layout" className="hidden md:block">Layout</TabsTrigger>
              <TabsTrigger value="usage" className="hidden md:block">Usage</TabsTrigger>
            </TabsList>

            {/* Brand Identity */}
            <TabsContent value="brand" className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Brand Identity</CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-full border-2 flex items-center justify-center" style={{ borderColor: tokens.colors.primary }}>
                      <Leaf size={24} />
                    </div>
                    <span className="text-2xl font-medium">Vertical Farm</span>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Mission Statement</h3>
                    <p className="text-muted-foreground">
                      Our vertical farming startup brings sustainable, locally grown produce to urban areas. 
                      With our innovative technology, we&apos;re revolutionizing the way we grow and consume fresh food.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Color Palette */}
            <TabsContent value="colors">
              <Card>
                <CardHeader>
                  <CardTitle>Color Palette</CardTitle>
                </CardHeader>
                <CardContent>
                  <motion.div 
                    variants={staggerContainer}
                    initial="hidden"
                    animate="visible"
                    className="grid grid-cols-2 md:grid-cols-4 gap-6"
                  >
                    <motion.div variants={fadeIn}>
                      <ColorSwatch color={tokens.colors.primary} name="Primary Green" hexCode="#d2e5a4" />
                    </motion.div>
                    <motion.div variants={fadeIn}>
                      <ColorSwatch color={tokens.colors.accent} name="Light Green/Accent" hexCode="#e0f5b1" />
                    </motion.div>
                    <motion.div variants={fadeIn}>
                      <ColorSwatch color={tokens.colors.dark} name="Dark Background" hexCode="#121212" />
                    </motion.div>
                    <motion.div variants={fadeIn}>
                      <ColorSwatch color={tokens.colors.white} name="White" hexCode="#ffffff" className={isDark ? "border border-border" : ""} />
                    </motion.div>
                  </motion.div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Typography */}
            <TabsContent value="typography">
              <Card>
                <CardHeader>
                  <CardTitle>Typography</CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Headings</h3>
                    <div className="space-y-4">
                      <div>
                        <h1 className="text-4xl font-bold">Heading 1 (2.5rem)</h1>
                        <p className="text-sm text-muted-foreground mt-1">font-bold text-4xl</p>
                      </div>
                      <div>
                        <h2 className="text-3xl font-bold">Heading 2 (1.75rem)</h2>
                        <p className="text-sm text-muted-foreground mt-1">font-bold text-3xl</p>
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold">Heading 3 (1.25rem)</h3>
                        <p className="text-sm text-muted-foreground mt-1">font-bold text-2xl</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Body Text</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="text-base">
                          Regular paragraph text (1rem). Our vertical farming startup brings sustainable, locally grown produce to urban areas. 
                          With our innovative technology, we&apos;re revolutionizing the way we grow and consume fresh food.
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">text-base</p>
                      </div>
                      <div>
                        <p className="text-base">
                          Text with <span className="font-bold">bold emphasis</span> for important information.
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">font-bold for emphasis</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Components */}
            <TabsContent value="components" className="space-y-6">
              <motion.div 
                variants={staggerContainer}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 md:grid-cols-2 gap-6"
              >
                <motion.div variants={fadeIn}>
                  <ComponentExample title="Buttons" description="Primary and secondary button styles with hover effects">
                    <div className="flex flex-col gap-4 w-full items-center">
                      <Button className="inline-block px-5 py-2.5 rounded-[30px] font-medium text-center border-none transition-all duration-300 ease-in-out bg-[#d2e5a4] text-[#121212] hover:bg-[#c3da8a]">
                        Start Free Trial
                      </Button>
                      <Button className="inline-block px-5 py-2.5 rounded-[30px] font-medium text-center transition-all duration-300 ease-in-out bg-[#121212] text-white border border-white hover:bg-[#2a2a2a]">
                        HOW IT WORKS?
                      </Button>
                    </div>
                  </ComponentExample>
                </motion.div>

                <motion.div variants={fadeIn}>
                  <ComponentExample title="Step Indicators">
                    <div className="rounded-lg p-4 flex items-center gap-3 w-full" style={{backgroundColor: tokens.colors.accent}}>
                      <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center font-semibold">
                        1
                      </div>
                      <div>Choose the right crops</div>
                    </div>
                  </ComponentExample>
                </motion.div>

                <motion.div variants={fadeIn}>
                  <ComponentExample title="Navigation">
                    <div className="flex gap-6 flex-wrap">
                      <a href="#" className="font-medium hover:text-primary">Solutions</a>
                      <div className="flex items-center gap-1 font-medium">
                        <span>Department</span>
                        <ChevronDown size={16} />
                      </div>
                      <a href="#" className="font-medium hover:text-primary">Farming Method</a>
                      <a href="#" className="font-medium hover:text-primary">Company</a>
                      <a href="#" className="font-medium hover:text-primary">About</a>
                    </div>
                  </ComponentExample>
                </motion.div>

                <motion.div variants={fadeIn}>
                  <ComponentExample title="Hero Section" description="Dark background section with highlighted text">
                    <div className="bg-[#121212] text-white p-8 rounded-md w-full">
                      <div className="text-3xl font-bold leading-tight">
                        Fresh, Sustainable, Plant <br />
                        Grown Vertically in <span className="inline-block px-[10px] py-[2px] rounded-sm bg-[#d2e5a4] text-[#121212]">Urban Areas</span>
                      </div>
                    </div>
                  </ComponentExample>
                </motion.div>

                <motion.div variants={fadeIn} className="md:col-span-2">
                  <ComponentExample title="Feature Cards" description="Cards with icons and uppercase labels">
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 w-full">
                      <div className="rounded-md p-6 bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] flex flex-col items-center text-center">
                        <div className="w-10 h-10 rounded-sm flex items-center justify-center bg-[#d2e5a4]">
                          <Check size={20} />
                        </div>
                        <h3 className="mt-3 font-medium uppercase">VERTICAL FARMING</h3>
                      </div>
                      
                      <div className="rounded-md p-6 bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] flex flex-col items-center text-center">
                        <div className="w-10 h-10 rounded-sm flex items-center justify-center bg-[#d2e5a4]">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 4V20M12 4L6 10M12 4L18 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <h3 className="mt-3 font-medium uppercase">IT&apos;S PERFECT METHOD</h3>
                      </div>
                      
                      <div className="rounded-md p-6 bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] flex flex-col items-center text-center">
                        <div className="w-10 h-10 rounded-sm flex items-center justify-center bg-[#d2e5a4]">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 12V22L12 17L4 22V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M12 2C13.5 3.5 16 5 20 5C20 8 18 10 16 11.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M12 2C10.5 3.5 8 5 4 5C4 8 6 10 8 11.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <h3 className="mt-3 font-medium uppercase">TECH</h3>
                      </div>
                    </div>
                  </ComponentExample>
                </motion.div>
              </motion.div>
            </TabsContent>

            {/* Layout */}
            <TabsContent value="layout">
              <Card>
                <CardHeader>
                  <CardTitle>Layout Guidelines</CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Spacing</h3>
                    <div className="space-y-2">
                      <p><span className="font-medium">Extra small (8px):</span> Minor spacing between related elements</p>
                      <p><span className="font-medium">Small (16px):</span> Default spacing between UI elements</p>
                      <p><span className="font-medium">Medium (24px):</span> Spacing between distinct sections</p>
                      <p><span className="font-medium">Large (32px):</span> Major section divisions</p>
                      <p><span className="font-medium">Extra large (48px):</span> Page-level spacing</p>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Border Radius</h3>
                    <div className="space-y-2">
                      <p><span className="font-medium">Small (4px):</span> Icons, badges</p>
                      <p><span className="font-medium">Medium (8px):</span> Cards, buttons</p>
                      <p><span className="font-medium">Large (12px):</span> Modal windows, major containers</p>
                      <p><span className="font-medium">Pill (30px+):</span> Action buttons</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Visual Representation</h3>
                    <div className="grid grid-cols-5 gap-2">
                      <div className="aspect-square rounded-sm bg-muted flex items-center justify-center text-xs">xs</div>
                      <div className="aspect-square rounded bg-muted flex items-center justify-center text-xs">sm</div>
                      <div className="aspect-square rounded-md bg-muted flex items-center justify-center text-xs">md</div>
                      <div className="aspect-square rounded-lg bg-muted flex items-center justify-center text-xs">lg</div>
                      <div className="aspect-square rounded-full bg-muted flex items-center justify-center text-xs">pill</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Usage Guidelines */}
            <TabsContent value="usage">
              <Card>
                <CardHeader>
                  <CardTitle>Usage Guidelines</CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Dark Background</h3>
                    <p>The dark background (#121212) should be used for:</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Main banner/hero sections</li>
                      <li>Footer sections</li>
                      <li>Areas where content needs to stand out</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Primary Green</h3>
                    <p>The primary green color (#d2e5a4) should be used for:</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Primary action buttons</li>
                      <li>Highlighted text</li>
                      <li>Icons related to farming and sustainability</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Typography Usage</h3>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Use large, bold headings for main section titles</li>
                      <li>Keep body text clean and legible with sufficient line height</li>
                      <li>For emphasis in dark sections, use the highlight class</li>
                      <li>Use all caps for feature labels and action buttons</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </>
  );
};

export default StyleGuidePage;
