import Footer from "@/components/footer";
import Hero from "@/components/hero";
import Navbar from "@/components/navbar";
import { ArrowUpRight, Leaf, MessageSquare, BarChart3 } from "lucide-react";
import { createClient } from "@/supabase/server";

export default async function Home() {
  const supabase = await createClient();
  await supabase.auth.getUser();

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      <Hero />

      {/* Features Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4 text-green-900">
              Our Services
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              AgriConnect provides innovative solutions to help farmers optimize
              their operations and connect with agricultural experts.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Leaf className="w-8 h-8" />,
                title: "Expert Matching",
                description:
                  "Connect with verified agricultural specialists who understand your specific crop and soil needs. Get personalized advice tailored to your farm's unique challenges.",
              },
              {
                icon: <MessageSquare className="w-8 h-8" />,
                title: "Consultations",
                description:
                  "Schedule virtual or on-site consultations with experts. Discuss issues, share images of your crops, and receive actionable recommendations in real-time.",
              },
              {
                icon: <BarChart3 className="w-8 h-8" />,
                title: "Farm Data Services",
                description:
                  "Upload and analyze your farm data to identify trends and optimization opportunities. Make data-driven decisions to increase yield and reduce resource usage.",
              },
            ].map((feature, index) => (
              <div
                key={index}
                className="p-8 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-green-100 group"
              >
                <div className="text-green-600 mb-6 group-hover:text-lime-500 transition-colors">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3 text-green-800">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-green-800 text-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">5,000+</div>
              <div className="text-green-100">Farmers Connected</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">200+</div>
              <div className="text-green-100">Agricultural Experts</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">30%</div>
              <div className="text-green-100">Average Yield Increase</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4 text-green-900">
            Ready to Grow with Us?
          </h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of farmers who are already optimizing their
            operations and increasing their yields with AgriConnect.
          </p>
          <a
            href="/sign-up"
            className="inline-flex items-center px-8 py-4 text-green-900 bg-lime-400 rounded-lg hover:bg-lime-500 transition-colors font-medium"
          >
            Get Started
            <ArrowUpRight className="ml-2 w-5 h-5" />
          </a>
        </div>
      </section>

      <Footer />
    </div>
  );
}
