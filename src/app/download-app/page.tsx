"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { supabase } from "@/supabase/client";
import { Download, Smartphone } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function DownloadAppPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkAuth() {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          router.push("/sign-in");
          return;
        }
        
        // Check if user is a farmer
        const { data: userRole } = await supabase
          .from("user_roles")
          .select("role_id, roles(name)")
          .eq("user_id", user.id)
          .single();
        
        // Get role name from the joined table data
        const { data: roleData } = await supabase
          .from("roles")
          .select("name")
          .eq("id", userRole?.role_id)
          .single();
        
        const role = roleData?.name;
        
        // If not a farmer, redirect to dashboard
        if (role && role !== "farmer") {
          router.push("/dashboard");
          return;
        }
        
        setLoading(false);
      } catch (error) {
        console.error("Error checking auth:", error);
        router.push("/sign-in");
      }
    }

    checkAuth();
  }, [router]);

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        Loading...
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-green-50 to-green-100 p-4">
      <div className="container max-w-4xl">
        <Card className="overflow-hidden border-none shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-2">
            <div className="bg-gradient-to-br from-green-600 to-green-800 p-8 text-white">
              <div className="space-y-4">
                <h1 className="text-3xl font-bold tracking-tight">AgriConnect Mobile</h1>
                <p className="text-green-100">
                  Get expert agricultural advice, manage your farm, and track your crops all from
                  your smartphone.
                </p>
                
                <div className="pt-4">
                  <h2 className="text-xl font-semibold">Key Features:</h2>
                  <ul className="mt-2 space-y-2 text-green-100">
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-300"></div>
                      Real-time crop monitoring
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-300"></div>
                      Expert consultations on demand
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-300"></div>
                      Weather alerts and forecasts
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-300"></div>
                      Marketplace for crops and supplies
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            
            <CardContent className="flex flex-col items-center justify-center p-8">
              <div className="mb-6 text-center">
                <Smartphone className="mx-auto h-16 w-16 text-green-600" />
                <h2 className="mt-4 text-2xl font-semibold">Download our Mobile App</h2>
                <p className="mt-2 text-muted-foreground">
                  Access all features and services from your mobile device
                </p>
              </div>
              
              <div className="grid w-full gap-4">
                <Button size="lg" className="bg-black text-white hover:bg-gray-800">
                  <svg 
                    className="mr-2 h-5 w-5" 
                    viewBox="0 0 24 24" 
                    fill="currentColor"
                  >
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11Z" />
                  </svg>
                  Download for iOS
                </Button>
                <Button size="lg" className="bg-green-600 hover:bg-green-700">
                  <Download className="mr-2 h-5 w-5" />
                  Download for Android
                </Button>
              </div>
              
              <div className="mt-8 text-center">
                <p className="text-sm text-muted-foreground">
                  By downloading, you agree to our{" "}
                  <a href="#" className="text-primary hover:underline">
                    Terms of Service
                  </a>{" "}
                  and{" "}
                  <a href="#" className="text-primary hover:underline">
                    Privacy Policy
                  </a>
                </p>
              </div>
            </CardContent>
          </div>
        </Card>
      </div>
    </div>
  );
}
