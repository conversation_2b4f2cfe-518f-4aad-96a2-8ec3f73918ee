"use client";

import { signInAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import Navbar from "@/components/navbar";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { AlertCircle, Eye, EyeOff } from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";
import { validateSignInForm, sanitizeInput } from "@/utils/auth-helpers";

import { useAuth } from "@/components/auth-provider";

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

export default function SignInPage(props: { searchParams: Message }) {
  const { user } = useAuth();


  // Redirect if user is already authenticated
  useEffect(() => {
    if (user) {
      console.log("User already authenticated, redirecting to dashboard");
      window.location.href = "/dashboard";
    }
  }, [user]);

  const searchParams = props.searchParams;
  const [message, setMessage] = useState<Message | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const { toast } = useToast();

  useEffect(() => {
    if (
      "message" in searchParams ||
      "error" in searchParams ||
      "success" in searchParams
    ) {
      // Prevent displaying the form message for the specific role mismatch error
      if ("error" in searchParams && searchParams.error === "Authentication failed due to incorrect login view.") {
        // Only show toast for this specific error
        toast({
          variant: "destructive",
          title: "Sign In Failed",
          description: "For admin login choose Admin login",
        });
      } else if ("error" in searchParams && searchParams.error === "email_not_verified") {
        toast({
          variant: "destructive",
          title: "Email Not Verified",
          description: "Please check your email and click the confirmation link before signing in.",
        });
      } else if ("error" in searchParams && searchParams.error === "verification_check_failed") {
        toast({
          variant: "destructive",
          title: "Verification Failed",
          description: "Unable to verify your account status. Please try again.",
        });
      } else if ("error" in searchParams && searchParams.error === "profile_fetch_failed") {
        toast({
          variant: "destructive",
          title: "Profile Error",
          description: "Unable to load your profile. Please try again.",
        });
      } else {
        // For other messages/errors, set the form message and show toast
        setMessage(searchParams);
        if ("error" in searchParams) {
          toast({
            variant: "destructive",
            title: "Sign In Failed",
            description: searchParams.error,
          });
        } else if ("success" in searchParams) {
          toast({
            title: "Success",
            description: searchParams.success,
          });
        }
      }
    }
  }, [searchParams, toast]);

  // Client-side validation using helper function
  const validateForm = (): boolean => {
    const validation = validateSignInForm(formData.email, formData.password);
    setFormErrors(validation.errors);
    return validation.isValid;
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    // Sanitize input for email field
    const sanitizedValue = field === "email" ? sanitizeInput(value) : value;
    setFormData(prev => ({ ...prev, [field]: sanitizedValue }));
    
    // Clear field-specific errors when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Handle form submission
  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (isLoading) return; // Prevent multiple submissions

    setFormErrors({});
    setMessage(null);

    // Basic client-side validation
    if (!validateForm()) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fix the errors below and try again.",
      });
      return;
    }

    setIsLoading(true);

    // Get the form element
    const form = e.currentTarget;
    const formData = new FormData(form);
    formData.set("role", isAdmin ? "admin" : "expert");

    try {
      // Show loading toast with spinner
      toast({
        title: "Signing in...",
        description: "Please wait while we authenticate you...",
      });

      // Call sign-in action
      const response = await signInAction(formData);

      if (response.success) {
        toast({
          title: "Success",
          description: "Sign-in successful! Redirecting...",
        });
        
        // Implement role-based redirection according to authentication plan
        const userRole = response.role;
        const accountActivated = response.accountActivated;
        
        if (userRole === 'admin') {
          // Admins get immediate access to dashboard
          window.location.href = "/dashboard";
        } else if (userRole === 'agriculture_expert' || userRole === 'industrial_expert' || userRole === 'expert') {
          // Expert users - redirect based on account activation status
          if (accountActivated === true) {
            window.location.href = "/dashboard";
          } else {
            window.location.href = "/dashboard/verification-pending";
          }
        } else {
          // Other roles (farmers, etc.) go to dashboard
          window.location.href = "/dashboard";
        }
      } else {
        // Handle server-side errors
        setFormErrors({ general: response.error });
        if (response.error === "Your account is not yet verified. Please check your email for a verification link or contact support.") {
          toast({
            variant: "destructive",
            title: "Verification Pending",
            description: response.error,
          });
        } else {
          toast({
            variant: "destructive",
            title: "Sign In Failed",
            description: response.error || "An unknown error occurred.",
          });
        }
      }
    } catch (error) {
      console.error("Sign in error:", error);
      toast({
        variant: "destructive",
        title: "Sign In Failed",
        description: "An unexpected error occurred. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (message && "message" in message) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={message} />
      </div>
    );
  }

  const toggleLoginType = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsAdmin(!isAdmin);
    setFormErrors({});
    setFormData({ email: "", password: "" });
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <>
      <Navbar />
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
              <form className="flex flex-col space-y-6" onSubmit={handleFormSubmit}>
              {/* Header Section */}
              <div className="space-y-2 text-center">
                <h1 className="text-3xl font-semibold tracking-tight">
                  {isAdmin ? "Admin Sign In" : "Sign In"}
                </h1>
                <p className="text-sm text-muted-foreground">
                  {isAdmin ? (
                    "Administrator access only. No public signup available."
                  ) : (
                    <>
                      Don&apos;t have an account?{" "}
                      <Link
                        className="text-primary font-medium hover:underline transition-all"
                        href="/sign-up"
                      >
                        Sign up
                      </Link>
                    </>
                  )}
                </p>
              </div>

              {/* Form Fields */}
              <div className="space-y-4">
                {/* Email Field */}
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium">
                    {isAdmin ? "Admin Email" : "Email"}
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder={isAdmin ? "<EMAIL>" : "<EMAIL>"}
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className={`w-full ${formErrors.email ? 'border-red-500 focus:border-red-500' : ''}`}
                    disabled={isLoading}
                    autoComplete="email"
                  />
                  {formErrors.email && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {formErrors.email}
                    </p>
                  )}
                </div>

                {/* Password Field */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="password" className="text-sm font-medium">
                      Password
                    </Label>
                    <Link
                      className="text-xs text-muted-foreground hover:text-foreground hover:underline transition-all"
                      href="/forgot-password"
                    >
                      Forgot Password?
                    </Link>
                  </div>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      name="password"
                      placeholder={isAdmin ? "Your admin password" : "Your password"}
                      value={formData.password}
                      onChange={(e) => handleInputChange("password", e.target.value)}
                      className={`w-full pr-10 ${formErrors.password ? 'border-red-500 focus:border-red-500' : ''}`}
                      disabled={isLoading}
                      autoComplete="current-password"
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                  {formErrors.password && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {formErrors.password}
                    </p>
                  )}
                </div>
              </div>

              {/* Hidden Role Field */}
              <input type="hidden" name="role" value={isAdmin ? "admin" : "expert"} />

              {/* Submit Button */}
              <SubmitButton
                className={`w-full ${
                  isAdmin 
                    ? "bg-green-700 hover:bg-green-800 text-white" 
                    : "bg-lime-500 hover:bg-lime-600 text-green-900"
                }`}
                pendingText="Signing in..."
                disabled={isLoading}
              >
                {isAdmin ? "Sign in as Admin" : "Sign in as Expert"}
              </SubmitButton>

              {/* Form Message */}
              {message && <FormMessage message={message} />}
            </form>
        </div>

        {/* Additional Help Section */}
        <div className="mt-6 text-center text-sm text-muted-foreground max-w-md">
          {/* Re-adding toggle login type with modified text */}
          <div className="text-center text-sm text-muted-foreground">
            <span>{isAdmin ? "Expert login - " : "System Admin Login - "} </span>
            <button
              type="button"
              onClick={toggleLoginType}
              className="text-primary font-medium hover:underline transition-all"
              disabled={isLoading}
            >
              Click here
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
