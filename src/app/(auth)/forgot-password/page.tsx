'use client'

import { useState, useTransition } from 'react'
import { useSearchParams } from 'next/navigation'
import { SubmitButton } from '@/components/submit-button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { FormMessage } from '@/components/form-message'
import Navbar from '@/components/navbar'

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [isPending, startTransition] = useTransition()

  const searchParams = useSearchParams()

  // Check for error from set-password page
  const errorParam = searchParams.get('error')
  if (errorParam && !error) {
    setError(decodeURIComponent(errorParam))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setMessage('')

    startTransition(async () => {
      try {
        const response = await fetch('/api/reset-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email }),
        })

        const data = await response.json()

        if (!response.ok) {
          console.error('Password reset error:', data.error)
          throw new Error(data.error || 'Failed to send reset email')
        }

        setMessage(data.message)
        setEmail('') // Clear email after successful request
      } catch (err) {
        console.error('Password reset error:', err)
        const errorMessage = err instanceof Error ? err.message : 'Something went wrong'
        setError(errorMessage)
      }
    })
  }

  return (
    <>
      <Navbar />
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
          <form onSubmit={handleSubmit} className="flex flex-col space-y-6">
            <div className="space-y-2 text-center">
              <h1 className="text-3xl font-semibold tracking-tight">Reset your password</h1>
              <p className="text-sm text-muted-foreground">
                Enter your email address and we&apos;ll send you a link to reset your password.
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={isPending}
                  className="bg-background"
                />
              </div>
            </div>

            {error && <FormMessage message={{ error }} />}
            {message && (
              <div className="rounded-lg bg-green-50 p-4">
                <p className="text-sm text-green-800">{message}</p>
              </div>
            )}

            <SubmitButton
              pendingText="Sending reset link..."
              className="w-full bg-primary text-primary-foreground"
              disabled={isPending}
            >
              Send reset link
            </SubmitButton>
          </form>
        </div>

        <div className="mt-6 text-center text-sm text-muted-foreground">
          <p>Remember your password? <a href="/sign-in" className="text-primary hover:underline">Sign in</a></p>
        </div>
      </div>
    </>
  )
}
