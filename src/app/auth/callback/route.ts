import { createClient } from "@/supabase/server";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";

export async function GET(request: Request) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  const next = requestUrl.searchParams.get("next");
  const cookieStore = cookies();

  // Log request details for debugging
  console.log("Auth callback:", {
    code: code ? "exists" : "missing",
    next,
    cookies: cookieStore.getAll().map(c => ({ name: c.name, value: c.value.substring(0, 10) + "..." }))
  });

  if (!code) {
    console.error("Auth callback: Missing code parameter");
    return NextResponse.redirect(
      new URL("/sign-in?error=missing_code", requestUrl.origin)
    );
  }

  // Get code verifier from cookie
  const projectId = process.env.NEXT_PUBLIC_SUPABASE_PROJECT_ID;
  const codeVerifierCookie = cookieStore.get(`sb-${projectId}-auth-token-code-verifier`);
  
  console.log("Auth callback: Code verifier cookie:", {
    name: `sb-${projectId}-auth-token-code-verifier`,
    exists: !!codeVerifierCookie,
    value: codeVerifierCookie ? "exists" : "missing"
  });

  const supabase = await createClient();

  try {
    // Exchange the code for a session
    const { data, error } = await supabase.auth.exchangeCodeForSession(code);

    if (error) {
      console.error("Auth callback error:", error);
      return NextResponse.redirect(
        new URL(
          "/sign-in?error=" + encodeURIComponent(error.message),
          requestUrl.origin
        )
      );
    }

    if (!data.session) {
      console.error("Auth callback: No session returned");
      return NextResponse.redirect(
        new URL("/sign-in?error=no_session", requestUrl.origin)
      );
    }

    // Create response with redirect
    const response = next === "/auth/set-password"
      ? NextResponse.redirect(new URL("/auth/set-password?success=true", requestUrl.origin))
      : NextResponse.redirect(new URL(next || "/dashboard", requestUrl.origin));

    // Get all auth-related cookies
    const authCookies = cookieStore.getAll().filter(cookie => 
      cookie.name === `sb-${projectId}-auth-token` ||
      cookie.name === `sb-${projectId}-auth-token-code-verifier` ||
      cookie.name.startsWith(`sb-${projectId}-auth-token.`) ||
      cookie.name.includes('code-verifier') ||
      cookie.name.includes('flow-state')
    );

    // Copy all auth cookies to the response with proper options
    authCookies.forEach(cookie => {
      response.cookies.set({
        name: cookie.name,
        value: cookie.value,
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        httpOnly: true,
        maxAge: 60 * 60 // 1 hour
      });
    });

    // Log response cookies for debugging
    console.log("Auth callback: Response cookies set:", response.cookies.getAll().map(c => c.name));

    return response;
  } catch (error) {
    console.error("Auth callback: Unexpected error", error);
    return NextResponse.redirect(
      new URL("/sign-in?error=unexpected_error", requestUrl.origin)
    );
  }
}
