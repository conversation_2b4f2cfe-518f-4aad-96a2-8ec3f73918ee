"use client";

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/supabase/client';
import { Session, User } from '@supabase/supabase-js';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import Navbar from "@/components/navbar";
import { Eye, EyeOff, AlertCircle } from "lucide-react";
import { PageLoadingSpinner } from "@/components/loading-spinner";
import { DebugInfo, useDebugMode } from "@/components/debug-info";

interface FormErrors {
  password?: string;
  confirmPassword?: string;
  general?: string;
}

export default function SetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isProcessingCode, setIsProcessingCode] = useState(true);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const isDebugMode = useDebugMode();

  useEffect(() => {
    let mounted = true;
    const code = searchParams.get('code');
    const type = searchParams.get('type');
    const redirect_to = searchParams.get('redirect_to');
    
    // Debug URL parameters
    console.log('Set-password page loaded with URL params:', {
      code: code ? 'present' : 'missing',
      type,
      redirect_to,
      fullURL: window.location.href,
      searchParams: Object.fromEntries(searchParams.entries())
    });
    
    // Add timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (mounted && isProcessingCode) {
        console.log('Authentication timeout - redirecting to sign-in');
        console.log('Final state before timeout:', { 
          hasCode: Boolean(code), 
          hasSession: Boolean(session),
          hasUser: Boolean(user),
          isProcessingCode 
        });
        toast({
          title: 'Authentication Timeout',
          description: 'The verification process took too long. Please try again.',
          variant: 'destructive'
        });
        router.push('/sign-in?error=auth_timeout');
      }
    }, 30000); // 30 second timeout
    
    async function handleAuthCode() {
      if (!mounted) return;
      
      setIsProcessingCode(true);
      
      try {
        if (code) {
          console.log('Processing authentication code...');
          // Exchange the code for a session
          const { data, error } = await supabase.auth.exchangeCodeForSession(code);
          
          if (!mounted) return;
          
          if (error) {
            console.error("Error exchanging code for session:", error);
            // Don't immediately redirect on code exchange error - might still have valid session
            console.log('Code exchange failed, will check existing session...');
          } else if (data?.session && data?.user) {
            console.log('Code exchange successful, user authenticated');
            setSession(data.session);
            setUser(data.user);
            setIsProcessingCode(false);
            clearTimeout(timeoutId);
            return;
          }
        }
        
        // If no code or after processing code, check current session
        console.log('Checking current session...');
        const { data: { session: currentSession } } = await supabase.auth.getSession();
        
        if (!mounted) return;
        
        if (!currentSession) {
          console.log('No current session found');
          // If we had a code but it failed, show specific error
          if (code) {
            toast({
              title: 'Authentication Error',
              description: 'Failed to verify your account. Please try again or request a new link.',
              variant: 'destructive'
            });
          }
          clearTimeout(timeoutId);
          router.push('/sign-in');
          return;
        }
        
        console.log('Current session found, getting user...');
        // Get the current user
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        
        if (!mounted) return;
        
        if (!currentUser) {
          console.log('No current user found, redirecting to sign-in');
          clearTimeout(timeoutId);
          router.push('/sign-in');
          return;
        }
        
        console.log('User found, checking if password setup is needed');
        
        // Check if user has already set password
        const { data: profile } = await supabase
          .from('profiles')
          .select('password_set, email_verified')
          .eq('id', currentUser.id)
          .single();
        
        if (profile?.password_set) {
          console.log('User already has password set, redirecting to dashboard');
          clearTimeout(timeoutId);
          router.push('/dashboard');
          return;
        }
        
        console.log('User needs to set password, proceeding with form');
        setSession(currentSession);
        setUser(currentUser);
        clearTimeout(timeoutId);
        
      } catch (err) {
        if (!mounted) return;
        
        console.error('Authentication error:', err);
        toast({
          title: 'Authentication Error',
          description: 'Failed to verify your account. Please try again or request a new link.',
          variant: 'destructive'
        });
        
        clearTimeout(timeoutId);
        router.push('/sign-in');
        return;
      } finally {
        if (mounted) {
          setIsProcessingCode(false);
        }
      }
    }
    
    handleAuthCode();
    
    // Cleanup function
    return () => {
      mounted = false;
      clearTimeout(timeoutId);
    };
  }, [isProcessingCode, router, searchParams, session, toast, user]);

  const validateForm = (): boolean => {
    const errors: FormErrors = {};
    
    if (!password) {
      errors.password = 'Password is required';
    } else if (password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    }
    
    if (!confirmPassword) {
      errors.confirmPassword = 'Please confirm your password';
    } else if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors below and try again.',
        variant: 'destructive'
      });
      return;
    }
    
    setIsLoading(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: password,
      });

      if (error) throw error;

      toast({
        title: 'Success!',
        description: 'Password set successfully. Redirecting to dashboard...',
      });

      // Wait a moment for the database trigger to update password_set
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);

    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred while setting password',
        variant: 'destructive'
      });
      setIsLoading(false);
    }
    // Don't set loading to false on success - let the redirect happen
  };

  const handleInputChange = (field: 'password' | 'confirmPassword', value: string) => {
    if (field === 'password') {
      setPassword(value);
    } else {
      setConfirmPassword(value);
    }
    
    // Clear field-specific errors when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const togglePasswordVisibility = (field: 'password' | 'confirmPassword') => {
    if (field === 'password') {
      setShowPassword(!showPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }
  };

  if (isProcessingCode || !session || !user) {
    return (
      <>
        <Navbar />
        <PageLoadingSpinner 
          text={isProcessingCode ? 'Verifying your account...' : 'Loading...'} 
        />
      </>
    );
  }

  const firstName = user.user_metadata?.first_name || '';
  const lastName = user.user_metadata?.last_name || '';
  const fullName = `${firstName} ${lastName}`.trim() || 'there';

  return (
    <>
      <Navbar />
      <DebugInfo isVisible={isDebugMode} session={session} user={user} />
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
          <form className="flex flex-col space-y-6" onSubmit={handleSetPassword}>
            {/* Header Section */}
            <div className="space-y-2 text-center">
              <h1 className="text-3xl font-semibold tracking-tight">
                Hi, {fullName}
              </h1>
              <p className="text-sm text-muted-foreground">
                Please set your password to complete your account setup
              </p>
            </div>

            {/* Form Fields */}
            <div className="space-y-4">
              {/* Password Field */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium">
                  New Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your new password"
                    value={password}
                    onChange={(e) => handleInputChange("password", e.target.value)}
                    className={`w-full pr-10 ${formErrors.password ? 'border-red-500 focus:border-red-500' : ''}`}
                    disabled={isLoading}
                    minLength={8}
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('password')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {formErrors.password && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {formErrors.password}
                  </p>
                )}
              </div>

              {/* Confirm Password Field */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-sm font-medium">
                  Confirm New Password
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your new password"
                    value={confirmPassword}
                    onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                    className={`w-full pr-10 ${formErrors.confirmPassword ? 'border-red-500 focus:border-red-500' : ''}`}
                    disabled={isLoading}
                    minLength={8}
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('confirmPassword')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                    disabled={isLoading}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {formErrors.confirmPassword && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {formErrors.confirmPassword}
                  </p>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <Button 
              type="submit" 
              disabled={isLoading} 
              className="w-full bg-lime-500 hover:bg-lime-600 text-green-900"
            >
              {isLoading ? 'Setting Password...' : 'Set Password'}
            </Button>
          </form>
        </div>

        {/* Help Text */}
        <div className="mt-6 text-center text-sm text-muted-foreground max-w-md">
          <p>Your password must be at least 8 characters long.</p>
          {isDebugMode && (
            <p className="text-xs text-yellow-600 mt-2">
              🐛 Debug mode active. Press Ctrl+Shift+D to toggle.
            </p>
          )}
        </div>
      </div>
    </>
  );
}
