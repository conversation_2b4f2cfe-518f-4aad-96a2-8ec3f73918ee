"use client";

import { useEffect, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { supabase } from '@/supabase/client';
import { ToastActionElement } from '@/components/ui/toast';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

interface CodeProcessorProps {
  toast: ((options: {
    title: string;
    description: string;
    variant?: 'default' | 'destructive';
    action?: ToastActionElement;
  }) => void);
  router: AppRouterInstance;
}

export default function CodeProcessor({ toast, router }: CodeProcessorProps) {
  const searchParams = useSearchParams();
  const hasProcessedCode = useRef(false);
  
  useEffect(() => {
    const code = searchParams.get('code');
    
    // Only process code once
    if (code && !hasProcessedCode.current) {
      hasProcessedCode.current = true;
      
      const processCode = async () => {
        try {
          await supabase.auth.exchangeCodeForSession(code);
          // Auth provider will handle the state updates
        } catch (error) {
          console.error('Code exchange failed:', error);
          toast({
            title: 'Authentication Error',
            description: 'Failed to verify your account. Please try again.',
            variant: 'destructive'
          });
          router.push('/sign-in');
        }
      };
      
      processCode();
    }
  }, [searchParams, toast, router]);
  
  return null; // This component doesn't render anything
}