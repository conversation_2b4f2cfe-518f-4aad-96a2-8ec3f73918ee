'use client'

import { useState, useEffect, useTransition, Suspense } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/supabase/client'
import { SubmitButton } from '@/components/submit-button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { FormMessage } from '@/components/form-message'
import Navbar from '@/components/navbar'
import { useToast } from "@/components/ui/use-toast";

type PageStatus = 'loading' | 'ready' | 'error'

function SetPasswordComponent() {
  const router = useRouter()
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [status, setStatus] = useState<PageStatus>('loading')
  const [error, setError] = useState<string | null>(null)
  const [isPending, startTransition] = useTransition()
  const { toast } = useToast();

  useEffect(() => {
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setStatus('ready');
      } else {
        setError('No active session found. Please use the link from your email.');
        setStatus('error');
      }
    };
    checkSession();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (password.length < 8) {
      setError('Password must be at least 8 characters long.')
      return
    }
    if (password !== confirmPassword) {
      setError('Passwords do not match.')
      return
    }

    startTransition(async () => {
      setError(null)
      const { error: updateError } = await supabase.auth.updateUser({ password })

      if (updateError) {
        setError(updateError.message)
      } else {
        await supabase.auth.signOut()
        toast({
          title: "Success!",
          description: "Your password has been updated. Please sign in with your new password.",
          duration: 5000,
        });
        router.push('/sign-in')
      }
    })
  }
  
  if (status === 'loading') {
    return (
      <>
        <Navbar />
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-sm text-muted-foreground">Verifying your session...</p>
          </div>
        </div>
      </>
    )
  }

  if (status === 'error') {
    return (
      <>
        <Navbar />
        <div className="flex min-h-screen items-center justify-center text-center">
          <div>
            <h1 className="text-xl font-bold text-destructive">Link Invalid or Session Expired</h1>
            <p className="text-muted-foreground mt-2">{error}</p>
            <button
              onClick={() => router.push('/forgot-password')}
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Request New Reset Link
            </button>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <Navbar />
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
          <form onSubmit={handleSubmit} className="flex flex-col space-y-6">
            <div className="space-y-2 text-center">
              <h1 className="text-3xl font-semibold tracking-tight">Set a New Password</h1>
              <p className="text-sm text-muted-foreground">Please enter and confirm your new password below.</p>
            </div>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password">New Password</Label>
                <Input 
                  id="password" 
                  name="password" 
                  type="password" 
                  placeholder="••••••••" 
                  required 
                  minLength={8} 
                  value={password} 
                  onChange={(e) => setPassword(e.target.value)} 
                  disabled={isPending} 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <Input 
                  id="confirmPassword" 
                  name="confirmPassword" 
                  type="password" 
                  placeholder="••••••••" 
                  required 
                  minLength={8} 
                  value={confirmPassword} 
                  onChange={(e) => setConfirmPassword(e.target.value)} 
                  disabled={isPending} 
                />
              </div>
            </div>
            
            <SubmitButton 
              pendingText="Updating..." 
              className="w-full"
              disabled={isPending}
            >
              Update Password
            </SubmitButton>

            {error && <FormMessage message={{ error }} />}
          </form>
        </div>
      </div>
    </>
  )
}

export default function SetPasswordPage() {
    return (
        <Suspense fallback={
          <div className="flex min-h-screen items-center justify-center">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        }>
            <SetPasswordComponent />
        </Suspense>
    )
}
