'use client'

import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import Navbar from '@/components/navbar'

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')

  return (
    <>
      <Navbar />
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="w-full max-w-md space-y-8 px-4 py-8">
          <div className="space-y-6 text-center">
            <h1 className="text-2xl font-semibold tracking-tight text-foreground">
              Authentication Error
            </h1>
            <div className="rounded-lg bg-destructive/10 p-4">
              <p className="text-sm text-destructive">
                {error || 'An error occurred during authentication'}
              </p>
            </div>
            <Link
              href="/forgot-password"
              className="inline-block rounded bg-primary px-4 py-2 text-sm text-primary-foreground hover:bg-primary/90"
            >
              Try requesting a new password reset link
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}
