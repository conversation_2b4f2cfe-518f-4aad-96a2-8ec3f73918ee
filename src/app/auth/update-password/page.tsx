'use client'

import { useState, useTransition, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/supabase/client'
import { SubmitButton } from '@/components/submit-button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { FormMessage } from '@/components/form-message'
import Navbar from '@/components/navbar'

export default function UpdatePasswordPage() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')
  const [isPending, startTransition] = useTransition()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        console.error("Auth error:", error?.message)
        router.push('/sign-in')
        return
      }
      
      setIsAuthenticated(true)
    }

    checkAuth()
  }, [router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (password !== confirmPassword) {
      setError('Passwords do not match')
      return
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters')
      return
    }

    startTransition(async () => {
      try {
        const { error } = await supabase.auth.updateUser({
          password: password
        })

        if (error) {
          console.error("Password update error:", error.message)
          setError(error.message)
          return
        }

        // Password updated successfully
        await supabase.auth.signOut()
        router.push('/sign-in?message=Password updated successfully')
      } catch (error) {
        console.error("Unexpected error:", error)
        setError('Something went wrong. Please try again.')
      }
    })
  }

  if (!isAuthenticated) {
    return (
      <>
        <Navbar />
        <div className="flex min-h-screen items-center justify-center">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      </>
    )
  }

  return (
    <>
      <Navbar />
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
          <form onSubmit={handleSubmit} className="flex flex-col space-y-6">
            <div className="space-y-2 text-center">
              <h1 className="text-3xl font-semibold tracking-tight">Update your password</h1>
              <p className="text-sm text-muted-foreground">Enter your new password below.</p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password">New Password</Label>
                <Input 
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your new password"
                  required
                  minLength={8}
                  disabled={isPending}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <Input 
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your new password"
                  required
                  minLength={8}
                  disabled={isPending}
                />
              </div>
            </div>

            {error && <FormMessage message={{ error }} />}

            <SubmitButton 
              pendingText="Updating..."
              className="w-full bg-primary text-primary-foreground"
              disabled={isPending}
            >
              Update password
            </SubmitButton>
          </form>
        </div>
      </div>
    </>
  )
}
