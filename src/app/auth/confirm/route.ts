import { type EmailOtpType } from '@supabase/supabase-js'
import { type NextRequest, NextResponse } from 'next/server'

import { createServerClient, type CookieOptions } from '@supabase/ssr'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/auth/set-password'

  const redirectTo = new URL(next, request.url)

  if (token_hash && type) {
    // Create a Supabase client that can be used in Server Components, Route Handlers, and Server Actions
    // This is the key change: we create a response object first and configure the client to use it.
    const response = NextResponse.redirect(redirectTo);
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            response.cookies.set({ name, value, ...options })
          },
          remove(name: string, options: CookieOptions) {
            response.cookies.set({ name, value: '', ...options })
          },
        },
      }
    )

    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })

    if (!error) {
      // The verifyOtp call has now set the session cookie on the response object.
      // We can return the response, which contains both the redirect and the cookie.
      return response
    }
  }

  // return the user to an error page with some instructions
  redirectTo.pathname = '/auth/error'
  redirectTo.searchParams.set('error', 'Invalid or expired token')
  return NextResponse.redirect(redirectTo)
}
