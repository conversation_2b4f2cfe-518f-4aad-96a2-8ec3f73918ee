import { createBrowserClient } from '@supabase/ssr';

// Get environment variables with validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing Supabase environment variables. Please check your .env.local file and ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set.'
  );
}

// Create a browser client that persists the session in localStorage and cookies
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey, {
  // Cookie options are set at the client option level, not inside cookies object
  cookieOptions: {
    // Use secure cookies in production
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
  },
  // Ensure persistent session storage
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
  },
});

// Auth state synchronization helper
export const synchronizeAuthState = async () => {
  try {
    // Get current session
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) return false;

    // Force refresh the session
    const { error } = await supabase.auth.refreshSession();

    if (error) return false;

    return true;
  } catch (error) {
    console.error("Error synchronizing auth state:", error);
    return false;
  }
};