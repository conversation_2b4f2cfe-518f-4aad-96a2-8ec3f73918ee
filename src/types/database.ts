export interface Profile {
  id: string;
  created_at: string;
  updated_at: string | null;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string | null;
  profile_picture_url: string | null;
  email_verified: boolean | null;
  phone_verified: boolean | null;
  is_active: boolean | null;
  language_preference: string | null;
  expertise_area: string | null;
  qualifications: string | null;
  years_of_experience: number | null;
  password_set: boolean | null;
}

export interface ExpertProfile {
  id: string;
  bio: string | null;
  years_of_experience: number | null;
  education: string | null;
  verification_status: string;
  is_available: boolean | null;
  average_rating: number | null;
  total_reviews: number | null;
  created_at: string | null;
  updated_at: string | null;
  expert_type: string | null;
  expertise_area: string | null;
  qualifications: string | null;
  certifications: string | null;
  current_position: string | null;
  organization: string | null;
  languages_spoken: string | null;
  professional_memberships: string | null;
  awards_honors: string | null;
  verified_at: string | null;
  verified_by: string | null;
}

export interface FarmerProfile {
  id: string;
  preferred_contact_method: string | null;
  created_at: string | null;
  updated_at: string | null;
  farm_name: string | null;
  farm_size_hectares: number | null;
  preferred_crops: string | null;
}

export interface Role {
  id: string;
  name: string;
  description: string | null;
  is_system: boolean | null;
  is_admin_role: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface UserRole {
  id: string;
  user_id: string;
  role_id: string;
  created_at: string | null;
}

export interface UserProfile extends Profile {
  expertProfile: ExpertProfile | null;
  farmerProfile: FarmerProfile | null;
  role: string;
}