{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "export": "next build && next export -o out", "postinstall": "next build", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test"}, "dependencies": {"@babel/runtime": "^7.27.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@sentry/nextjs": "^9.33.0", "@supabase/ssr": "latest", "@swc/helpers": "^0.5.17", "@types/recharts": "^1.8.29", "autoprefixer": "10.4.20", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "critters": "^0.0.24", "embla-carousel-react": "^8.5.2", "find-up": "^5.0.0", "framer-motion": "^12.5.0", "lucide-react": "^0.511.0", "next": "^14.2.30", "next-compose-plugins": "^2.2.1", "next-themes": "^0.2.1", "postcss": "^8", "prettier": "^3.3.3", "radix-ui": "^1.1.3", "react": "^18", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.3", "stripe": "^17.6.0", "styled-jsx": "^5.1.7", "tailwindcss-rtl": "^0.9.0", "tempo-devtools": "^2.0.106", "use-debounce": "^10.0.1", "vaul": "^1.1.2"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@netlify/plugin-nextjs": "^5.11.3", "@playwright/test": "^1.55.1", "@supabase/supabase-js": "^2.58.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^5.0.4", "@vitest/ui": "^3.2.4", "eslint": "8.57.1", "eslint-config-next": "15.3.4", "jsdom": "^27.0.0", "msw": "^2.11.3", "next-superjson-plugin": "^0.6.3", "playwright": "^1.55.1", "supabase": "^2.33.9", "superjson": "^2.2.2", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5.8.3", "vitest": "^3.2.4"}}