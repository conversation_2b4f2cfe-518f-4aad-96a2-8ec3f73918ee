[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NEXT_TELEMETRY_DISABLED = "1"
  NODE_VERSION = "18.18.2"
  NPM_FLAGS = "--legacy-peer-deps --production=false"
  NEXT_FORCE_EDGE_IMAGES = "true"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[functions]
  # Increase the timeout for functions
  node_bundler = "esbuild"
  external_node_modules = ["next", "@netlify/plugin-nextjs", "find-up"]

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[dev]
  command = "npm run dev"
  port = 3000
  publish = ".next"
  targetPort = 3000