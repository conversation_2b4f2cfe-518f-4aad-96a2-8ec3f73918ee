# Detailed Lint Issues Resolution Plan

This document provides a granular, step-by-step plan to resolve all outstanding lint issues. Each task includes the specific file, the issue, and the exact code change required.

---

## **Task 1: Fix `react-hooks/exhaustive-deps` in Dashboard Layout**

- **File:** [`src/app/dashboard/layout.tsx`](src/app/dashboard/layout.tsx)
- **Issue:** The `initializeUser` function is redefined on every render, causing an infinite loop in the `useEffect` hook.
- **Solution:** Wrap `initializeUser` in a `useCallback` hook to memoize it. This stabilizes its identity, making it a safe dependency for the `useEffect`.

```typescript
// BFORE:
const initializeUser = async () => { ... };
useEffect(() => {
  initializeUser();
}, [user, authLoading, router, initializeUser]);

// AFTER:
const initializeUser = useCallback(async () => { ... }, [authLoading, user, router, fetchFullProfile]);
useEffect(() => {
  initializeUser();
}, [initializeUser]);
```

## **Task 2: Fix `no-explicit-any` in Management Page**

- **File:** [`src/app/dashboard/management/page.tsx`](src/app/dashboard/management/page.tsx)
- **Issue:** The `service` parameter in the `getServiceIcon` function is typed as `any`.
- **Solution:** Create a specific union type for the possible service names (`'consultations' | 'support' | ...`) and use that to type the `service` parameter.

```typescript
// BEFORE:
const getServiceIcon = (service: any) => { ... };

// AFTER:
type ServiceName = 'consultations' | 'support' | 'users' | 'settings';
const getServiceIcon = (service: ServiceName) => { ... };
```

## **Task 3: Fix `no-explicit-any` in My Profile Page**

- **File:** [`src/app/dashboard/my-profile/page.tsx`](src/app/dashboard/my-profile/page.tsx)
- **Issue:** The `profile` and `sessions` props are typed as `any`.
- **Solution:** Create specific `ProfileProps` and `Session` interfaces to define the expected shape of these objects.

```typescript
// BEFORE:
export default function MyProfilePage({ profile, sessions }: { profile: any; sessions: any[] }) { ... }

// AFTER:
interface ProfileProps { ... }
interface Session { ... }
export default function MyProfilePage({ profile, sessions }: { profile: ProfileProps; sessions: Session[] }) { ... }
```

## **Task 4: Fix `prefer-const` in Pagination**

- **File:** [`src/app/dashboard/pagination.tsx`](src/app/dashboard/pagination.tsx)
- **Issue:** `let endPage` is used where `const` is appropriate.
- **Solution:** Change `let endPage` to `const endPage` since the variable is never reassigned.

## **Task 5: Fix `no-unescaped-entities` in UI Pages**

- **Files:** [`src/app/style-guide/page.tsx`](src/app/style-guide/page.tsx), [`src/components/hero.tsx`](src/components/hero.tsx)
- **Issue:** Literal apostrophes are used in JSX.
- **Solution:** Replace all instances of `'` with `'`.

## **Task 6: Fix `no-explicit-any` in Dashboard Views**

- **Files:** [`src/components/dashboard/views/admin-view.tsx`](src/components/dashboard/views/admin-view.tsx), [`src/components/dashboard/views/expert-view.tsx`](src/components/dashboard/views/expert-view.tsx)
- **Issue:** The `user` prop is typed as `any`.
- **Solution:** Import the `User` type from `@supabase/supabase-js` and use it to type the `user` prop.

```typescript
// BEFORE:
export default function AdminView({ user }: { user: any }) { ... }

// AFTER:
import { User } from '@supabase/supabase-js';
export default function AdminView({ user }: { user: User }) { ... }
```

## **Task 7: Fix `no-explicit-any` in i18n Provider**

- **File:** [`src/components/i18n-provider.tsx`](src/components/i18n-provider.tsx)
- **Issue:** The `value` for `I18nContext.Provider` and the `children` prop are typed as `any`.
- **Solution:** Define a `I18nContextType` interface for the context value and type `children` as `React.ReactNode`.

## **Task 8: Fix `no-empty-object-type` in UI Components**

- **Files:** [`src/components/ui/command.tsx`](src/components/ui/command.tsx), [`src/components/ui/input.tsx`](src/components/ui/input.tsx), [`src/components/ui/textarea.tsx`](src/components/ui/textarea.tsx)
- **Issue:** Empty interfaces are used for props.
- **Solution:** Extend the interfaces from the appropriate base React HTML attributes.

```typescript
// BEFORE (in input.tsx):
export interface InputProps {}

// AFTER (in input.tsx):
export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}
```

## **Task 9: Fix Remaining Linter Issues**

- **`use-toast.ts`:** The `_actionTypes` variable is correctly identified as a type-only import. No change is needed as this is a warning that won't break the build, but we acknowledge it.
- **`user-profile.tsx` & `navbar.tsx`:** Fix `any` types by creating specific interfaces for the props.
- **`hooks/use-user-profile.ts`:** Remove `supabase` from the `useEffect` dependency array.
- **`middleware.ts` & `utils/` files:** Replace `any` with specific types from Supabase or standard DOM events.

---

This detailed plan provides a clear path to resolving all lint issues. I am ready to proceed with your approval.
