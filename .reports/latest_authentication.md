# Database Authentication Schema Report

## Overview
This report documents the current state of the database authentication schema and logic extracted from `supabase/schemas/full_schema.sql`. The authentication system is built on Supabase's auth schema with custom extensions in the public schema.

## Core Authentication Tables

### auth.users
**Purpose**: Core authentication table storing user login credentials and metadata
**Key Columns**:
- `id` (uuid): Primary key, user identifier
- `email` (varchar): User email address
- `encrypted_password` (varchar): Hashed password
- `email_confirmed_at` (timestamp): Email verification timestamp
- `last_sign_in_at` (timestamp): Last login timestamp
- `raw_user_meta_data` (jsonb): Additional user metadata
- `is_sso_user` (boolean): Indicates SSO authentication
- `confirmed_at` (timestamp): Account confirmation timestamp (computed)

### auth.identities
**Purpose**: Stores user identities from various authentication providers
**Key Columns**:
- `provider_id` (text): Provider-specific user ID
- `user_id` (uuid): Reference to auth.users
- `identity_data` (jsonb): Provider-specific identity data
- `provider` (text): Authentication provider (e.g., email, google)
- `email` (text): Computed email from identity_data

### auth.sessions
**Purpose**: Active user sessions management
**Key Columns**:
- `id` (uuid): Session identifier
- `user_id` (uuid): Associated user
- `created_at` (timestamp): Session creation time
- `aal` (auth.aal_level): Authentication assurance level
- `not_after` (timestamp): Session expiration

### auth.refresh_tokens
**Purpose**: JWT refresh token storage
**Key Columns**:
- `token` (varchar): Refresh token value
- `user_id` (varchar): Associated user
- `revoked` (boolean): Token revocation status
- `created_at` (timestamp): Token creation time

### public.profiles
**Purpose**: Extended user profile information
**Key Columns**:
- `id` (uuid): References auth.users.id
- `email` (varchar): User email
- `first_name` (text): User's first name
- `last_name` (text): User's last name
- `account_activated` (boolean): Account activation status
- `entity_type` (EntityType): User type (FARM or FACTORY)
- `password_set` (boolean): Password setup status

### public.account_activation_history
**Purpose**: Audit trail for account activation actions
**Key Columns**:
- `user_id` (uuid): Target user
- `action` (ActivationAction): Type of activation action
- `performed_by` (uuid): Admin who performed the action
- `notes` (text): Additional notes
- `old_status` (boolean): Previous activation status
- `new_status` (boolean): New activation status

## Authentication Functions

### Core Auth Functions (auth schema)

#### auth.uid()
```sql
CREATE FUNCTION auth.uid() RETURNS uuid
```
**Purpose**: Returns the current user's UUID from JWT claims
**Usage**: `auth.uid()` - commonly used in RLS policies

#### auth.jwt()
```sql
CREATE FUNCTION auth.jwt() RETURNS jsonb
```
**Purpose**: Returns the current JWT token claims as JSONB
**Usage**: `auth.jwt()->>'email'` for extracting claims

#### auth.email()
```sql
CREATE FUNCTION auth.email() RETURNS text
```
**Purpose**: Returns the current user's email from JWT claims
**Usage**: `auth.email()` - deprecated, use `auth.jwt()->>'email'` instead

#### auth.role()
```sql
CREATE FUNCTION auth.role() RETURNS text
```
**Purpose**: Returns the current user's role from JWT claims
**Usage**: `auth.role()` - deprecated, use `auth.jwt()->>'role'` instead

### Public Schema Authentication Functions

#### public.handle_new_user()
```sql
CREATE FUNCTION public.handle_new_user() RETURNS trigger
```
**Purpose**: Trigger function that creates a profile when a new user signs up
**Logic**:
- Creates profile with `account_activated = false` (inactive by default)
- Extracts user metadata (first_name, last_name, phone_number, entity_type)
- Uses safe defaults for missing data
- Handles exceptions gracefully to prevent orphaned auth records

#### public.activate_user_account(uuid, uuid, text)
```sql
CREATE FUNCTION public.activate_user_account(user_id uuid, performed_by uuid, notes text)
```
**Purpose**: Manually activate a user account
**Logic**:
- Updates profile activation status
- Logs activation in history table
- Returns boolean success status

#### public.approve_user_account(uuid)
```sql
CREATE FUNCTION public.approve_user_account(p_user_id uuid)
```
**Purpose**: Approve user account (simplified version)
**Logic**:
- Sets `account_activated = true`
- Note: Missing admin role check in current implementation

#### public.deactivate_user_account(uuid)
```sql
CREATE FUNCTION public.deactivate_user_account(p_user_id uuid)
```
**Purpose**: Deactivate a user account
**Logic**:
- Sets `account_activated = false`
- Note: Missing admin role check

#### public.is_account_activated(uuid)
```sql
CREATE FUNCTION public.is_account_activated(user_id uuid) RETURNS boolean
```
**Purpose**: Check if a user account is activated
**Logic**:
- Returns the `account_activated` status from profiles table

#### public.get_my_role()
```sql
CREATE FUNCTION public.get_my_role() RETURNS text
```
**Purpose**: Get the current user's role
**Logic**:
- Returns role from profiles table
- Note: Currently assumes role column exists in profiles

## Authentication Triggers

### handle_new_user Trigger
**Table**: `auth.users`
**Event**: AFTER INSERT
**Function**: `public.handle_new_user()`
**Purpose**: Automatically create user profile on signup
**Logic**: Creates inactive profile requiring admin approval

### on_registration_approved Trigger
**Table**: `public.registration_requests`
**Event**: AFTER UPDATE (when status changes to APPROVED)
**Function**: `public.handle_registration_approval()`
**Purpose**: Process approved registration requests
**Logic**: Creates asset and activates user account

## Authentication Types and Enums

### auth.aal_level
```sql
CREATE TYPE auth.aal_level AS ENUM ('aal1', 'aal2', 'aal3');
```
**Purpose**: Authentication Assurance Level (AAL1 = password, AAL2 = MFA, AAL3 = hardware token)

### auth.factor_status
```sql
CREATE TYPE auth.factor_status AS ENUM ('unverified', 'verified');
```
**Purpose**: MFA factor verification status

### auth.factor_type
```sql
CREATE TYPE auth.factor_type AS ENUM ('totp', 'webauthn', 'phone');
```
**Purpose**: Type of MFA factor

### public.ActivationAction
```sql
CREATE TYPE public.ActivationAction AS ENUM (
    'ACCOUNT_CREATED', 'SETUP_STARTED', 'SETUP_COMPLETED',
    'REGISTRATION_SUBMITTED', 'ACCOUNT_ACTIVATED', 'ACCOUNT_DEACTIVATED',
    'SETUP_RESET'
);
```
**Purpose**: Types of account activation actions for audit trail

### public.user_role
```sql
CREATE TYPE public.user_role AS ENUM (
    'farmer', 'factory_worker', 'agri_engineer',
    'industrial_engineer', 'expert', 'admin'
);
```
**Purpose**: User role types in the system

## Row Level Security (RLS) Policies

### Profile Access Policies
- **Users can view their own profile**: `SELECT` policy allowing users to see their own profile
- **Users can update their own profile**: `UPDATE` policy for profile modifications
- **Admins have full access to profiles**: Admin override policy

### Account Activation History Policies
- **Users can insert their own activation history**: `INSERT` policy for logging
- **Users can view their own activation history**: `SELECT` policy for personal history
- **Admins have full access to activation history**: Admin override policy

### Registration Request Policies
- **Users can manage their own registration requests**: Full CRUD for own requests
- **Admins have full access to registration requests**: Admin override policy

## Authentication Extensions

### pgcrypto
**Purpose**: Cryptographic functions for password hashing and encryption
**Usage**: Password encryption, secure token generation

### pgjwt
**Purpose**: JSON Web Token handling
**Usage**: JWT token creation and validation

### uuid-ossp
**Purpose**: UUID generation functions
**Usage**: Generating unique identifiers for users and sessions

## Authentication Flow Summary

### User Registration Process
1. User signs up via Supabase Auth
2. `handle_new_user` trigger creates inactive profile
3. User submits registration request with entity details
4. Admin reviews and approves registration
5. `on_registration_approved` trigger activates account and creates asset

### Login Process
1. User authenticates via Supabase Auth
2. JWT token contains user claims (sub, email, role)
3. `auth.uid()`, `auth.jwt()` functions extract user information
4. RLS policies control data access based on user identity

### Account Activation Process
1. New users start with `account_activated = false`
2. Admin can activate via `activate_user_account()` function
3. Activation is logged in `account_activation_history`
4. Only activated users can access full system features

## Security Considerations

### Current Security Features
- Row Level Security (RLS) enabled on all tables
- JWT-based authentication with claims validation
- Password encryption using pgcrypto
- Session management with expiration
- MFA support infrastructure

### Potential Security Gaps
- Some admin functions lack proper role validation
- Profile creation trigger doesn't validate input thoroughly
- No rate limiting visible in schema
- Password reset flow depends on Supabase defaults

## Recommendations

1. **Add proper admin role validation** to activation/deactivation functions
2. **Implement input validation** in user creation triggers
3. **Add audit logging** for sensitive operations
4. **Consider implementing** account lockout mechanisms
5. **Review and strengthen** RLS policies for edge cases

---

*Report generated from supabase/schemas/full_schema.sql on 2025-09-18*