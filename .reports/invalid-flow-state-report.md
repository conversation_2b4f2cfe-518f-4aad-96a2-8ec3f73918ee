# Issue Report: "Invalid Flow State" During Password Reset

## 1. Summary of the Issue

When a user attempts to reset their password, the flow fails upon reaching the `/auth/set-password` page. The user is shown an "Invalid or expired password reset link" error.

-   **<PERSON><PERSON>er Console Error:** `Code exchange error: invalid flow state, no valid flow state found`
-   **Supabase Server Log:** `404: invalid flow state, no valid flow state found`

This error indicates that the secure session exchange (PKCE flow) required for password reset is failing.

## 2. Root Cause Analysis

The "invalid flow state" error occurs because the temporary state required to complete the authentication flow is being lost. In a Supabase and Next.js application, this state is stored in a cookie (e.g., `sb-PROJECT_REF-auth-flow`).

The failure happens in this sequence:
1.  A user requests a password reset, and Supabase sets the `auth-flow` cookie.
2.  The user clicks the reset link in their email and is redirected back to the app.
3.  The Next.js middleware runs. **Crucially, the current middleware implementation does not correctly preserve or refresh the Supabase session cookies.**
4.  By the time the `set-password` page loads and attempts to exchange the code from the URL for a session, the `auth-flow` cookie is missing or invalid.
5.  Supabase's token endpoint rejects the request because it cannot find the corresponding flow state, resulting in the error.

The root cause is the incorrect implementation of the session-handling logic within **`src/middleware.ts`**. It creates its own Supabase client but fails to use the `updateSession` function from `src/supabase/middleware.ts`, which is specifically designed to manage session cookies across server-side requests in Next.js.

## 3. Analysis of Key Files

### `src/middleware.ts` (Primary Issue Location)
The existing middleware does not correctly integrate Supabase's SSR session handling. It attempts to manage cookies manually, which conflicts with the complex requirements of the PKCE flow.

### `src/supabase/middleware.ts`
This file contains the `updateSession` helper function, which is the correct tool for the job. It is designed to be called at the beginning of the main middleware to ensure all Supabase cookies are handled properly before any other logic runs.

### `src/app/auth/set-password/page.tsx`
The logic in this file is correct. It properly attempts to exchange the `code` for a session, but fails because the necessary state has already been lost due to the middleware issue.

## 4. Proposed Solution (Summary)

The fix is to refactor `src/middleware.ts` to use the `updateSession` function correctly.

The corrected middleware should:
1.  Import `updateSession` from `@/supabase/middleware`.
2.  Call `const res = await updateSession(req);` at the very beginning to let Supabase handle its session cookies.
3.  Proceed with the application-specific routing logic (e.g., protecting routes).
4.  Return the response object (`res`) that `updateSession` provides.

This ensures that the `auth-flow` cookie persists throughout the redirect process, allowing the password reset to complete successfully.