# Production Build Error Resolution Plan

## Executive Summary

This analysis addresses a failing Next.js 14 deployment on Netlify with two critical issues: a TypeScript unused imports error blocking the build and a runtime `styled-jsx` module dependency error in serverless functions. The following prioritized resolution plan addresses both build-blocking and runtime errors systematically.

## Issue Analysis & Categorization

### Critical Issues (Build-Blocking)

**Issue 1: TypeScript Unused Imports Error**
- **Severity**: Critical - Prevents deployment
- **Location**: `./src/app/dashboard/consultations/[id]/page.tsx` line 12
- **Error**: `Type error: All imports in import declaration are unused.`
- **Root Cause**: The recharts imports are declared but never used in the component, and TypeScript's strict `noUnusedLocals: true` configuration treats this as a compilation error in production builds [1][2]

**Issue 2: Runtime Styled-JSX Module Missing**
- **Severity**: Critical - Application crashes at runtime
- **Error**: `Runtime.UnhandledPromiseRejection: Error: Cannot find module 'styled-jsx/style'`
- **Root Cause**: Dependency resolution issue in Netlify's serverless environment where `styled-jsx`, a core Next.js dependency, is not properly bundled or accessible [3][4][5]

### Warnings (Non-Blocking)

**Issue 3: Supabase Dynamic Import Warning**
- **Severity**: Warning - Does not block build
- **Error**: `Critical dependency: the request of a dependency is an expression` in `@supabase/realtime-js`
- **Root Cause**: Webpack warning about dynamic imports in the Supabase SSR library

## Root Cause Analysis

### TypeScript Configuration Interaction
The `tsconfig.json` configuration includes strict TypeScript settings (`noUnusedLocals: true`, `noUnusedParameters: true`) that enforce unused variable detection [2]. While this works fine in development mode, Next.js production builds treat these as compilation errors rather than warnings [1].

### Dependency Management Issues
The project shows signs of mixed dependency management (npm vs pnpm) and potential `package-lock.json` inconsistencies. The `styled-jsx` error specifically occurs in Netlify's serverless function environment, suggesting a bundling or dependency resolution problem [4][5].

### Next.js 14 Configuration Conflicts
The experimental `optimizeCss: true` setting combined with complex webpack configurations may interfere with proper dependency bundling for serverless functions [3].

## Prioritized Resolution Plan

### Phase 1: Fix Build-Blocking Errors

#### Step 1: Resolve Unused Imports Error (CRITICAL)

**Action**: Remove unused recharts imports from the consultation detail page

**Files to modify**:
```typescript
// src/app/dashboard/consultations/[id]/page.tsx
// Remove line 12 completely:
// import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
```

**Alternative approach** (if charts will be implemented later):
```typescript
// Replace unused imports with a comment placeholder
// TODO: Implement charts - import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
```

**Why this fixes the issue**: TypeScript's `noUnusedLocals` setting treats unused imports as compilation errors in production builds. Removing the unused imports eliminates the TypeScript error [1][6].

#### Step 2: Fix Styled-JSX Dependency Issue (CRITICAL)

**Action A**: Explicitly add styled-jsx to dependencies
```json
// package.json - Add to dependencies section
"styled-jsx": "^5.1.7"
```

**Action B**: Clean dependency resolution
```bash
# Commands to run:
rm -rf node_modules package-lock.json
npm install
npm run build
```

**Action C**: Update Next.js configuration to ensure proper bundling
```javascript
// next.config.js - Add to experimental section
experimental: {
  serverComponentsExternalPackages: ['styled-jsx'],
  // Remove optimizeCss temporarily to isolate the issue
  // optimizeCss: true,
  scrollRestoration: true,
  serverMinification: true,
  // Add esmExternals configuration
  esmExternals: 'loose',
}
```

**Why this fixes the issue**: The styled-jsx module is a core Next.js dependency that needs to be properly resolved in the serverless environment. Explicitly adding it and ensuring proper bundling configuration addresses the module resolution error [3][4][5].

### Phase 2: Optimize Build Configuration

#### Step 3: Update Netlify Configuration

**Action**: Modify netlify.toml for better dependency management
```toml
[build]
  command = "npm ci && npm run build"
  publish = ".next"

[build.environment]
  NEXT_TELEMETRY_DISABLED = "1"
  NODE_VERSION = "18.18.2"
  # Add NPM configuration
  NPM_FLAGS = "--production=false"
```

**Why this helps**: Using `npm ci` ensures consistent dependency installation from package-lock.json, reducing dependency resolution issues [7].

#### Step 4: TypeScript Configuration Optimization

**Action**: Create build-specific TypeScript configuration
```json
// tsconfig.build.json (new file)
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    // Temporarily disable strict unused variable checking for builds
    "noUnusedLocals": false,
    "noUnusedParameters": false
  }
}
```

**Update package.json build script**:
```json
{
  "scripts": {
    "build": "next build --typescript-config tsconfig.build.json"
  }
}
```

**Why this helps**: Separates development-time strictness from build-time requirements, preventing unused variable errors from blocking deployments while maintaining code quality during development [2].

### Phase 3: Address Warnings and Optimizations

#### Step 5: Resolve Supabase Dynamic Import Warning

**Action**: Update webpack configuration in next.config.js
```javascript
webpack: (config, { dev, isServer }) => {
  // Existing configuration...
  
  // Add rule to handle Supabase dynamic imports
  config.module = config.module || {};
  config.module.rules = config.module.rules || [];
  
  config.module.rules.push({
    test: /node_modules\/@supabase\/realtime-js/,
    use: {
      loader: 'null-loader'
    }
  });
  
  return config;
}
```

#### Step 6: Re-enable Optimizations Gradually

After confirming the build works:
1. Re-enable `optimizeCss: true` in next.config.js
2. Test deployment
3. If successful, re-enable strict TypeScript settings for new development

## Implementation Commands

Execute these commands in sequence:

```bash
# 1. Clean environment
rm -rf node_modules package-lock.json .next

# 2. Fix package.json dependencies
npm install styled-jsx@^5.1.7 --save

# 3. Remove unused imports from consultation page
# (Manual edit required)

# 4. Reinstall dependencies
npm install

# 5. Test build locally
npm run build

# 6. Deploy to Netlify
git add .
git commit -m "Fix: Remove unused imports and resolve styled-jsx dependency"
git push origin main
```

## Verification Steps

1. **Build Success**: Confirm `npm run build` completes without TypeScript errors
2. **Local Function Test**: Run `netlify dev` to test serverless functions locally
3. **Deployment Success**: Verify Netlify build completes successfully
4. **Runtime Verification**: Test application functionality, especially authentication flows
5. **Performance Check**: Monitor bundle sizes and loading times

## Risk Mitigation

- **Rollback Plan**: Keep the current working commit hash for quick rollback if issues arise
- **Gradual Deployment**: Test changes in a staging environment before production
- **Monitoring**: Set up alerts for runtime errors in Netlify function logs
- **Documentation**: Update team documentation about dependency management practices

This plan addresses the immediate deployment blockers while establishing better practices for future development and deployment stability.