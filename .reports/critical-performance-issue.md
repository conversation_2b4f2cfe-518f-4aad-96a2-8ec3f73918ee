ahmad@pop-os:~/Desktop/agri project/Web APP/agri-industrial-project$ npm run dev
 
> dev
> next dev

  ▲ Next.js 14.2.30
  - Local:        http://localhost:3000
  - Environments: .env.local
  - Experiments (use with caution):
    · instrumentationHook

 ✓ Starting...
 ○ Compiling /instrumentation ...
 ✓ Compiled /instrumentation in 49.1s (3165 modules)
 ✓ Ready in 92.3s
 ○ Compiling /src/middleware ...
 ✓ Compiled /src/middleware in 5.2s (545 modules)
Middleware: updateSession response cookies: []
Middleware: updateSession response cookies: []
Middleware: updateSession response cookies: []
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
 ○ Compiling / ...
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
request to https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap failed, reason: 

Retrying 1/3...
request to https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap failed, reason: 

Retrying 1/3...
request to https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap failed, reason: 

Retrying 2/3...
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./node_modules/@supabase/ssr/dist/module/createBrowserClient.js
./node_modules/@supabase/ssr/dist/module/index.js
./src/supabase/server.ts
./src/app/page.tsx
 GET /profile/1 404 in 94789ms
 GET / 200 in 102095ms
 GET / 200 in 102095ms
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
(node:4643) [DEP0060] DeprecationWarning: The `util._extend` API is deprecated. Please use Object.assign() instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./node_modules/@supabase/ssr/dist/module/createBrowserClient.js
./node_modules/@supabase/ssr/dist/module/index.js
./src/supabase/server.ts
./src/app/page.tsx
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./node_modules/@supabase/ssr/dist/module/createBrowserClient.js
./node_modules/@supabase/ssr/dist/module/index.js
./src/supabase/server.ts
./src/app/page.tsx
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./node_modules/@supabase/ssr/dist/module/createServerClient.js
./node_modules/@supabase/ssr/dist/module/index.js
./src/supabase/server.ts
./src/app/actions.ts
 GET /favicon.ico 200 in 10858ms
 GET /favicon.ico 200 in 14900ms
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
 GET /dashboard 200 in 7478ms
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./node_modules/@supabase/ssr/dist/module/createBrowserClient.js
./node_modules/@supabase/ssr/dist/module/index.js
./src/supabase/server.ts
./src/app/actions.ts
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./node_modules/@supabase/ssr/dist/module/createBrowserClient.js
./node_modules/@supabase/ssr/dist/module/index.js
./src/supabase/server.ts
./src/app/actions.ts
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
 GET /dashboard/users?_rsc=1knam 200 in 2915ms
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./node_modules/@supabase/ssr/dist/module/createBrowserClient.js
./node_modules/@supabase/ssr/dist/module/index.js
./src/supabase/server.ts
./src/app/actions.ts
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: updateSession response cookies: []
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token, Value: exists
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.0, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.1, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.2, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.3, Value: undefined
Middleware: Cookie get - Name: sb-jrhbvcjwxvyrxrmgjfbu-auth-token.4, Value: undefined
 ⚠ Server is approaching the used memory threshold, restarting...
  ▲ Next.js 14.2.30
  - Local:        http://localhost:3000
  - Environments: .env.local
  - Experiments (use with caution):
    · instrumentationHook

 ✓ Starting...
