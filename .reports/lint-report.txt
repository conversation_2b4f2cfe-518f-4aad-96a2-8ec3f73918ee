
> build
> next build

  ▲ Next.js 14.2.23
  - Environments: .env
  - Experiments (use with caution):
    · turbotrace
    · optimizeCss
    · scrollRestoration

   Creating an optimized production build ...
 ✓ Compiled successfully
   Linting and checking validity of types ...
Failed to compile.

./src/app/auth/set-password/page.tsx:23:10
Type error: 'isSubmitting' is declared but its value is never read.

[0m [90m 21 |[39m   [36mconst[39m [confirmPassword[33m,[39m setConfirmPassword] [33m=[39m useState([32m''[39m)[33m;[39m[0m
[0m [90m 22 |[39m   [36mconst[39m [isLoading[33m,[39m setIsLoading] [33m=[39m useState([36mfalse[39m)[33m;[39m[0m
[0m[31m[1m>[22m[39m[90m 23 |[39m   [36mconst[39m [isSubmitting[33m,[39m setIsSubmitting] [33m=[39m useState([36mfalse[39m)[33m;[39m[0m
[0m [90m    |[39m          [31m[1m^[22m[39m[0m
[0m [90m 24 |[39m   [36mconst[39m hasProcessedCode [33m=[39m useRef([36mfalse[39m)[33m;[39m[0m
[0m [90m 25 |[39m   useEffect(() [33m=>[39m {[0m
[0m [90m 26 |[39m     [36mconst[39m code [33m=[39m searchParams[33m.[39m[36mget[39m([32m'code'[39m)[33m;[39m[0m
Static worker exited with code: 1 and signal: null
