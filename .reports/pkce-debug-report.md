# PKCE Flow Debug Report - Password Reset Issue

## Current Error
```
Client: Code exchange error: invalid request: both auth code and code verifier should be non-empty
```

## Error Context
1. The error occurs during code exchange in the password reset flow
2. Code verifier cookie is not being found during exchange
3. The flow is attempting client-side code exchange when it should be server-side

## Request Details
- URL: https://jrhbvcjwxvyrxrmgjfbu.supabase.co/auth/v1/token?grant_type=pkce
- Method: POST
- Status: 400
- Auth Code Present: Yes (678233)
- Code Verifier Present: No

## Related Files

### 1. src/app/auth/set-password/page.tsx
- Currently attempting client-side code exchange
- Logs show code verifier is not found in cookies
- Multiple exchange attempts occurring

### 2. src/app/actions.ts
- Generates code verifier during password reset request
- Sets code verifier in HTTP-only cookie
- Uses callback route for redirection

### 3. src/app/auth/callback/route.ts
- Server-side code exchange handler
- Should be handling the code exchange instead of client

### 4. src/middleware.ts
- Handles cookie management
- Preserves auth cookies during flow

## Flow Analysis

### Current Flow (Not Working)
1. User requests password reset
2. Code verifier generated and stored in cookie
3. Email sent with reset link
4. User clicks link → goes directly to set-password page
5. Client-side code exchange fails due to missing verifier

### Expected Flow
1. User requests password reset
2. Code verifier generated and stored in cookie
3. Email sent with reset link
4. User clicks link → goes to callback route first
5. Server-side code exchange with access to cookies
6. Redirect to set-password page with session

## Root Cause
The password reset email is using the wrong redirect URL format:
- Current: `/auth/set-password?code=678233`
- Should be: `/auth/callback?code=678233&next=/auth/set-password`

This is causing:
1. Bypass of the server-side code exchange
2. Client-side code exchange attempt without access to HTTP-only cookies
3. PKCE validation failure due to missing code verifier

## Required Changes

### 1. Update Redirect URL in forgotPasswordAction
```typescript
// Current
const redirectUrl = `${origin}/auth/callback?next=/auth/set-password`;

// Need to ensure this URL is actually being used in the email
const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
  redirectTo: redirectUrl,
});
```

### 2. Verify Cookie Setting
```typescript
// Verify cookie is being set with correct options
const cookieName = `sb-${projectId}-auth-token-code-verifier`;
const cookieOptions = {
  path: '/',
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  maxAge: 3600,
  httpOnly: true
};
```

### 3. Update Callback Route
```typescript
// Ensure proper cookie preservation in response
const response = NextResponse.redirect(...);
const authCookies = cookieStore.getAll().filter(cookie => 
  cookie.name.includes('auth-token') ||
  cookie.name.includes('code-verifier')
);
```

## Testing Steps
1. Clear all browser cookies
2. Request password reset
3. Check server logs for cookie creation
4. Click reset link in email
5. Verify redirect to callback route
6. Check server logs for code exchange
7. Verify successful redirect to set-password

## Monitoring Points
1. Cookie presence and values
2. Redirect URL in reset email
3. Server-side code exchange logs
4. Session establishment
5. Final redirect with success state

## Next Steps
1. Verify email redirect URL
2. Add detailed logging in callback route
3. Test complete flow with cookie monitoring
4. Verify session establishment
5. Update client-side error handling

## Additional Notes
- The code verifier generation is correct
- Cookie setting logic is correct
- The issue is in the flow sequence
- Need to ensure server-side code exchange

Report generated: June 27, 2025
Status: Investigation in Progress
Priority: High - Auth Flow Broken
