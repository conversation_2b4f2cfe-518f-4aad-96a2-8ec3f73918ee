# Simple Authentication Flow Test Guide

## Quick Test Steps

### 1. Sign Up Test
1. Go to `/sign-up`
2. Fill in the form:
   - First Name: "<PERSON>"
   - Last Name: "<PERSON><PERSON>" 
   - Email: "<EMAIL>"
   - Phone: "+**********"
3. Click "Create Expert Account"
4. Should see: "Signup successful, a confirmation link has been sent to your email."

### 2. Email Confirmation Test
1. Check email inbox for confirmation email
2. Click the confirmation link
3. Should redirect to `/auth/set-password?code=...`
4. Should see "Setting up your account..." briefly
5. Then should show password form with "Hi, <PERSON>"

### 3. Password Setup Test
1. Enter password (minimum 8 characters): "TestPass123"
2. Confirm password: "TestPass123"
3. Click "Set Password"
4. Should see "Success! Password set successfully. Redirecting..."
5. Should redirect to `/dashboard` after 1.5 seconds

## Expected Console Logs

When testing, you should see these console messages:

### Email Confirmation Click:
```
Exchanging code for session...
No session found, redirecting to sign-in
```
OR
```
Exchanging code for session...
Authentication successful, showing password form
```

### Password Setup:
```
Updating user password...
Password updated successfully
```

## Common Issues & Solutions

### Issue: "No session found, redirecting to sign-in"
**Cause**: Code exchange failed
**Solution**: 
- Check if email link is valid (not expired)
- Try requesting a new signup
- Check browser console for detailed errors

### Issue: Page keeps loading
**Cause**: Authentication flow stuck
**Solution**: 
- Refresh the page
- Clear browser cache/cookies
- Try incognito mode

### Issue: Password update fails
**Cause**: Session expired or invalid
**Solution**:
- Go back to email and click confirmation link again
- Try the signup process from the beginning

## Success Indicators

✅ **Working Flow**:
- Signup → Email sent
- Email click → Password form with personalized greeting
- Password set → Redirect to dashboard
- No infinite loading
- No timeout errors

❌ **Broken Flow**:
- Infinite "Setting up your account..." 
- Authentication timeout messages
- Redirect loops
- Console errors

## Browser Console Check

Open browser DevTools (F12) and check Console tab for:
- ✅ No red errors
- ✅ Clear authentication flow logs
- ✅ Successful password update message

## Database Verification

After successful password setup, check in Supabase:

```sql
SELECT email, first_name, last_name, email_verified, password_set 
FROM profiles 
WHERE email = '<EMAIL>';
```

Expected results:
- email_verified: true
- password_set: true

## Reset for Retesting

To test again with the same email:

```sql
-- Delete user completely
DELETE FROM auth.users WHERE email = '<EMAIL>';
DELETE FROM profiles WHERE email = '<EMAIL>';
```

## Troubleshooting Commands

### Check if user exists:
```sql
SELECT id, email, email_confirmed_at 
FROM auth.users 
WHERE email = '<EMAIL>';
```

### Check profile status:
```sql
SELECT * FROM profiles WHERE email = '<EMAIL>';
```

This simplified version should work reliably without timeouts or infinite loading issues.