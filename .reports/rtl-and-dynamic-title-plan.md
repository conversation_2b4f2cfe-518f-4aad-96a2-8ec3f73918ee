# Plan: RTL Layout Enhancement & Dynamic Page Titles

This document outlines the step-by-step plan to refactor the web app's UI for proper Right-to-Left (RTL) support and to implement dynamic page titles across the dashboard.

**Developer:** <PERSON><PERSON>, Experienced Technical Leader
**Date:** 2025-06-17
**Status:** In Progress

---

## Part 1: Dynamic Page Titles

This section details the creation of a shared context to manage page titles dynamically.

### Current State & Issues

The dynamic title system has been partially implemented, but several issues remain:

*   **Dashboard Title:** The main dashboard page does not set its title, causing it to incorrectly display the title of the previously viewed page.
*   **Incorrect Title Keys:** The "My Profile" and "Support" pages are using hardcoded string paths (`"pageTitles.myProfile"`, `"pageTitles.support"`) instead of the correct translation keys.
*   **Redundant Providers:** The `PageTitleProvider` was being rendered on each page, causing the title state to reset on navigation. This has been fixed by moving the provider to the root layout.

### Action Plan

To resolve these issues, the following steps will be taken:

#### 1.1. Fix Dashboard Title

The `dashboard/page.tsx` file will be updated to correctly set its title using the `usePageTitle` hook.

```tsx
// src/app/dashboard/page.tsx
// ... imports
import { usePageTitle } from '@/components/page-title-provider';

export default function Dashboard() {
  const { t } = useTranslation();
  const { setTitle } = usePageTitle();

  useEffect(() => {
    setTitle("pageTitles.dashboard");
  }, [setTitle, t]);

  // ... rest of the component
}
```

#### 1.2. Correct Title Keys

The following pages will be updated to use the correct translation keys for their titles:

*   **`my-profile/page.tsx`**: Change `"pageTitles.myProfile"` to `"myProfile.title"`.
*   **`support/page.tsx`**: Change `"pageTitles.support"` to `"supportPage.title"`.

**Example for `my-profile/page.tsx`:**

```tsx
// ... imports
import { usePageTitle } from '@/components/page-title-provider';

export default function MyProfilePage() {
  const { t } = useTranslation();
  const { setTitle } = usePageTitle();

  useEffect(() => {
    setTitle("myProfile.title");
  }, [setTitle, t]);

  // ... rest of the component
}
```

#### 1.3. Centralize `PageTitleProvider` (Completed)

The `PageTitleProvider` has been successfully moved to the root layout at `src/app/dashboard/layout.tsx`. This ensures a single, persistent state for the page title across the entire dashboard.

**`src/app/dashboard/layout.tsx`:**

```tsx
// ...
import { PageTitleProvider } from '@/components/page-title-provider';
import DashboardHeader from '@/components/dashboard-header';

// ...

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
    // ...
    return (
        <div className="flex h-screen bg-gray-50 rtl:flex-row-reverse">
            <DashboardSidebar userRole={userRole} />
            <div className="flex-1 overflow-auto border-s">
                <PageTitleProvider>
                    <DashboardHeader />
                    <main className="flex-1 p-6">{children}</main>
                </PageTitleProvider>
            </div>
        </div>
    );
}
```

---

## Part 2: Global Layout Adjustments (RTL)

### 2.1. Main Layout (`dashboard/layout.tsx`)

*   **Sidebar Position:** The main `div` will have `rtl:flex-row-reverse` added to its className. This will correctly position the sidebar to the right in RTL mode and the left in LTR mode, fixing the root of the layout issue. The `dir` attribute will be kept to enable this behavior.

**`src/app/dashboard/layout.tsx`:**
```tsx
  return (
    <div
      className="flex h-screen bg-gray-50 rtl:flex-row-reverse"
      dir={locale === "ar" ? "rtl" : "ltr"}
    >
      <DashboardSidebar userRole={userRole} />
      <div className="flex-1 overflow-auto border-l rtl:border-l-0 rtl:border-r">
        <PageTitleProvider>
          <DashboardHeader />
          <main className="flex-1 p-6">{children}</main>
        </PageTitleProvider>
      </div>
    </div>
  );
```

### 2.2. Sidebar (`dashboard-sidebar.tsx`)

*   **Logo/Brand:** The `AgriConnect` branding section should retain its left-to-right direction. The `dir="ltr"` is correct.
*   **Sidebar Items:** The `Link` component for each navigation item currently uses `isRTL ? "flex-row-reverse text-right" : ""`. This should be refactored to use Tailwind's `rtl:` variants for consistency and to avoid potential style conflicts.

**Refactoring `SidebarNav`:**
```tsx
// src/components/dashboard-sidebar.tsx

// ...
export function SidebarNav({ className, items, ...props }: SidebarNavProps) {
  const pathname = usePathname();

  return (
    <nav className={cn("flex flex-col space-y-1", className)} {...props}>
      {items.map((item) => {
        const isActive = pathname === item.href;
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-all hover:bg-[#e0f5b1] hover:text-[#121212]",
              "rtl:flex-row-reverse rtl:text-right", // Use Tailwind variants
              isActive
                ? "bg-[#d2e5a4] text-[#121212]"
                : "text-muted-foreground",
            )}
          >
            {item.icon}
            <span>{item.title}</span>
          </Link>
        );
      })}
    </nav>
  );
}
//...
```

### 2.3. Header (`dashboard-header.tsx`)

*   **Layout:** We need to ensure the header also uses `rtl:` variants for layout instead of conditional classes.

---

## Part 3: Page-Specific RTL Adjustments

The principle here is to **remove all `isRTL` conditional classes** and replace them with Tailwind's `rtl:` and `ltr:` variants. This ensures that LTR styles are the default and RTL styles are additive.

### 3.1. Expert View (`expert-view.tsx`)

This component has the most complex layout and is the source of the LTR misalignment. We will refactor it systematically.

*   **Main Container:** Change `className={cn("space-y-6", isRTL ? "text-right" : "")}` to `className="space-y-6 text-left rtl:text-right"`. This explicitly sets the default alignment.

*   **Welcome Header:**
    *   **From:** `className={cn("flex w-full items-center justify-between", isRTL ? "flex-row-reverse" : "")}`
    *   **To:** `className="flex w-full items-center justify-between rtl:flex-row-reverse"`

*   **Consultation Requests Card Header:**
    *   **From:** `className={cn("flex flex-row items-center justify-between pb-3", isRTL ? "flex-row-reverse" : "")}`
    *   **To:** `className="flex flex-row items-center justify-between pb-3 rtl:flex-row-reverse"`
    *   **CardTitle, CardDescription:** Remove `isRTL ? "text-right" : ""`. Add `text-left rtl:text-right` to parent `div`.

*   **Request Item:**
    *   **Main container:** Change to `className="flex items-center justify-between rtl:flex-row-reverse"`.
    *   **Farmer info:** Change `isRTL ? "space-x-reverse" : ""` to `rtl:space-x-reverse`.
    *   **Action buttons:** Change spacing logic from `isRTL ? "space-x-reverse ml-4" : "mr-4"` to `mr-4 rtl:mr-0 rtl:ml-4`.

### 3.2. Admin View (`admin-view.tsx`)

This view is simpler and closer to the ideal. We will just ensure consistency.

*   **Main Container:** Change `className="space-y-6 text-start rtl:text-right"` to `className="space-y-6 text-left rtl:text-right"`. `text-start` is good, but `text-left` is more explicit for LTR.
*   **Welcome Header:** The `h1` should have `text-left rtl:text-right`.
*   **Metrics Cards Header:** `rtl:flex-row-reverse` is already correct.
*   **Metrics Cards Content:** `rtl:text-right` is correct.

---
## Summary of New Approach

1.  **Single Source of Truth:** The `dir` attribute on the root layout element, driven by the `i18n-provider`, is the single source of truth for text direction.
2.  **Tailwind Variants:** Use `rtl:` and `ltr:` variants for all directional styling. This is the key to fixing the LTR display issues.
3.  **Explicit Defaults:** For properties like text alignment, explicitly set a default for LTR (e.g., `text-left`) and an override for RTL (e.g., `rtl:text-right`). This prevents unexpected style inheritance.

This revised plan provides a more robust and maintainable solution for handling both LTR and RTL layouts. It will resolve the current alignment issues and provide a clear pattern for future development.