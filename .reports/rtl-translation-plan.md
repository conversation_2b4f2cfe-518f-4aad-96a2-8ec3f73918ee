# Plan: Arabic Translation and RTL Implementation

This plan outlines the strategy and steps required to fully integrate Arabic language support and a right-to-left (RTL) layout into the web application.

## 1. Project Analysis & Foundation

*   **i18n:** The project has a solid `I18nProvider` that handles language switching and sets the `lang` and `dir` attributes on the `<html>` tag. This is the correct foundation.
*   **Styling:** The project uses Tailwind CSS, which provides powerful and efficient RTL support through its `rtl` and `ltr` variants.
*   **Fonts:** The project correctly includes the `Amiri` font for Arabic text and `Inter` for LTR, which are loaded in `src/app/layout.tsx`.

## 2. Phase 1: Comprehensive Translation

The goal is to ensure every piece of text visible to the user is translated.

*   **Audit All Components:** Systematically go through every page and component in both the Admin and Expert views.
*   **Extract Hardcoded Strings:** Identify all hardcoded text (e.g., "Dashboard", "Users", button labels, table headers).
*   **Update JSON Files:**
    *   Add a new key-value pair to `src/locales/en.json` for each extracted string.
    *   Add the corresponding Arabic translation to `src/locales/ar.json`.
*   **Implement `useTranslation` Hook:** In each component, import and use the `useTranslation` hook to replace hardcoded strings with `t('key.path')`.

**Example:**

```tsx
// Before
<h1>Users</h1>

// After
import { useTranslation } from '@/components/i18n-provider';
const { t } = useTranslation();
<h1>{t('pageTitles.users')}</h1>
```

## 3. Phase 2: RTL Layout Implementation

The core of this phase is to use Tailwind CSS's logical properties and RTL variants to flip the layout and component styling.

### 3.1. Main Layout (Sidebar and Content)

The main layout in `src/app/dashboard/layout.tsx` uses a `flex` container. We will flip its direction for RTL.

*   **Current LTR:**
    ```html
    <div class="flex h-screen bg-gray-50">
      <DashboardSidebar />
      <div class="flex-1 overflow-auto">...</div>
    </div>
    ```
*   **Proposed RTL-aware Change:**
    ```html
    <div class="flex h-screen bg-gray-50 rtl:flex-row-reverse">
      <DashboardSidebar />
      <div class="flex-1 overflow-auto">...</div>
    </div>
    ```
    This single change (`rtl:flex-row-reverse`) will move the sidebar to the right and the main content to the left when the language is Arabic.

### 3.2. Component-Level RTL Styling

We will use Tailwind's `rtl:` and `ltr:` variants to apply styles conditionally. This involves replacing directional CSS properties with their logical, direction-aware counterparts.

**Key Replacements:**

*   `pl-*` (padding-left) -> `ps-*` (padding-start)
*   `pr-*` (padding-right) -> `pe-*` (padding-end)
*   `ml-*` (margin-left) -> `ms-*` (margin-start)
*   `mr-*` (margin-right) -> `me-*` (margin-end)
*   `left-*` -> `start-*`
*   `right-*` -> `end-*`
*   `border-l-*` -> `border-s-*`
*   `border-r-*` -> `border-e-*`
*   `text-left` -> `text-start`
*   `text-right` -> `text-end`
*   `rounded-l-*` -> `rounded-s-*`
*   `rounded-r-*` -> `rounded-e-*`

### 3.3. Specific Component Adjustments

*   **Icons and Text:** For elements containing an icon and text (like buttons or nav links), we can reverse the order using `rtl:flex-row-reverse`.
    *   **File:** `src/components/dashboard-sidebar.tsx`
    *   **Change:**
        ```tsx
        className={cn(
          "flex items-center gap-3 ... rtl:flex-row-reverse", // Add this
          ...
        )}
        ```
*   **Tables:** Ensure `text-align` is handled correctly. Use `text-start` for columns that should be left-aligned in LTR and right-aligned in RTL.
*   **Forms & Inputs:** Labels and inputs should align correctly. The `form.tsx` and other UI components from `shadcn/ui` generally have good RTL support, but we need to verify each one.
*   **Charts/Graphs:** Pay special attention to labels and axes in charts (e.g., Recharts). They may require specific configuration to render correctly in RTL.

## 4. Phase 3: Language Switcher & Persistence

The current `I18nProvider` already saves the locale to `localStorage`. We need to provide a UI element for the user to switch languages.

*   **Create a Language Switcher Component:** This component will use the `useTranslation` hook's `setLocale` function. It could be a simple dropdown in the header or user profile menu.
*   **Add to Header:** Place the new language switcher component in the dashboard's header within `src/app/dashboard/layout.tsx`.

## 5. Phase 4: Testing and Verification

*   **Manual Testing:**
    *   Switch between English (LTR) and Arabic (RTL).
    *   Verify that all text is translated.
    *   Verify that the layout flips correctly (sidebar on the right, content on the left).
    *   Check individual components: buttons, tables, forms, cards, and modals for correct alignment and text direction.
*   **Cross-Browser Testing:** Test on major browsers (Chrome, Firefox, Safari) to ensure consistent behavior.