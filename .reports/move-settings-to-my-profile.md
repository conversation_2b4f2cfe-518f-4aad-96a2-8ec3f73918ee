# Plan for Modifying Settings Page and Creating "My Profile" Page

**Goal:** To refactor the existing `SettingsPage` to remove certain tabs and move professional profile-related information to a new `MyProfilePage`.

### Phase 1: Create the "My Profile" page (`src/app/dashboard/my-profile/page.tsx`)

1.  **Create the new file:**
    *   Create `src/app/dashboard/my-profile/page.tsx`.
2.  **Move Interfaces and State:**
    *   Copy `UserProfile`, `ExpertProfile`, `Specialization`, and `ExpertSpecialization` interfaces from `settings/page.tsx` to `my-profile/page.tsx`.
    *   Copy the `user`, `setUser`, `loading`, `setLoading`, `userRole`, `setUserRole`, `expertProfile`, `setExpertProfile`, `isExpertProfileLoading`, `setIsExpertProfileLoading` states and their related `useEffect` logic for fetching user and expert-specific data (expert profile and specializations) to `my-profile/page.tsx`.
    *   Ensure all necessary imports (e.g., `supabase`, `Button`, `Input`, `Label`, `Card`, `Tabs`, `TabsContent`, `TabsList`, `TabsTrigger`, `Avatar`, `UserProfile`, `DashboardSidebar`, `useRouter`, `useEffect`, `useState`, `useRef`, `User`) are present in `my-profile/page.tsx`.
3.  **Move "Professional Information" content:**
    *   Extract the `TabsContent` block with `value="professional_info"` from `settings/page.tsx`.
    *   Paste this content into `my-profile/page.tsx` within a `Tabs` component.
4.  **Move "Area of Expertise" content:**
    *   Extract the `div` containing the "Areas of Expertise" `Label` and `textarea` from the "Profile" `TabsContent` in `settings/page.tsx`.
    *   Integrate this content into the "Professional Information" card within `my-profile/page.tsx`.
5.  **Create "Specializations" content:**
    *   Create a new `TabsContent` with `value="specializations"` in `my-profile/page.tsx`.
    *   Add a placeholder message like "Specializations content will be added here."
6.  **Adjust JSX Structure:**
    *   Structure the new page with a similar layout to the settings page (sidebar, header, main content, `Tabs` component).

### Phase 2: Modify the "Settings" page (`src/app/dashboard/settings/page.tsx`)

1.  **Remove Tabs from `TabsList`:**
    *   Remove the `TabsTrigger` elements for "Account", "Professional Info", "Specializations", "Portfolio", "Credentials", "Notifications".
2.  **Remove `TabsContent` blocks:**
    *   Remove the `TabsContent` blocks with `value="account"`, `value="professional_info"`, `value="specializations"`, `value="portfolio"`, `value="credentials"`, and `value="notifications"`.
3.  **Remove "Areas of Expertise" from "Profile" tab:**
    *   Remove the `div` containing the "Areas of Expertise" `Label` and `textarea` from the "Profile" `TabsContent`.
4.  **Adjust Imports:**
    *   Remove any imports that are no longer needed due to the removal of components or interfaces (e.g., `ExpertProfile`, `Specialization`, `ExpertSpecialization` interfaces, and related state management if they are fully moved).

### Phase 3: Update Navigation (`src/components/dashboard-sidebar.tsx`)

1.  **Read `dashboard-sidebar.tsx`:**
    *   Read the content of `src/components/dashboard-sidebar.tsx` to identify where navigation links are defined.
2.  **Add "My Profile" link:**
    *   Add a new navigation link for "My Profile" pointing to `/dashboard/my-profile`.
3.  **Review "Settings" link:**
    *   Ensure the existing "Settings" link remains and points to `/dashboard/settings`.

### Phase 4: Create the plan file

1.  Create a new file `.plans/move-settings-to-my-profile.md` and add this detailed plan to it.
