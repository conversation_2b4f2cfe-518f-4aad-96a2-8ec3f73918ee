# Comprehensive Report and Action Plan: Fixing the Password Reset Flow

## 1. Executive Summary

The password reset functionality is failing due to a fundamental architectural issue in how the application handles the authentication session handoff between the server-side Next.js environment and the client-side browser. The core problem is that the session cookie, created after a user verifies their identity via an email link, is not being correctly persisted in the browser. This results in the "Link Invalid or Session Expired" error on the `/auth/set-password` page.

This report provides a detailed analysis of the problem and a step-by-step plan to re-architect the flow for a robust, reliable solution.

## 2. Root Cause Analysis

The primary issue is a misconfiguration of the Supabase Server-Side Rendering (SSR) client within the Next.js Route Handler at `/auth/confirm/route.ts`. The existing code does not correctly manage the `NextResponse` object, which is essential for setting cookies that the browser can use on subsequent requests.

The authentication flow should be:
1.  User requests a password reset.
2.  User clicks the link in the email, which takes them to `/auth/confirm`.
3.  The `/auth/confirm` route handler verifies the token and, crucially, **sets a session cookie on the response that redirects the user to `/auth/set-password`**.
4.  The `/auth/set-password` page loads, and the client-side Supabase instance successfully retrieves the session from the now-present cookie.

The breakdown is happening at **Step 3**. Our current implementation fails to properly attach the cookie to the redirect response.

## 3. The Definitive Solution

The solution is to re-architect the password reset flow to follow modern best practices for Supabase and Next.js. This involves three key changes:

### Step 1: Correct the Password Reset API (`/api/reset-password/route.ts`)

The `redirectTo` URL in this file must point to the `/auth/confirm` route. This is the correct entry point for the server-side verification process.

**File:** [`src/app/api/reset-password/route.ts`](src/app/api/reset-password/route.ts)
**Change:** Ensure the `redirectTo` parameter is set as follows:
```typescript
const { error } = await supabase.auth.resetPasswordForEmail(email, {
  redirectTo: `${origin}/auth/confirm`,
});
```

### Step 2: Re-implement the Confirmation Route (`/auth/confirm/route.ts`)

This is the most critical change. We must rewrite this route to correctly handle the session cookie. This involves creating a `NextResponse` object *before* initializing the Supabase client and then configuring the client to use that response object for all cookie operations.

**File:** [`src/app/auth/confirm/route.ts`](src/app/auth/confirm/route.ts)
**Change:** Replace the entire file content with the following:
```typescript
import { type EmailOtpType } from '@supabase/supabase-js'
import { type NextRequest, NextResponse } from 'next/server'
import { createServerClient, type CookieOptions } from '@supabase/ssr'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/auth/set-password'

  const redirectTo = new URL(next, request.url)

  if (token_hash && type) {
    // Create a response object that we can attach cookies to
    const response = NextResponse.redirect(redirectTo);

    // Create a Supabase client that is aware of the response object
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            response.cookies.set({ name, value, ...options })
          },
          remove(name: string, options: CookieOptions) {
            response.cookies.set({ name, value: '', ...options })
          },
        },
      }
    )

    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })

    if (!error) {
      // The session cookie is now set on the response object.
      // Returning this response will redirect the user AND set the cookie.
      return response
    }
  }

  // Handle errors
  redirectTo.pathname = '/auth/error'
  redirectTo.searchParams.set('error', 'Invalid or expired token. Please try again.')
  return NextResponse.redirect(redirectTo)
}
```

### Step 3: Simplify the Set Password Page (`/auth/set-password/page.tsx`)

With the server-side flow corrected, the `/auth/set-password` page becomes much simpler. Its only job is to check for an existing session when it loads. It no longer needs to handle any complex code exchange logic.

**File:** [`src/app/auth/set-password/page.tsx`](src/app/auth/set-password/page.tsx)
**Change:** Replace the entire file content with the following:
```typescript
'use client'

import { useState, useEffect, useTransition, Suspense } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/supabase/client'
import { SubmitButton } from '@/components/submit-button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { FormMessage } from '@/components/form-message'
import Navbar from '@/components/navbar'
import { useToast } from "@/components/ui/use-toast";

type PageStatus = 'loading' | 'ready' | 'error'

function SetPasswordComponent() {
  const router = useRouter()
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [status, setStatus] = useState<PageStatus>('loading')
  const [error, setError] = useState<string | null>(null)
  const [isPending, startTransition] = useTransition()
  const { toast } = useToast();

  useEffect(() => {
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setStatus('ready');
      } else {
        setError('No active session found. Please use the link from your email.');
        setStatus('error');
      }
    };
    checkSession();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (password.length < 8) {
      setError('Password must be at least 8 characters long.')
      return
    }
    if (password !== confirmPassword) {
      setError('Passwords do not match.')
      return
    }

    startTransition(async () => {
      setError(null)
      const { error: updateError } = await supabase.auth.updateUser({ password })

      if (updateError) {
        setError(updateError.message)
      } else {
        await supabase.auth.signOut()
        toast({
          title: "Success!",
          description: "Your password has been updated. Please sign in with your new password.",
          duration: 5000,
        });
        router.push('/sign-in')
      }
    })
  }
  
  if (status === 'loading') {
    return (
      <>
        <Navbar />
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-sm text-muted-foreground">Verifying your session...</p>
          </div>
        </div>
      </>
    )
  }

  if (status === 'error') {
    return (
      <>
        <Navbar />
        <div className="flex min-h-screen items-center justify-center text-center">
          <div>
            <h1 className="text-xl font-bold text-destructive">Link Invalid or Session Expired</h1>
            <p className="text-muted-foreground mt-2">{error}</p>
            <button
              onClick={() => router.push('/forgot-password')}
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Request New Reset Link
            </button>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <Navbar />
      <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
        <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
          <form onSubmit={handleSubmit} className="flex flex-col space-y-6">
            <div className="space-y-2 text-center">
              <h1 className="text-3xl font-semibold tracking-tight">Set a New Password</h1>
              <p className="text-sm text-muted-foreground">Please enter and confirm your new password below.</p>
            </div>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password">New Password</Label>
                <Input 
                  id="password" 
                  name="password" 
                  type="password" 
                  placeholder="••••••••" 
                  required 
                  minLength={8} 
                  value={password} 
                  onChange={(e) => setPassword(e.target.value)} 
                  disabled={isPending} 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <Input 
                  id="confirmPassword" 
                  name="confirmPassword" 
                  type="password" 
                  placeholder="••••••••" 
                  required 
                  minLength={8} 
                  value={confirmPassword} 
                  onChange={(e) => setConfirmPassword(e.target.value)} 
                  disabled={isPending} 
                />
              </div>
            </div>
            
            <SubmitButton 
              pendingText="Updating..." 
              className="w-full"
              disabled={isPending}
            >
              Update Password
            </SubmitButton>

            {error && <FormMessage message={{ error }} />}
          </form>
        </div>
      </div>
    </>
  )
}

export default function SetPasswordPage() {
    return (
        <Suspense fallback={
          <div className="flex min-h-screen items-center justify-center">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        }>
            <SetPasswordComponent />
        </Suspense>
    )
}
```

## 4. Conclusion and Next Steps

By implementing the three changes outlined above, we will have a correctly architected, robust, and reliable password reset flow. The core problem of session cookie persistence will be resolved.

I will now await your approval to proceed with implementing this plan.