### Implementation Documentation

#### 1. Introduction
This documentation outlines how to style the Consultation page for bilingual support (English LTR and Arabic RTL). The layout toggles between LTR and RTL by setting the `dir` attribute on the `<html>` element, with CSS handling the direction-aware styling.

#### 2. Setting the `dir` Attribute
To enable RTL support:
- **English (LTR)**: Set `dir="ltr"`
- **Arabic (RTL)**: Set `dir="rtl"`

In your Next.js root layout (e.g., `src/app/layout.tsx`), the `dir` attribute is dynamically set based on the current locale via the `src/components/dynamic-locale-attributes.tsx` component. This component listens for locale changes and updates the `<html>` tag accordingly.

```tsx
// src/components/dynamic-locale-attributes.tsx
'use client';

import { useEffect } from 'react';
import { useLocale } from '@/components/locale-provider';

export default function DynamicLocaleAttributes() {
  const { locale } = useLocale();

  useEffect(() => {
    if (locale) {
      document.documentElement.lang = locale;
      document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
    }
  }, [locale]);

  return null;
}
```

#### 3. Key Styling Techniques
The CSS in `consultations.module.css` uses the following techniques:
- **Text Alignment**: `text-align: start` aligns text to the start of the container (left in LTR, right in RTL).
- **Logical Properties**: 
  - `margin-inline`, `padding-inline`, and `inset-inline-start` adapt margins and paddings to the direction.
  - Example: `.searchIcon` uses `inset-inline-start: 1rem` to position the icon on the start side.
- **Flexbox Ordering**: 
  - The `.card` is a flex container with `.contentSection` (order: 1) and `.metaSection` (order: 2) in LTR.
  - In RTL (`html[dir="rtl"]`), these swap to `.contentSection` (order: 2) and `.metaSection` (order: 1), placing content on the right and meta on the left.
- **Item Reversal**: 
  - `.cropInfoContainer` and `.timeInfoContainer` use `flex-direction: row-reverse` in RTL to swap icon and text order.

**Why Logical Properties?**
Logical properties (e.g., `margin-inline-start` vs. `margin-left`) are direction-aware, ensuring the layout adapts seamlessly to LTR and RTL without duplicating styles. Physical properties can break RTL layouts by enforcing fixed directions.

#### 4. Testing Instructions
To verify the bilingual layout:
1. **LTR Test**:
   - Switch the application language to English.
   - Check: Page title is left-aligned, content section is on the left, meta section is on the right, search icon is on the left.
2. **RTL Test**:
   - Switch the application language to Arabic.
   - Check: Page title is right-aligned, content section is on the right, meta section is on the left, search icon is on the right, text is right-aligned.