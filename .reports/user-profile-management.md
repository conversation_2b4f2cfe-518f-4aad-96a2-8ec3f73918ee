# User Profile Management: Core Features Implementation Plan

## 1. High-Level Summary

This document outlines a focused plan to implement the core profile management and expert verification features for the AgriConnect application. The strategy is to build a secure and maintainable system by leveraging Supabase for backend logic and access control, allowing the frontend to focus on user experience.

The main goals of this plan are:
- **Profile Management:** To provide a seamless experience for both Experts and Admins to manage their profile information through a unified `/settings` page.
- **Expert Verification:** To empower Administrators to manage the expert verification lifecycle directly from the `/admin/users` dashboard, with the ability to view, approve, and reject expert applications.
- **Access Control:** To implement a nuanced access control system. Non-approved experts will be prevented from accessing core application features (like consultations) but will **retain access to their own `/settings` page**. This ensures they can manage their profile while awaiting approval.

This will be achieved by using PostgreSQL functions and Row Level Security (RLS) for backend security, and a React/Next.js frontend for the user interface.

## 2. Prerequisites & Requirements

- **Supabase Project:** An active Supabase project with the required database schema.
- **Existing Codebase:** A Next.js application with `supabase-js` v2 integrated.
- **Environment Variables:** `NEXT_PUBLIC_SUPABASE_URL` and `NEXT_PUBLIC_SUPABASE_ANON_KEY` correctly configured.

## 3. Development Tasks (Todo List)

The implementation will be executed in the following order:

### Phase 1: Backend Setup (Completed)
- [x] **Task 1.1:** Create the `is_admin(user_id uuid)` SQL helper function.
- [ ] **Task 1.2:** Define and apply all Row Level Security (RLS) policies for the `profiles`, `expert_profiles`, and `user_roles` tables.
- [x] **Task 1.3:** Create the required `security definer` RPC functions: `approve_expert` and `reject_expert`.

### Phase 2: Frontend Development
- [ ] **Task 2.1:** Create the `useUserProfile()` custom hook for fetching user-related data.
- [ ] **Task 2.2:** Update the `/settings` page to handle both admin and expert profile editing, including displaying the read-only verification status for experts.
- [ ] **Task 2.3:** Overhaul the `/admin/users` page:
    - [ ] Update the data-fetching query to include `expert_profiles.verification_status`.
    - [ ] Replace the "View" button with row-click navigation to a new expert detail page.
    - [ ] Add "Approve" and "Reject" buttons for pending experts and connect them to the RPC functions.
- [ ] **Task 2.4:** Create the new dynamic route and component for the read-only expert detail view (`/admin/users/[id]`).
- [ ] **Task 2.5:** Implement the route protection middleware (`middleware.ts`) to redirect non-approved experts from core features.
- [ ] **Task 2.6:** Create the "Pending Verification" page that unapproved experts will be redirected to from protected routes.

## 4. Technical Implementation Details

### 4.1. Database & Backend

#### **Database Helper Function (`is_admin`)**
A `security definer` function to check if a user has an admin role, simplifying RLS policies.
```sql
CREATE OR REPLACE FUNCTION is_admin(user_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = user_id AND r.is_admin_role = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### **Row Level Security (RLS) Policies**
- **`profiles` Table:**
  - Users can view and update their own record.
  - Admins can view all records.
- **`expert_profiles` Table:**
  - Experts can view and update their own record (but not `verification_status`).
  - Admins can view all records.
- **`user_roles` Table:**
  - Admins have full permissions.
  - Users can view their own role.

#### **PostgreSQL Functions (RPC)**
- `approve_expert(expert_user_id uuid)`: An admin-only function to set `verification_status` to `'approved'`.
- `reject_expert(expert_user_id uuid)`: An admin-only function to set `verification_status` to `'rejected'`.

*(The full SQL for RLS policies and RPC functions will be used during implementation.)*

### 4.2. Frontend (React/Next.js)

#### **`useUserProfile()` Hook**
This hook (`src/hooks/use-user-profile.ts`) will be the primary mechanism for fetching all data related to the currently logged-in user.

#### **Component Structure**
- **/settings (`page.tsx` & `settings-form.tsx`):** The form will use the `useUserProfile` hook to conditionally render fields for admins and experts.
- **/admin/users (`page.tsx`):** The user table will be updated to display the `verification_status` and will include "Approve" and "Reject" buttons that call the respective RPC functions.
- **/admin/users/[id] (`page.tsx`):** A new dynamic route to display a detailed, read-only view of a selected expert.
- **/verification-pending (`page.tsx`):** A simple page to inform non-approved experts of their status.

#### **Route Protection (`middleware.ts`)**
The middleware will be updated to check the user's role and `verification_status`. It will redirect non-approved experts to the `/verification-pending` page if they try to access any core feature, **except for the `/settings` page**.