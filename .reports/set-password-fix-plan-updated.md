# Updated Plan to Fix Password Reset Flow

## Current Issues Analysis

1. **Token Verification Issue:**
   - The code verifier cookie is not being properly passed through the flow
   - The session is not being established after token verification
   - Multiple redirects are causing session state loss

2. **Route Confusion:**
   - Having both `/auth/update-password` and `/auth/set-password` is confusing
   - Inconsistent handling between routes
   - No clear distinction between password update vs reset flows

3. **Session Management:**
   - Session checks happening too early in the flow
   - No proper handling of temporary reset sessions
   - Missing error recovery paths

## Proposed Solution

### 1. Simplify the Flow

```mermaid
sequenceDiagram
    participant User
    participant ForgotPasswordPage
    participant ConfirmRoute
    participant SetPasswordPage
    participant Supabase

    User->>ForgotPasswordPage: Requests password reset
    ForgotPasswordPage->>Supabase: resetPasswordForEmail()
    Supabase-->>User: Sends email with token_hash
    User->>ConfirmRoute: Clicks link (/auth/confirm?token_hash=...)
    
    ConfirmRoute->>Supabase: verifyOtp(token_hash)
    alt Verification Success
        Supabase-->>ConfirmRoute: Creates session
        ConfirmRoute->>SetPasswordPage: Redirect with session
    else Verification Fails
        ConfirmRoute->>ErrorPage: Show error with recovery options
    end
    
    SetPasswordPage->>Supabase: Check session
    alt Valid Session
        SetPasswordPage->>User: Show password form
        User->>SetPasswordPage: Submit new password
        SetPasswordPage->>Supabase: Update password
        SetPasswordPage->>SignInPage: Redirect with success message
    else No Session
        SetPasswordPage->>ForgotPasswordPage: Redirect to start over
    end
```

### 2. Implementation Changes

1. **Confirm Route (`/auth/confirm/route.ts`)**
```typescript
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null

  if (!token_hash || !type) {
    return NextResponse.redirect(
      new URL('/auth/error?error=missing_token', request.url)
    )
  }

  const supabase = await createClient()
  const { data, error } = await supabase.auth.verifyOtp({
    token_hash,
    type
  })

  if (error) {
    return NextResponse.redirect(
      new URL(`/auth/error?error=${encodeURIComponent(error.message)}`, request.url)
    )
  }

  // Ensure session is established
  if (!data?.session) {
    return NextResponse.redirect(
      new URL('/auth/error?error=session_creation_failed', request.url)
    )
  }

  // Redirect to set password with session
  const response = NextResponse.redirect(
    new URL('/auth/set-password', request.url)
  )

  // Copy session cookies
  const cookieStore = cookies()
  const authCookies = cookieStore.getAll()
    .filter(cookie => cookie.name.includes('auth'))
  
  authCookies.forEach(cookie => {
    response.cookies.set(cookie.name, cookie.value, {
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      httpOnly: true
    })
  })

  return response
}
```

### 3. Key Changes

1. **Remove PKCE Flow:**
   - Use Supabase's built-in token verification
   - Rely on `token_hash` instead of code verifier
   - Let Supabase handle session creation

2. **Simplify Routes:**
   - Remove `/auth/update-password`
   - Keep only `/auth/set-password` for password resets
   - Clear separation between reset and update flows

3. **Improve Error Handling:**
   - Add detailed error logging
   - Provide clear user feedback
   - Include recovery paths for all error cases

4. **Session Management:**
   - Ensure session cookies are preserved during redirects
   - Validate session state at each step
   - Clear cleanup after password update

### 4. Supabase Configuration

1. **Email Template Configuration:**
   ```html
   <h2>Reset Your Password</h2>
   <p>Follow this link to reset your password:</p>
   <a href="{{ .SiteURL }}/auth/confirm?token_hash={{ .TokenHash }}&type=recovery&next=/auth/set-password">
     Reset Password
   </a>
   <p>If you did not request this, please ignore this email.</p>
   ```

   Key Points:
   - Uses `{{ .TokenHash }}` instead of custom tokens
   - Sets `type=recovery` for proper verification
   - Includes `next` parameter for redirect after verification
   - Uses `{{ .SiteURL }}` for proper base URL

2. **Project Settings:**
   ```
   Dashboard -> Authentication -> URL Configuration:
   
   Site URL: [Your domain] (e.g., http://localhost:3000 for development)
   
   Redirect URLs:
   - /auth/confirm
   - /auth/set-password
   - /auth/error
   ```

3. **Authentication Settings:**
   ```
   Dashboard -> Authentication -> Email:
   
   [✓] Enable Email Signup
   [✓] Enable Email Confirmations
   [✓] Secure Email Change
   [✓] Secure Password Change
   
   Password Reset Settings:
   - Link Expiry: 24 hours (recommended)
   - Minimum Password Length: 8
   ```

4. **Security Settings:**
   ```
   Dashboard -> Authentication -> Security:
   
   [✓] Enable Rate Limiting
   Rate Limit Count: 20 (recommended)
   Rate Limit Interval: 1 hour
   
   Session Settings:
   - JWT Expiry: 1 hour
   - Enable Refresh Token Rotation
   ```

5. **Environment Variables:**
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   NEXT_PUBLIC_SITE_URL=your_site_url
   ```

### 5. Testing Steps

1. **Reset Flow:**
   - Request password reset
   - Check email content (should have token_hash)
   - Click reset link
   - Verify session creation
   - Set new password
   - Confirm redirect to sign in

2. **Error Cases:**
   - Invalid token
   - Expired token
   - Missing session
   - Password validation
   - Network failures

3. **Security:**
   - Session cookie presence
   - HTTP-only flags
   - Secure flags in production
   - XSS protection

4. **Configuration Testing:**
   - Verify email template rendering
   - Check all redirect URLs work
   - Test rate limiting
   - Verify session expiry
   - Test token expiration

### 6. Troubleshooting Guide

1. **Email Issues:**
   - Check email template syntax
   - Verify site URL configuration
   - Check spam folder
   - Review email logs in Supabase dashboard

2. **Session Issues:**
   - Verify cookie settings
   - Check JWT expiry
   - Review auth logs
   - Test in incognito mode

3. **Token Issues:**
   - Check token_hash in URL
   - Verify type parameter
   - Review token expiry settings
   - Check for URL encoding issues

4. **Common Fixes:**
   - Clear browser cookies
   - Update email templates
   - Check environment variables
   - Review security settings

### 7. Implementation Order

1. **Phase 1: Configuration**
   - Update Supabase email templates
   - Configure project URLs
   - Set security parameters
   - Update environment variables

2. **Phase 2: Code Implementation**
   - Create confirm route
   - Update set-password page
   - Remove update-password route
   - Add error page

3. **Phase 3: Testing**
   - Test basic flow
   - Test error cases
   - Test security measures
   - Verify configuration

4. **Phase 4: Deployment**
   - Deploy code changes
   - Verify production settings
   - Monitor error logs
   - Document changes
