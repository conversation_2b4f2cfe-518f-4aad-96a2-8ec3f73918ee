-- supabase/tests/expert_verification/01_setup.test.sql
-- Tests that the basic schema for expert verification is correctly set up.

BEGIN;

-- Set the total number of tests
SELECT plan(17);

-- 1. Check for custom types
SELECT has_type('public', 'expert_verification_status', 'Type expert_verification_status should exist.');
SELECT enum_has_labels(
    'public',
    'expert_verification_status',
    ARRAY['pending', 'under_review', 'approved', 'rejected', 'suspended', 'resubmission_required'],
    'expert_verification_status should have correct labels.'
);
SELECT has_type('public', 'EntityType', 'Type EntityType should exist.');


-- 2. Check for tables
SELECT has_table('public', 'profiles', 'Table profiles should exist.');
SELECT has_table('public', 'expert_profiles', 'Table expert_profiles should exist.');
SELECT has_table('public', 'expert_documents', 'Table expert_documents should exist.');
SELECT has_table('public', 'expert_verification_history', 'Table expert_verification_history should exist.');

-- 3. Check for columns in profiles
SELECT has_column('public', 'profiles', 'entity_type', 'profiles table should have entity_type column.');

-- 4. Check for columns in expert_profiles
SELECT has_column('public', 'expert_profiles', 'verification_status', 'expert_profiles table should have verification_status column.');
SELECT col_type_is('public', 'expert_profiles', 'verification_status', 'public', 'expert_verification_status', 'verification_status column should have the correct enum type.');

-- 5. Check for columns in expert_documents
SELECT has_column('public', 'expert_documents', 'expert_id', 'expert_documents table should have expert_id column.');
SELECT has_column('public', 'expert_documents', 'document_type', 'expert_documents table should have document_type column.');
SELECT has_column('public', 'expert_documents', 'document_url', 'expert_documents table should have document_url column.');
SELECT has_column('public', 'expert_documents', 'mime_type', 'expert_documents table should have mime_type column.');

-- 6. Check for columns in expert_verification_history
SELECT has_column('public', 'expert_verification_history', 'expert_id', 'expert_verification_history table should have expert_id column.');
SELECT has_column('public', 'expert_verification_history', 'new_status', 'expert_verification_history table should have new_status column.');
SELECT has_column('public', 'expert_verification_history', 'rejection_reasons', 'expert_verification_history table should have rejection_reasons column.');


-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
