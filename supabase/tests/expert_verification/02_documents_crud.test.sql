--supabase/tests/expert_verification/02_documents_crud.test.sql
-- Tests CRUD operations for expert_documents table.

BEGIN;

-- Set the total number of tests
SELECT plan(11);

-- Setup test data for this test file
INSERT INTO public.profiles (id, email, first_name, last_name, role, account_activated)
VALUES ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', '<EMAIL>', 'CRUD', 'Expert', 'expert', false);

INSERT INTO public.expert_profiles (id, verification_status, bio, expertise_area)
VALUES ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'pending', 'Test expert bio for CRUD', 'Testing CRUD');

-- 1. Test INSERT operation
INSERT INTO public.expert_documents (expert_id, document_type, document_url, document_name, file_size, mime_type)
VALUES ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'qualification', 'https://example.com/crud-test.pdf', 'CRUD Test Document', 1024, 'application/pdf');

SELECT results_eq(
    'SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = \'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11\'',
    $$VALUES (1::bigint)$$
    'Should be able to INSERT an expert document.'
);

-- 2. Test SELECT and data integrity
SELECT results_eq(
    'SELECT document_type FROM public.expert_documents WHERE expert_id = \'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11\'',
    $$VALUES ('qualification')$$::text,
    'Document type should be stored and retrieved correctly.'
);

-- 3. Test UPDATE operation
UPDATE public.expert_documents
SET document_name = 'Updated CRUD Test Document'
WHERE expert_id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';

SELECT results_eq(
    'SELECT document_name FROM public.expert_documents WHERE expert_id = \'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11\'',
    $$VALUES ('Updated CRUD Test Document')$$::text,
    'Should be able to UPDATE an expert document.'
);

-- 4. Test required fields (NOT NULL constraints)
SELECT throws_ok(
    $$INSERT INTO public.expert_documents (expert_id, document_url, document_name) VALUES ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'test.pdf', 'Test')$$::text,
    '23502', -- not_null_violation
    NULL,
    'Should enforce NOT NULL constraint on document_type.'
);

-- 5. Test foreign key constraint on expert_id
SELECT throws_ok(
    $$INSERT INTO public.expert_documents (expert_id, document_type, document_url, document_name) VALUES ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'qualification', 'test.pdf', 'Test')$$::text,
    '23503', -- foreign_key_violation
    NULL,
    'Should enforce foreign key constraint on expert_id.'
);

-- 6. Test allowing multiple documents per expert
INSERT INTO public.expert_documents (expert_id, document_type, document_url, document_name)
VALUES ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'certification', 'https://example.com/crud-cert.pdf', 'CRUD Cert');

SELECT results_eq(
    'SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = \'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11\'',
    $$VALUES (2::bigint)$$::text,
    'Should allow multiple documents for one expert.'
);

-- 7. Test DELETE operation
DELETE FROM public.expert_documents WHERE document_type = 'certification';

SELECT results_eq(
    'SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = \'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11\'',
    $$VALUES (1::bigint)$$::text,
    'Should be able to DELETE an expert document.'
);

-- 8. Test automatic timestamp on insert
SELECT col_not_null('public', 'expert_documents', 'created_at', 'created_at should be automatically set on insert.');
SELECT col_not_null('public', 'expert_documents', 'updated_at', 'updated_at should be automatically set on insert.');

-- 9. Test automatic timestamp on update
-- Let's wait a bit to ensure the timestamp changes
SELECT pg_sleep(0.1);
UPDATE public.expert_documents SET document_name = 'Another Update' WHERE expert_id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
SELECT isnt(
    (SELECT created_at FROM public.expert_documents WHERE expert_id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'),
    (SELECT updated_at FROM public.expert_documents WHERE expert_id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'),
    'updated_at should change on update.'
);


-- 10. Test ON DELETE CASCADE when profile is deleted
DELETE FROM public.profiles WHERE id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
SELECT results_eq(
    'SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = \'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11\'',
    $$VALUES (0::bigint)$$::text,
    'Documents should be deleted when expert profile is deleted (ON DELETE CASCADE).'
);


-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
