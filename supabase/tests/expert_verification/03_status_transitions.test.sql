--supabase/tests/expert_verification/03_status_transitions.test.sql
-- Tests the management and transitions of expert verification statuses.

BEGIN;

-- Set the total number of tests
SELECT plan(15);

-- Setup test data for this test file
INSERT INTO public.profiles (id, email, first_name, last_name, role, account_activated)
VALUES
    ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', '<EMAIL>', 'Status', 'Expert', 'expert', false),
    ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', '<EMAIL>', 'Status', 'Admin', 'admin', true);

INSERT INTO public.expert_profiles (id, verification_status, bio, expertise_area)
VALUES ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'pending', 'Test expert bio for status transitions', 'Testing Statuses');

-- 1. Test default verification status
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
    'pending'::expert_verification_status,
    'Default verification status should be "pending".'
);

-- 2. Test transition to 'under_review'
UPDATE public.expert_profiles SET verification_status = 'under_review' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
    'under_review'::expert_verification_status,
    'Status should correctly transition to "under_review".'
);

-- 3. Test transition to 'approved'
UPDATE public.expert_profiles SET verification_status = 'approved' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
    'approved'::expert_verification_status,
    'Status should correctly transition to "approved".'
);

-- 4. Test transition to 'rejected'
UPDATE public.expert_profiles SET verification_status = 'rejected' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
    'rejected'::expert_verification_status,
    'Status should correctly transition to "rejected".'
);

-- 5. Test transition to 'suspended'
UPDATE public.expert_profiles SET verification_status = 'suspended' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
    'suspended'::expert_verification_status,
    'Status should correctly transition to "suspended".'
);

-- 6. Test transition to 'resubmission_required'
UPDATE public.expert_profiles SET verification_status = 'resubmission_required' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
    'resubmission_required'::expert_verification_status,
    'Status should correctly transition to "resubmission_required".'
);

-- 7. Test that an invalid status value is rejected
SELECT throws_ok(
    $$UPDATE public.expert_profiles SET verification_status = 'invalid_status' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'$$::text,
    '22P02', -- invalid_text_representation
    NULL,
    'Should reject an invalid verification status enum value.'
);

-- 8. Test 'verified_at' timestamp is set on approval
UPDATE public.expert_profiles SET verification_status = 'approved', verified_at = NOW() WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT col_not_null('public', 'expert_profiles', 'verified_at', 'verified_at should be set when status is "approved".');

-- 9. Test 'verified_by' field stores admin ID
UPDATE public.expert_profiles SET verified_by = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT is(
    (SELECT verified_by FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
    'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13'::uuid,
    'verified_by should correctly store the admin\'s UUID.'
);

-- 10. Test 'account_activated' relationship
UPDATE public.profiles SET account_activated = true WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT is(
    (SELECT account_activated FROM public.profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
    true,
    'account_activated flag should be updatable.'
);

-- 11. Test combination: approved + activated
UPDATE public.expert_profiles SET verification_status = 'approved' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
UPDATE public.profiles SET account_activated = true WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT ok(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12') = 'approved' AND
    (SELECT account_activated FROM public.profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12') = true,
    'Combination: An approved and activated expert should have full access.'
);

-- 12. Test combination: pending + not activated
UPDATE public.expert_profiles SET verification_status = 'pending' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
UPDATE public.profiles SET account_activated = false WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT ok(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12') = 'pending' AND
    (SELECT account_activated FROM public.profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12') = false,
    'Combination: A pending and deactivated expert should have no access.'
);

-- 13. Test combination: rejected + not activated
UPDATE public.expert_profiles SET verification_status = 'rejected' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT ok(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12') = 'rejected' AND
    (SELECT account_activated FROM public.profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12') = false,
    'Combination: A rejected expert should have no access.'
);

-- 14. Test combination: suspended + activated
UPDATE public.expert_profiles SET verification_status = 'suspended' WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
UPDATE public.profiles SET account_activated = true WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT ok(
    (SELECT verification_status FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12') = 'suspended' AND
    (SELECT account_activated FROM public.profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12') = true,
    'Combination: A suspended expert should have limited access even if their account is active.'
);

-- 15. Test 'documents_required' field
UPDATE public.expert_profiles SET documents_required = ARRAY['qualification', 'certification'] WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
SELECT is(
    (SELECT documents_required FROM public.expert_profiles WHERE id = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'),
    ARRAY['qualification', 'certification'],
    'documents_required field should correctly store an array of strings.'
);

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
