-- Remove the 'role' column from profiles table to avoid confusion
-- Roles are managed through the user_roles table for better flexibility

-- IMPORTANT: This migration should be applied carefully
-- Make sure all role-based logic uses user_roles table instead of profiles.role

-- First, let's check if there are any dependencies on the role column
-- You may need to update any functions or policies that reference profiles.role

-- Step 1: Update any RLS policies that reference profiles.role
-- (The is_profile_admin function we created earlier should be updated to use user_roles)

-- Step 2: Create a function to check user roles from user_roles table
CREATE OR REPLACE FUNCTION "public"."get_user_role"("user_uuid" "uuid" DEFAULT "auth"."uid"()) 
RETURNS text 
LANGUAGE "sql" 
STABLE 
SECURITY DEFINER 
AS $$
SELECT r.name
FROM public.user_roles ur
JOIN public.roles r ON ur.role_id = r.id
WHERE ur.user_id = COALESCE(user_uuid, auth.uid())
LIMIT 1;
$$;

-- Grant permissions on the new function
GRANT ALL ON FUNCTION "public"."get_user_role"("user_uuid" "uuid") TO "authenticated";

-- Step 3: Update is_profile_admin to use user_roles table instead
CREATE OR REPLACE FUNCTION "public"."is_profile_admin"("user_uuid" "uuid" DEFAULT "auth"."uid"()) 
RETURNS boolean 
LANGUAGE "sql" 
STABLE 
SECURITY DEFINER 
AS $$
SELECT EXISTS (
    SELECT 1
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = COALESCE(user_uuid, auth.uid())
    AND r.name = 'admin'
);
$$;

-- Step 4: Before dropping the column, let's migrate any existing role data to user_roles table
-- This ensures no data is lost

-- Insert roles that exist in profiles.role but not in user_roles
INSERT INTO public.user_roles (user_id, role_id)
SELECT DISTINCT p.id, r.id
FROM public.profiles p
JOIN public.roles r ON r.name = p.role
WHERE p.role IS NOT NULL
AND NOT EXISTS (
    SELECT 1 
    FROM public.user_roles ur 
    WHERE ur.user_id = p.id AND ur.role_id = r.id
);

-- Step 5: Now we can safely drop the role column
-- UNCOMMENT THE FOLLOWING LINE ONLY AFTER VERIFYING ALL DEPENDENCIES ARE UPDATED:
-- ALTER TABLE public.profiles DROP COLUMN IF EXISTS role;

-- Add comment for documentation
COMMENT ON FUNCTION "public"."get_user_role"("user_uuid" "uuid") IS 
'Gets the user role from user_roles table (replaces profiles.role column)';

COMMENT ON FUNCTION "public"."is_profile_admin"("user_uuid" "uuid") IS 
'Updated to check admin role from user_roles table instead of profiles.role';

-- Note: The actual DROP COLUMN command is commented out for safety
-- Uncomment it only after ensuring all application code uses user_roles table
