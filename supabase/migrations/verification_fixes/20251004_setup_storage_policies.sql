-- Setup Row Level Security (RLS) policies for expert verification documents storage
-- This ensures proper access control for the expert_verification_documents bucket

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Expert document upload" ON storage.objects;
DROP POLICY IF EXISTS "Expert view own documents" ON storage.objects;
DROP POLICY IF EXISTS "Admin view all verification documents" ON storage.objects;
DROP POLICY IF EXISTS "Admin delete verification documents" ON storage.objects;

-- 1. Allow experts to upload documents to their own folder
CREATE POLICY "Expert document upload"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'expert_verification_documents' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- 2. Allow experts to view their own documents
CREATE POLICY "Expert view own documents"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'expert_verification_documents' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- 3. Allow admins to view ALL verification documents for review
CREATE POLICY "Admin view all verification documents"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'expert_verification_documents' AND
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- 4. Allow admins to delete documents if needed (for cleanup/moderation)
CREATE POLICY "Admin delete verification documents"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'expert_verification_documents' AND
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- 5. Allow admins to update document metadata if needed
CREATE POLICY "Admin update verification documents"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  bucket_id = 'expert_verification_documents' AND
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- Add comments for documentation
COMMENT ON POLICY "Expert document upload" ON storage.objects IS 
'Allows experts to upload verification documents to their own folder (folder name = user ID)';

COMMENT ON POLICY "Expert view own documents" ON storage.objects IS 
'Allows experts to view and download their own uploaded verification documents';

COMMENT ON POLICY "Admin view all verification documents" ON storage.objects IS 
'Allows admin users to view all expert verification documents for review purposes';

COMMENT ON POLICY "Admin delete verification documents" ON storage.objects IS 
'Allows admin users to delete verification documents if needed for moderation';

COMMENT ON POLICY "Admin update verification documents" ON storage.objects IS 
'Allows admin users to update document metadata if needed';
