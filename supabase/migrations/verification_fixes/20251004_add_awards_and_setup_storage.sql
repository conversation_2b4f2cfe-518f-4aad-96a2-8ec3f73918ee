-- Add missing columns to expert_profiles table for verification form
-- This migration adds all columns that are being submitted in the verification form

ALTER TABLE public.expert_profiles
ADD COLUMN IF NOT EXISTS awards_honors TEXT,
ADD COLUMN IF NOT EXISTS certifications TEXT,
ADD COLUMN IF NOT EXISTS current_position TEXT,
ADD COLUMN IF NOT EXISTS organization TEXT,
ADD COLUMN IF NOT EXISTS languages_spoken TEXT,
ADD COLUMN IF NOT EXISTS professional_memberships TEXT;

-- Make expert_type NOT NULL since it's essential to differentiate between expert types
-- First set a default value for existing records, then make it NOT NULL
UPDATE public.expert_profiles
SET expert_type = 'AGRICULTURE'
WHERE expert_type IS NULL;

ALTER TABLE public.expert_profiles
ALTER COLUMN expert_type SET NOT NULL;