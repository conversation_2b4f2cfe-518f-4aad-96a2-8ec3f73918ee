-- Fix RLS policies for admin access to profiles table
-- The existing is_user_admin() function checks user_roles table, but admins are stored in profiles.role

-- Create a new function that checks profiles.role directly
CREATE OR REPLACE FUNCTION "public"."is_profile_admin"("user_uuid" "uuid" DEFAULT "auth"."uid"()) 
RETURNS boolean 
LANGUAGE "sql" 
STABLE 
SECURITY DEFINER 
AS $$
SELECT EXISTS (
    SELECT 1
    FROM public.profiles p
    WHERE p.id = COALESCE(user_uuid, auth.uid())
    AND p.role = 'admin'
);
$$;

-- Grant permissions on the new function
GRANT ALL ON FUNCTION "public"."is_profile_admin"("user_uuid" "uuid") TO "authenticated";

-- Drop existing admin policies for profiles
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON "public"."profiles";
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON "public"."profiles";

-- Create new admin policies using the correct function
CREATE POLICY "Ad<PERSON> can view all profiles" ON "public"."profiles" 
FOR SELECT 
USING ("public"."is_profile_admin"());

CREATE POLICY "Ad<PERSON> can update all profiles" ON "public"."profiles" 
FOR UPDATE 
USING ("public"."is_profile_admin"()) 
WITH CHECK ("public"."is_profile_admin"());

-- Also create admin policies for expert_profiles table if they don't exist
DROP POLICY IF EXISTS "Admins can view all expert profiles" ON "public"."expert_profiles";
CREATE POLICY "Admins can view all expert profiles" ON "public"."expert_profiles" 
FOR SELECT 
USING ("public"."is_profile_admin"());

-- Also create admin policies for client_profiles table if they don't exist  
DROP POLICY IF EXISTS "Admins can view all client profiles" ON "public"."client_profiles";
CREATE POLICY "Admins can view all client profiles" ON "public"."client_profiles" 
FOR SELECT 
USING ("public"."is_profile_admin"());

-- Add comment for documentation
COMMENT ON FUNCTION "public"."is_profile_admin"("user_uuid" "uuid") IS 
'Checks if the user has admin role in the profiles table (not user_roles table)';
