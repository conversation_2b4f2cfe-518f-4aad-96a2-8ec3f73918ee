# Verification Fixes

This document outlines the necessary database modifications to support the expert verification feature.

## 1. Apply Database Migrations

These migrations add missing columns to the `expert_profiles` table that are required for the verification form.

### Migration 1: Add Form Fields and Expert Type Constraints
Run the SQL script from `20251004_add_awards_and_setup_storage.sql`:

```sql
-- Add missing columns to expert_profiles table for verification form
ALTER TABLE public.expert_profiles
ADD COLUMN IF NOT EXISTS awards_honors TEXT,
ADD COLUMN IF NOT EXISTS certifications TEXT,
ADD COLUMN IF NOT EXISTS current_position TEXT,
ADD COLUMN IF NOT EXISTS organization TEXT,
ADD COLUMN IF NOT EXISTS languages_spoken TEXT,
ADD COLUMN IF NOT EXISTS professional_memberships TEXT;

-- Make expert_type NOT NULL since it's essential to differentiate between expert types
UPDATE public.expert_profiles
SET expert_type = 'AGRICULTURE'
WHERE expert_type IS NULL;

ALTER TABLE public.expert_profiles
ALTER COLUMN expert_type SET NOT NULL;
```

### Migration 2: Document Storage (No Changes Needed)
The `expert_documents` table is already available in the schema for storing uploaded verification documents. This provides better structure and tracking than storing URLs in the expert_profiles table.

### Migration 3: Setup Storage Policies
Run the SQL script from `20251004_setup_storage_policies.sql` to configure Row Level Security policies.

### Migration 4: Fix Admin RLS Policies
Run the SQL script from `20251004_fix_admin_rls_policies.sql` to fix admin access to profiles:

```sql
-- This migration fixes the RLS issue where admins can't view all profiles
-- Creates is_profile_admin() function that checks profiles.role = 'admin'
-- Updates RLS policies to use the correct admin check function
```

**Alternative:** You can use the Supabase CLI to apply all migration files located in this directory:
```bash
supabase db push
```

## 2. Configure Storage Bucket for Expert Verification Documents

The application requires a Supabase storage bucket named `expert_verification_documents` to store documents uploaded by experts during the verification process.

### Steps to Create the Bucket:

1.  Go to your Supabase project dashboard.
2.  Navigate to the "Storage" section from the left sidebar.
3.  Click on "Create a new bucket".
4.  **Bucket Name:** `expert_verification_documents`
5.  **Public Bucket:** Keep this **disabled** (unchecked) to ensure documents are private and secure.
6.  Click "Create bucket".

**Important:** Do NOT make this bucket public. Expert verification documents contain sensitive personal information and should only be accessible to authorized users.

### Row Level Security (RLS) Policies:

You need to set up RLS policies on the `storage.objects` table to control access to expert verification documents. These policies ensure that:
- Experts can upload documents to their own folder
- Experts can view their own documents
- **Admins can view ALL verification documents** for review purposes

Add these policies in the Supabase SQL editor:

**1. Allow experts to upload documents to their own folder:**

```sql
CREATE POLICY "Expert document upload"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'expert_verification_documents' AND
  auth.uid()::text = (storage.foldername(name))[1]
);
```

**2. Allow experts to view their own documents:**

```sql
CREATE POLICY "Expert view own documents"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'expert_verification_documents' AND
  auth.uid()::text = (storage.foldername(name))[1]
);
```

**3. Allow admins to view ALL verification documents:**

```sql
CREATE POLICY "Admin view all verification documents"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'expert_verification_documents' AND
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);
```

**4. Allow admins to delete documents if needed:**

```sql
CREATE POLICY "Admin delete verification documents"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'expert_verification_documents' AND
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);
```

**Important Notes:**
- Documents are stored in folders named with the expert's user ID: `{user-id}/{filename}`
- Admin users must have `role = 'admin'` in the `public.profiles` table
- The bucket must be **private** (not public) for security
- These policies allow admins to view ALL expert documents for verification review

## 3. Summary of Required Actions

To complete the expert verification document upload setup:

1. ✅ **Apply database migrations** (add missing columns to expert_profiles)
2. ✅ **Create storage bucket** named `expert_verification_documents` (keep private)
3. ✅ **Apply storage policies** (run the storage policies migration)
4. ✅ **Verify admin users** have `role = 'admin'` in profiles table

After completing these steps, the system will support:
- Multiple file uploads by experts to their private folders
- Admin access to review all expert verification documents
- Secure document storage with proper access controls
