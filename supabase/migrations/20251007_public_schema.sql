

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "public";


ALTER SCHEMA "public" OWNER TO "postgres";


CREATE TYPE "public"."ActivationAction" AS ENUM (
    'ACCOUNT_CREATED',
    'SETUP_STARTED',
    'SETUP_COMPLETED',
    'REGISTRATION_SUBMITTED',
    'ACCOUNT_ACTIVATED',
    'ACCOUNT_DEACTIVATED',
    'SETUP_RESET'
);


ALTER TYPE "public"."ActivationAction" OWNER TO "postgres";


CREATE TYPE "public"."EntityType" AS ENUM (
    'FARM',
    'FACTORY'
);


ALTER TYPE "public"."EntityType" OWNER TO "postgres";


CREATE TYPE "public"."RegistrationAction" AS ENUM (
    'SUBMITTED',
    'APPROVED',
    'REJECTED',
    'RESUBMITTED',
    'EDITED'
);


ALTER TYPE "public"."RegistrationAction" OWNER TO "postgres";


CREATE TYPE "public"."RegistrationStatus" AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED',
    'RESUBMITTED'
);


ALTER TYPE "public"."RegistrationStatus" OWNER TO "postgres";


CREATE TYPE "public"."asset_status" AS ENUM (
    'pending',
    'approved',
    'rejected'
);


ALTER TYPE "public"."asset_status" OWNER TO "postgres";


CREATE TYPE "public"."asset_type" AS ENUM (
    'farm',
    'factory'
);


ALTER TYPE "public"."asset_type" OWNER TO "postgres";


CREATE TYPE "public"."client_type" AS ENUM (
    'FARMER',
    'FACTORY_WORKER'
);


ALTER TYPE "public"."client_type" OWNER TO "postgres";


CREATE TYPE "public"."expert_type" AS ENUM (
    'AGRICULTURE',
    'INDUSTRIAL'
);


ALTER TYPE "public"."expert_type" OWNER TO "postgres";


CREATE TYPE "public"."expert_verification_status" AS ENUM (
    'pending',
    'under_review',
    'approved',
    'rejected',
    'suspended',
    'resubmission_required'
);


ALTER TYPE "public"."expert_verification_status" OWNER TO "postgres";


CREATE TYPE "public"."user_role" AS ENUM (
    'farmer',
    'factory_worker',
    'agri_engineer',
    'industrial_engineer',
    'expert',
    'admin',
    'agriculture_expert',
    'industrial_expert'
);


ALTER TYPE "public"."user_role" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."activate_user_account"("user_id" "uuid", "performed_by" "uuid" DEFAULT NULL::"uuid", "notes" "text" DEFAULT NULL::"text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    previous_status BOOLEAN;
BEGIN
    -- Get current activation status
    SELECT account_activated INTO previous_status
    FROM public.profiles 
    WHERE id = user_id;
    
    -- Update account activation status
    UPDATE public.profiles 
    SET 
        account_activated = true,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = user_id;
    
    -- Log activation history
    INSERT INTO public.account_activation_history (
        user_id, action, performed_by, notes, previous_status, new_status
    ) VALUES (
        user_id, 'ACCOUNT_ACTIVATED', performed_by, notes, previous_status, true
    );
    
    RETURN true;
END;
$$;


ALTER FUNCTION "public"."activate_user_account"("user_id" "uuid", "performed_by" "uuid", "notes" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."approve_registration_request"("request_id" "uuid", "performed_by" "uuid", "notes" "text" DEFAULT NULL::"text") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    request_record RECORD;
    asset_id UUID;
    result JSONB;
BEGIN
    -- Get request details
    SELECT * INTO request_record
    FROM public.registration_requests 
    WHERE id = request_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Registration request not found';
    END IF;
    
    -- Update request status
    UPDATE public.registration_requests 
    SET 
        status = 'APPROVED',
        reviewed_at = CURRENT_TIMESTAMP,
        reviewed_by = performed_by,
        admin_notes = notes,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = request_id;
    
    -- Log approval history
    INSERT INTO public.registration_approval_history (
        request_id, action, performed_by, notes, previous_status, new_status
    ) VALUES (
        request_id, 'APPROVED', performed_by, notes, request_record.status::public.RegistrationStatus, 'APPROVED'::public.RegistrationStatus
    );
    
    -- Transfer to assets table
    BEGIN
        asset_id := public.transfer_registration_to_assets(request_id);
        
        result := jsonb_build_object(
            'success', true,
            'asset_id', asset_id,
            'message', 'Registration approved and asset created successfully'
        );
    EXCEPTION WHEN OTHERS THEN
        -- If transfer fails, log the error but don't fail the approval
        result := jsonb_build_object(
            'success', true,
            'asset_id', null,
            'message', 'Registration approved but asset creation failed: ' || SQLERRM,
            'error', SQLERRM
        );
    END;
    
    RETURN result;
END;
$$;


ALTER FUNCTION "public"."approve_registration_request"("request_id" "uuid", "performed_by" "uuid", "notes" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."approve_user_account"("p_user_id" "uuid") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check if the caller is an admin before proceeding.
  -- This requires a function like `is_admin()` that checks the caller's role.
  -- IF NOT is_admin(auth.uid()) THEN
  --   RAISE EXCEPTION 'Only admins can approve accounts.';
  -- END IF;

  UPDATE public.profiles
  SET account_activated = true
  WHERE id = p_user_id;
END;
$$;


ALTER FUNCTION "public"."approve_user_account"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_expert_consultation_access"("p_expert_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_verification_status expert_verification_status;
    v_account_activated BOOLEAN;
BEGIN
    -- Get expert verification status and account activation
    SELECT 
        ep.verification_status,
        p.account_activated
    INTO 
        v_verification_status,
        v_account_activated
    FROM public.expert_profiles ep
    JOIN public.profiles p ON p.id = ep.id
    WHERE ep.id = p_expert_id;

    -- Return false if expert not found
    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Expert must be approved and account activated
    RETURN (v_verification_status = 'approved' AND v_account_activated = true);
END;
$$;


ALTER FUNCTION "public"."check_expert_consultation_access"("p_expert_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."deactivate_user_account"("p_user_id" "uuid") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Add admin check here as well

  UPDATE public.profiles
  SET account_activated = false
  WHERE id = p_user_id;
END;
$$;


ALTER FUNCTION "public"."deactivate_user_account"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."execute_approve_registration"("p_request_id" "uuid", "p_admin_user_id" "uuid", "p_admin_notes" "text" DEFAULT NULL::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
DECLARE
    request_record RECORD;
    asset_name TEXT;
    asset_location TEXT;
    new_asset_id UUID;
    basic_info_data JSONB;
BEGIN
    -- 1. Retrieve and lock the registration request
    SELECT * INTO request_record
    FROM public.registration_requests
    WHERE id = p_request_id FOR UPDATE;

    IF NOT FOUND THEN RAISE EXCEPTION 'Registration request not found: %', p_request_id; END IF;
    IF request_record.status = 'APPROVED' THEN RAISE EXCEPTION 'Registration request % has already been approved.', p_request_id; END IF;

    -- 2. Extract required data from the registration_data JSON
    SELECT step_data INTO basic_info_data
    FROM jsonb_to_recordset(request_record.registration_data->'registration_steps') as s(step_name text, step_data jsonb)
    WHERE s.step_name = 'farm_basic_info' OR s.step_name = 'factory_basic_info';

    IF basic_info_data IS NULL THEN RAISE EXCEPTION 'Could not find basic_info step data in request %', p_request_id; END IF;

    asset_name := basic_info_data->>'name';
    asset_location := basic_info_data->>'location_address';

    IF asset_name IS NULL OR asset_name = '' THEN RAISE EXCEPTION 'Asset name is missing from basic_info in request %', p_request_id; END IF;

    -- 3. Create the new asset
    INSERT INTO public.assets (owner_id, name, location_address, asset_type, status, details)
    VALUES (request_record.user_id, asset_name, asset_location, request_record.entity_type::asset_type, 'approved', request_record.registration_data)
    RETURNING id INTO new_asset_id;

    -- 4. Update the original request status
    UPDATE public.registration_requests
    SET status = 'APPROVED', reviewed_at = CURRENT_TIMESTAMP, reviewed_by = p_admin_user_id, admin_notes = p_admin_notes
    WHERE id = p_request_id;

    -- 5. Log the approval action
    INSERT INTO public.registration_approval_history (request_id, action, performed_by, notes, previous_status, new_status)
    VALUES (p_request_id, 'APPROVED', p_admin_user_id, p_admin_notes, request_record.status, 'APPROVED');

    -- 6. Activate the user's account if it's not already
    UPDATE public.profiles SET account_activated = true WHERE id = request_record.user_id AND account_activated = false;

    RETURN new_asset_id;

EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error approving registration %: %', p_request_id, SQLERRM;
        RAISE; -- Re-raise the exception to ensure the client knows about the failure.
END;
$$;


ALTER FUNCTION "public"."execute_approve_registration"("p_request_id" "uuid", "p_admin_user_id" "uuid", "p_admin_notes" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."extract_asset_data_from_registration"("p_registration_data" "jsonb") RETURNS "jsonb"
    LANGUAGE "plpgsql" IMMUTABLE
    AS $$
DECLARE
    result JSONB := '{}'::JSONB;
    step_record RECORD;
    basic_info JSONB;
    location_info JSONB;
    contact_info JSONB;
    additional_info JSONB;
    images JSONB;
BEGIN
    -- Initialize result structure
    result := jsonb_build_object(
        'basic_info', '{}'::JSONB,
        'location_info', '{}'::JSONB,
        'contact_info', '{}'::JSONB,
        'additional_info', '{}'::JSONB,
        'images', '[]'::JSONB,
        'documents', '[]'::JSONB
    );
    
    -- Extract data from registration steps
    FOR step_record IN 
        SELECT step_name, step_data 
        FROM jsonb_to_recordset(p_registration_data->'registration_steps') 
        AS x(step_name TEXT, step_data JSONB)
    LOOP
        CASE step_record.step_name
            WHEN 'farm_basic_info', 'factory_basic_info' THEN
                result := jsonb_set(result, '{basic_info}', step_record.step_data);
                
            WHEN 'farm_location', 'factory_location' THEN
                result := jsonb_set(result, '{location_info}', step_record.step_data);
                
            WHEN 'farm_contact', 'factory_contact' THEN
                result := jsonb_set(result, '{contact_info}', step_record.step_data);
                
            WHEN 'farm_additional', 'factory_additional' THEN
                result := jsonb_set(result, '{additional_info}', step_record.step_data);
                
            WHEN 'farm_images', 'factory_images' THEN
                IF step_record.step_data ? 'images' THEN
                    result := jsonb_set(result, '{images}', step_record.step_data->'images');
                END IF;
                
            WHEN 'farm_documents', 'factory_documents' THEN
                IF step_record.step_data ? 'documents' THEN
                    result := jsonb_set(result, '{documents}', step_record.step_data->'documents');
                END IF;
                
            ELSE
                -- Handle any other step types by adding them to additional_info
                result := jsonb_set(
                    result, 
                    array['additional_info', step_record.step_name], 
                    step_record.step_data
                );
        END CASE;
    END LOOP;
    
    -- Add computed fields for easy access
    result := result || jsonb_build_object(
        'computed', jsonb_build_object(
            'asset_name', COALESCE(
                result->'basic_info'->>'name',
                result->'basic_info'->>'farm_name',
                result->'basic_info'->>'factory_name',
                'Unnamed Asset'
            ),
            'asset_location', COALESCE(
                result->'location_info'->>'location_address',
                result->'basic_info'->>'location_address',
                result->'location_info'->>'address',
                'No location provided'
            ),
            'contact_person', COALESCE(
                result->'contact_info'->>'contact_person',
                result->'basic_info'->>'owner_name',
                'No contact provided'
            ),
            'contact_phone', COALESCE(
                result->'contact_info'->>'phone',
                result->'contact_info'->>'contact_phone',
                result->'basic_info'->>'phone',
                'No phone provided'
            ),
            'total_images', COALESCE(jsonb_array_length(result->'images'), 0),
            'total_documents', COALESCE(jsonb_array_length(result->'documents'), 0)
        )
    );
    
    RETURN result;
END;
$$;


ALTER FUNCTION "public"."extract_asset_data_from_registration"("p_registration_data" "jsonb") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."extract_asset_data_from_registration"("p_registration_data" "jsonb") IS 'Extract and format asset data from registration JSON for display';



CREATE OR REPLACE FUNCTION "public"."get_account_setup_progress"("user_id" "uuid") RETURNS TABLE("current_step" integer, "total_steps" integer, "completed_steps" integer, "entity_type" "text", "setup_completed" boolean)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.account_setup_step as current_step,
        CASE 
            WHEN p.entity_type IS NOT NULL THEN 5 
            ELSE 0 
        END as total_steps,
        COALESCE((
            SELECT COUNT(*)::INTEGER 
            FROM public.account_setup_progress asp 
            WHERE asp.user_id = p.id AND asp.is_completed = true
        ), 0) as completed_steps,
        p.entity_type::TEXT as entity_type,
        p.account_setup_completed as setup_completed
    FROM public.profiles p
    WHERE p.id = user_id;
END;
$$;


ALTER FUNCTION "public"."get_account_setup_progress"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_expert_verification_details"("p_expert_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_expert_details JSONB;
    v_documents JSONB;
    v_history JSONB;
    v_result JSONB;
BEGIN
    -- Check if user is admin or the expert themselves
    IF NOT (
        public.is_profile_admin(auth.uid()) OR
        auth.uid() = p_expert_id
    ) THEN
        RAISE EXCEPTION 'Unauthorized: Access denied';
    END IF;

    -- Get expert details
    SELECT jsonb_build_object(
        'expert_id', p.id,
        'full_name', p.first_name || ' ' || p.last_name,
        'email', p.email,
        'verification_status', ep.verification_status,
        'account_activated', p.account_activated,
        'expertise_area', ep.expertise_area,
        'qualifications', ep.qualifications,
        'years_of_experience', ep.years_of_experience,
        'education', ep.education,
        'bio', ep.bio,
        'verified_at', ep.verified_at,
        'verified_by', ep.verified_by,
        'documents_required', ep.documents_required,
        'created_at', ep.created_at,
        'updated_at', ep.updated_at
    ) INTO v_expert_details
    FROM public.profiles p
    JOIN public.expert_profiles ep ON ep.id = p.id
    WHERE p.id = p_expert_id;

    IF v_expert_details IS NULL THEN
        RAISE EXCEPTION 'Expert not found with id: %', p_expert_id;
    END IF;

    -- Get documents
    SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
            'id', ed.id,
            'document_type', ed.document_type,
            'document_name', ed.document_name,
            'document_url', ed.document_url,
            'file_size', ed.file_size,
            'file_type', ed.file_type,
            'verification_status', ed.verification_status,
            'admin_notes', ed.admin_notes,
            'upload_date', ed.upload_date,
            'reviewed_at', ed.reviewed_at
        )
    ), '[]'::jsonb) INTO v_documents
    FROM public.expert_documents ed
    WHERE ed.expert_id = p_expert_id;

    -- Get verification history
    SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
            'id', evh.id,
            'previous_status', evh.previous_status,
            'new_status', evh.new_status,
            'admin_notes', evh.admin_notes,
            'rejection_reasons', evh.rejection_reasons,
            'created_at', evh.created_at,
            'changed_by', evh.changed_by
        ) ORDER BY evh.created_at DESC
    ), '[]'::jsonb) INTO v_history
    FROM public.expert_verification_history evh
    WHERE evh.expert_id = p_expert_id;

    -- Build final result
    v_result := v_expert_details || jsonb_build_object(
        'documents', v_documents,
        'verification_history', v_history
    );

    RETURN v_result;
END;
$$;


ALTER FUNCTION "public"."get_expert_verification_details"("p_expert_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_experts_pending_verification"() RETURNS TABLE("expert_id" "uuid", "full_name" "text", "email" "text", "verification_status" "public"."expert_verification_status", "expertise_area" "text", "years_of_experience" integer, "documents_count" integer, "created_at" timestamp with time zone, "updated_at" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Check if user is admin
    IF NOT public.is_profile_admin(auth.uid()) THEN
        RAISE EXCEPTION 'Unauthorized: Only admins can access verification queue';
    END IF;

    RETURN QUERY
    SELECT 
        p.id as expert_id,
        (p.first_name || ' ' || p.last_name) as full_name,
        p.email,
        ep.verification_status,
        ep.expertise_area,
        ep.years_of_experience,
        COALESCE(doc_count.count, 0)::INTEGER as documents_count,
        ep.created_at,
        ep.updated_at
    FROM public.profiles p
    JOIN public.expert_profiles ep ON ep.id = p.id
    JOIN public.user_roles ur ON ur.user_id = p.id
    JOIN public.roles r ON r.id = ur.role_id
    LEFT JOIN (
        SELECT 
            expert_id, 
            COUNT(*)::INTEGER as count
        FROM public.expert_documents 
        GROUP BY expert_id
    ) doc_count ON doc_count.expert_id = p.id
    WHERE r.name = 'expert'
    AND ep.verification_status IN ('pending', 'under_review', 'resubmission_required')
    ORDER BY ep.created_at ASC;
END;
$$;


ALTER FUNCTION "public"."get_experts_pending_verification"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_my_role"() RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    user_role_name text;
BEGIN
    -- Join with user_roles and roles tables to get the role name
    SELECT r.name INTO user_role_name
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid()
    LIMIT 1;
    
    -- If no role found, return 'user' as default
    RETURN COALESCE(user_role_name, 'user');
END;
$$;


ALTER FUNCTION "public"."get_my_role"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_my_role"() IS 'Returns the current user role name from user_roles/roles tables';



CREATE OR REPLACE FUNCTION "public"."get_my_verification_status"() RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_result JSONB;
    v_expert_id UUID;
BEGIN
    -- Get current user ID and verify they are an expert
    SELECT p.id INTO v_expert_id 
    FROM public.profiles p
    JOIN public.user_roles ur ON ur.user_id = p.id
    JOIN public.roles r ON r.id = ur.role_id
    WHERE p.id = auth.uid() AND r.name = 'expert';

    IF v_expert_id IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: Only experts can check verification status';
    END IF;

    -- Get verification status and details
    SELECT jsonb_build_object(
        'verification_status', ep.verification_status,
        'account_activated', p.account_activated,
        'documents_required', ep.documents_required,
        'consultation_access', public.check_expert_consultation_access(v_expert_id),
        'verified_at', ep.verified_at,
        'can_upload_documents', (ep.verification_status IN ('pending', 'resubmission_required'))
    ) INTO v_result
    FROM public.profiles p
    JOIN public.expert_profiles ep ON ep.id = p.id
    WHERE p.id = v_expert_id;

    RETURN COALESCE(v_result, '{}'::jsonb);
END;
$$;


ALTER FUNCTION "public"."get_my_verification_status"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_registration_request_details"("p_request_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    request_record RECORD;
    user_record RECORD;
    extracted_data JSONB;
    approval_history JSONB;
    result JSONB;
BEGIN
    -- Get registration request
    SELECT * INTO request_record
    FROM public.registration_requests
    WHERE id = p_request_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Registration request not found';
    END IF;
    
    -- Get user information
    SELECT * INTO user_record
    FROM public.profiles
    WHERE id = request_record.user_id;
    
    -- Extract asset data
    extracted_data := public.extract_asset_data_from_registration(request_record.registration_data);
    
    -- Get approval history
    SELECT jsonb_agg(
        jsonb_build_object(
            'id', h.id,
            'action', h.action,
            'performed_by', h.performed_by,
            'performed_at', h.performed_at,
            'notes', h.notes,
            'previous_status', h.previous_status,
            'new_status', h.new_status
        ) ORDER BY h.performed_at DESC
    ) INTO approval_history
    FROM public.registration_approval_history h
    WHERE h.request_id = p_request_id;
    
    -- Build result
    result := jsonb_build_object(
        'request', jsonb_build_object(
            'id', request_record.id,
            'user_id', request_record.user_id,
            'entity_type', request_record.entity_type,
            'status', request_record.status,
            'submitted_at', request_record.submitted_at,
            'reviewed_at', request_record.reviewed_at,
            'reviewed_by', request_record.reviewed_by,
            'admin_notes', request_record.admin_notes,
            'rejection_reason', request_record.rejection_reason,
            'created_at', request_record.created_at,
            'updated_at', request_record.updated_at
        ),
        'user', jsonb_build_object(
            'id', user_record.id,
            'email', user_record.email,
            'first_name', user_record.first_name,
            'last_name', user_record.last_name,
            'phone_number', user_record.phone_number,
            'entity_type', user_record.entity_type,
            'account_activated', user_record.account_activated
        ),
        'asset_data', extracted_data,
        'approval_history', COALESCE(approval_history, '[]'::JSONB)
    );
    
    RETURN result;
END;
$$;


ALTER FUNCTION "public"."get_registration_request_details"("p_request_id" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_registration_request_details"("p_request_id" "uuid") IS 'Get complete registration request details with extracted asset data and history - Fixed type matching';



CREATE OR REPLACE FUNCTION "public"."get_registration_requests"("p_limit" integer DEFAULT 20, "p_offset" integer DEFAULT 0, "p_entity_type" "public"."EntityType" DEFAULT NULL::"public"."EntityType", "p_status" "public"."RegistrationStatus" DEFAULT NULL::"public"."RegistrationStatus", "p_search_term" "text" DEFAULT NULL::"text") RETURNS TABLE("id" "uuid", "user_id" "uuid", "entity_type" "public"."EntityType", "registration_data" "jsonb", "status" "public"."RegistrationStatus", "submitted_at" timestamp with time zone, "reviewed_at" timestamp with time zone, "reviewed_by" "uuid", "admin_notes" "text", "rejection_reason" "text", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "user_email" character varying, "user_first_name" character varying, "user_last_name" character varying, "user_phone_number" character varying, "asset_name" "text", "asset_location" "text", "total_count" bigint)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    total_records BIGINT;
    search_pattern TEXT;
BEGIN
    -- Prepare search pattern
    search_pattern := CASE 
        WHEN p_search_term IS NOT NULL AND p_search_term != '' 
        THEN '%' || p_search_term || '%' 
        ELSE NULL 
    END;
    
    -- Get total count first
    SELECT COUNT(*) INTO total_records
    FROM public.registration_requests rr
    LEFT JOIN public.profiles p ON rr.user_id = p.id
    WHERE 
        (p_entity_type IS NULL OR rr.entity_type = p_entity_type)
        AND (p_status IS NULL OR rr.status = p_status)
        AND (
            search_pattern IS NULL OR 
            p.email ILIKE search_pattern OR 
            p.first_name ILIKE search_pattern OR 
            p.last_name ILIKE search_pattern OR 
            rr.registration_data::text ILIKE search_pattern
        );
    
    -- Return the results with pagination
    RETURN QUERY
    SELECT 
        rr.id,
        rr.user_id,
        rr.entity_type,
        rr.registration_data,
        rr.status,
        rr.submitted_at,
        rr.reviewed_at,
        rr.reviewed_by,
        rr.admin_notes,
        rr.rejection_reason,
        rr.created_at,
        rr.updated_at,
        p.email::VARCHAR(255) as user_email,
        p.first_name::VARCHAR(255) as user_first_name,
        p.last_name::VARCHAR(255) as user_last_name,
        p.phone_number::VARCHAR(255) as user_phone_number,
        -- Extract asset name from registration data
        COALESCE(
            (rr.registration_data->'registration_steps'->0->'step_data'->>'name'),
            (rr.registration_data->'registration_steps'->1->'step_data'->>'name'),
            'N/A'
        )::TEXT as asset_name,
        -- Extract location from registration data
        COALESCE(
            (rr.registration_data->'registration_steps'->0->'step_data'->>'location_address'),
            (rr.registration_data->'registration_steps'->1->'step_data'->>'location_address'),
            'N/A'
        )::TEXT as asset_location,
        total_records as total_count
    FROM public.registration_requests rr
    LEFT JOIN public.profiles p ON rr.user_id = p.id
    WHERE 
        (p_entity_type IS NULL OR rr.entity_type = p_entity_type)
        AND (p_status IS NULL OR rr.status = p_status)
        AND (
            search_pattern IS NULL OR 
            p.email ILIKE search_pattern OR 
            p.first_name ILIKE search_pattern OR 
            p.last_name ILIKE search_pattern OR 
            rr.registration_data::text ILIKE search_pattern
        )
    ORDER BY rr.submitted_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$;


ALTER FUNCTION "public"."get_registration_requests"("p_limit" integer, "p_offset" integer, "p_entity_type" "public"."EntityType", "p_status" "public"."RegistrationStatus", "p_search_term" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_registration_requests"("p_limit" integer, "p_offset" integer, "p_entity_type" "public"."EntityType", "p_status" "public"."RegistrationStatus", "p_search_term" "text") IS 'Fetch registration requests with pagination, filtering, and user information - Fixed type matching';



CREATE OR REPLACE FUNCTION "public"."get_user_role"() RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  role_name TEXT;
BEGIN
  SELECT r.name INTO role_name
  FROM public.user_roles ur
  JOIN public.roles r ON ur.role_id = r.id
  WHERE ur.user_id = auth.uid()
  LIMIT 1;
  
  RETURN COALESCE(role_name, 'user');
END;
$$;


ALTER FUNCTION "public"."get_user_role"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_user_role"() IS 'Helper function to get current user role name';



CREATE OR REPLACE FUNCTION "public"."get_user_role"("user_uuid" "uuid" DEFAULT "auth"."uid"()) RETURNS "text"
    LANGUAGE "sql" STABLE SECURITY DEFINER
    AS $$
SELECT r.name
FROM public.user_roles ur
JOIN public.roles r ON ur.role_id = r.id
WHERE ur.user_id = COALESCE(user_uuid, auth.uid())
LIMIT 1;
$$;


ALTER FUNCTION "public"."get_user_role"("user_uuid" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_user_role"("user_uuid" "uuid") IS 'Gets the user role from user_roles table (replaces profiles.role column)';



CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  INSERT INTO public.profiles (
    id,
    email,
    first_name,
    last_name,
    phone_number,
    entity_type,
    account_activated,
    password_set,
    account_setup_completed,
    account_setup_step
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'first_name', '[Pending]'),
    COALESCE(NEW.raw_user_meta_data->>'last_name', '[Pending]'),
    NEW.raw_user_meta_data->>'phone_number',
    (NEW.raw_user_meta_data->>'entity_type')::public."EntityType",
    false,
    false,
    false,
    0
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone_number = EXCLUDED.phone_number,
    updated_at = NOW();

  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_registration_approval"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
DECLARE
    v_asset_name TEXT;
    v_location_address TEXT;
    v_latitude NUMERIC;
    v_longitude NUMERIC;
    v_basic_info_data JSONB;
    v_new_asset_id UUID;
BEGIN
    -- Extract the basic info step data from the registration_steps array
    SELECT s.step_data INTO v_basic_info_data
    FROM jsonb_to_recordset(NEW.registration_data->'registration_steps') as s(step_name text, step_data jsonb)
    WHERE s.step_name = 'farm_basic_info' OR s.step_name = 'factory_basic_info';

    -- Correctly extract the asset name
    v_asset_name := COALESCE(v_basic_info_data->>'farm_name', v_basic_info_data->>'factory_name');

    -- Correctly extract nested location data
    v_location_address := v_basic_info_data->'location'->>'address';
    v_latitude := (v_basic_info_data->'location'->>'latitude')::NUMERIC;
    v_longitude := (v_basic_info_data->'location'->>'longitude')::NUMERIC;

    -- Validation: Ensure asset name is present
    IF v_asset_name IS NULL OR v_asset_name = '' THEN
        RAISE EXCEPTION 'Approval failed: Asset name is missing from basic_info in request %', NEW.id;
    END IF;

    -- Create the new asset with all extracted data
    INSERT INTO public.assets (owner_id, name, location_address, latitude, longitude, asset_type, status, details)
    VALUES (
        NEW.user_id,
        v_asset_name,
        v_location_address,
        v_latitude,
        v_longitude,
        LOWER(NEW.entity_type::text)::asset_type, -- Corrected cast
        'approved',
        NEW.registration_data -- Store the full original JSON for completeness
    )
    RETURNING id INTO v_new_asset_id;

    -- Activate the user's account if it's not already active
    UPDATE public.profiles SET account_activated = true WHERE id = NEW.user_id AND account_activated = false;

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_registration_approval"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_account_activated"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN (
        SELECT account_activated 
        FROM public.profiles 
        WHERE id = user_id
    );
END;
$$;


ALTER FUNCTION "public"."is_account_activated"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_admin"() RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid() AND r.is_admin_role = true
  );
END;
$$;


ALTER FUNCTION "public"."is_admin"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."is_admin"() IS 'Helper function to check if current user has admin role';



CREATE OR REPLACE FUNCTION "public"."is_admin_simple"() RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid() 
    AND r.name = 'admin'
  );
$$;


ALTER FUNCTION "public"."is_admin_simple"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_profile_admin"("user_uuid" "uuid" DEFAULT "auth"."uid"()) RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    AS $$
SELECT EXISTS (
    SELECT 1
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = COALESCE(user_uuid, auth.uid())
    AND r.name = 'admin'
);
$$;


ALTER FUNCTION "public"."is_profile_admin"("user_uuid" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."is_profile_admin"("user_uuid" "uuid") IS 'Updated to check admin role from user_roles table instead of profiles.role';



CREATE OR REPLACE FUNCTION "public"."is_user_admin"("user_uuid" "uuid" DEFAULT "auth"."uid"()) RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = COALESCE(user_uuid, auth.uid())
    AND r.name = 'admin'
  );
$$;


ALTER FUNCTION "public"."is_user_admin"("user_uuid" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."migrate_approved_registrations_to_assets"() RETURNS integer
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    request_record RECORD;
    transferred_count INTEGER := 0;
    asset_id UUID;
BEGIN
    -- Loop through all approved registrations that haven't been transferred
    FOR request_record IN 
        SELECT rr.* 
        FROM public.registration_requests rr
        LEFT JOIN public.assets a ON a.owner_id = rr.user_id 
            AND a.created_at >= rr.reviewed_at
        WHERE rr.status = 'APPROVED' 
            AND a.id IS NULL
    LOOP
        BEGIN
            asset_id := public.transfer_registration_to_assets(request_record.id);
            transferred_count := transferred_count + 1;
            
            RAISE NOTICE 'Transferred registration % to asset %', request_record.id, asset_id;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Failed to transfer registration %: %', request_record.id, SQLERRM;
        END;
    END LOOP;
    
    RETURN transferred_count;
END;
$$;


ALTER FUNCTION "public"."migrate_approved_registrations_to_assets"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."reject_registration_request"("p_request_id" "uuid", "p_performed_by" "uuid", "p_rejection_reason" "text", "p_admin_notes" "text" DEFAULT NULL::"text") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    request_record RECORD;
    result JSONB;
BEGIN
    -- Get request details
    SELECT * INTO request_record
    FROM public.registration_requests 
    WHERE id = p_request_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Registration request not found';
    END IF;
    
    IF request_record.status = 'REJECTED' THEN
        RAISE EXCEPTION 'Registration request has already been rejected';
    END IF;
    
    IF request_record.status = 'APPROVED' THEN
        RAISE EXCEPTION 'Cannot reject an already approved registration request';
    END IF;
    
    -- Update request status
    UPDATE public.registration_requests 
    SET 
        status = 'REJECTED',
        reviewed_at = CURRENT_TIMESTAMP,
        reviewed_by = p_performed_by,
        rejection_reason = p_rejection_reason,
        admin_notes = p_admin_notes,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_request_id;
    
    -- Log rejection history
    INSERT INTO public.registration_approval_history (
        request_id, action, performed_by, notes, previous_status, new_status
    ) VALUES (
        p_request_id, 'REJECTED', p_performed_by, 
        COALESCE(p_admin_notes, '') || ' | Rejection reason: ' || p_rejection_reason, 
        request_record.status::public."RegistrationStatus", 'REJECTED'::public."RegistrationStatus"
    );
    
    result := jsonb_build_object(
        'success', true,
        'message', 'Registration request rejected successfully',
        'request_id', p_request_id,
        'previous_status', request_record.status,
        'new_status', 'REJECTED'
    );
    
    RETURN result;
END;
$$;


ALTER FUNCTION "public"."reject_registration_request"("p_request_id" "uuid", "p_performed_by" "uuid", "p_rejection_reason" "text", "p_admin_notes" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."reject_registration_request"("p_request_id" "uuid", "p_performed_by" "uuid", "p_rejection_reason" "text", "p_admin_notes" "text") IS 'Reject a registration request with proper audit logging';



CREATE OR REPLACE FUNCTION "public"."submit_registration_request"("user_id" "uuid", "entity_type" "text", "registration_data" "jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    request_id UUID;
    typed_entity_type "public"."EntityType";
BEGIN
    -- Convert text to enum type with validation
    CASE UPPER(entity_type)
        WHEN 'FARM' THEN typed_entity_type := 'FARM'::"public"."EntityType";
        WHEN 'FACTORY' THEN typed_entity_type := 'FACTORY'::"public"."EntityType";
        ELSE 
            RAISE EXCEPTION 'Invalid entity_type: %. Must be FARM or FACTORY', entity_type;
    END CASE;
    
    -- Insert with properly typed enum value
    INSERT INTO public.registration_requests (
        user_id, 
        entity_type, 
        registration_data,
        status,
        created_at,
        updated_at
    ) VALUES (
        user_id, 
        typed_entity_type, 
        registration_data,
        'PENDING',
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    ) RETURNING id INTO request_id;
    
    RETURN request_id;
END;
$$;


ALTER FUNCTION "public"."submit_registration_request"("user_id" "uuid", "entity_type" "text", "registration_data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."submit_registration_request_typed"("user_id" "uuid", "entity_type" "public"."EntityType", "registration_data" "jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    request_id UUID;
BEGIN
    INSERT INTO public.registration_requests (
        user_id, 
        entity_type, 
        registration_data,
        status,
        created_at,
        updated_at
    ) VALUES (
        user_id, 
        entity_type, 
        registration_data,
        'PENDING',
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    ) RETURNING id INTO request_id;
    
    RETURN request_id;
END;
$$;


ALTER FUNCTION "public"."submit_registration_request_typed"("user_id" "uuid", "entity_type" "public"."EntityType", "registration_data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."test_get_expert_verification_status"("p_expert_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_result JSONB;
BEGIN
    -- Verify user is an expert (using passed expert_id instead of auth.uid())
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = p_expert_id AND role = 'expert'
    ) THEN
        RAISE EXCEPTION 'Invalid expert ID: %', p_expert_id;
    END IF;

    -- Get verification status and details
    SELECT jsonb_build_object(
        'verification_status', ep.verification_status,
        'account_activated', p.account_activated,
        'documents_required', ep.documents_required,
        'consultation_access', public.check_expert_consultation_access(p_expert_id),
        'verified_at', ep.verified_at,
        'can_upload_documents', (ep.verification_status IN ('pending', 'resubmission_required'))
    ) INTO v_result
    FROM public.profiles p
    JOIN public.expert_profiles ep ON ep.id = p.id
    WHERE p.id = p_expert_id;

    RETURN COALESCE(v_result, '{}'::jsonb);
END;
$$;


ALTER FUNCTION "public"."test_get_expert_verification_status"("p_expert_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."test_upload_expert_verification_document"("p_expert_id" "uuid", "p_document_type" character varying, "p_document_url" "text", "p_document_name" character varying, "p_file_size" integer DEFAULT NULL::integer, "p_file_type" character varying DEFAULT NULL::character varying) RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_document_id UUID;
BEGIN
    -- Verify user is an expert (using passed expert_id instead of auth.uid())
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = p_expert_id AND role = 'expert'
    ) THEN
        RAISE EXCEPTION 'Invalid expert ID: %', p_expert_id;
    END IF;

    -- Insert document record
    INSERT INTO public.expert_documents (
        expert_id,
        document_type,
        document_url,
        document_name,
        file_size,
        file_type
    ) VALUES (
        p_expert_id,
        p_document_type,
        p_document_url,
        p_document_name,
        p_file_size,
        p_file_type
    ) RETURNING id INTO v_document_id;

    RETURN v_document_id;
END;
$$;


ALTER FUNCTION "public"."test_upload_expert_verification_document"("p_expert_id" "uuid", "p_document_type" character varying, "p_document_url" "text", "p_document_name" character varying, "p_file_size" integer, "p_file_type" character varying) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."transfer_registration_to_assets"("request_id" "uuid") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    request_record RECORD;
    asset_id UUID;
    asset_details JSONB;
BEGIN
    -- Get the approved registration request
    SELECT * INTO request_record
    FROM public.registration_requests 
    WHERE id = request_id AND status = 'APPROVED';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Approved registration request not found with id: %', request_id;
    END IF;
    
    -- Extract asset details from registration data
    asset_details := request_record.registration_data;
    
    -- Insert into assets table
    INSERT INTO public.assets (
        owner_id,
        name,
        location_address,
        latitude,
        longitude,
        asset_type,
        status,
        details,
        created_at,
        updated_at
    ) VALUES (
        request_record.user_id,
        COALESCE(
            asset_details->>'name',
            asset_details->'registration_steps'->'basic_info'->>'farm_name',
            asset_details->'registration_steps'->'basic_info'->>'factory_name',
            asset_details->'basic_info'->>'farm_name',
            asset_details->'basic_info'->>'factory_name',
            'Unnamed Asset'
        ),
        COALESCE(
            asset_details->'registration_steps'->'basic_info'->'location'->>'address',
            asset_details->'basic_info'->'location'->>'address',
            asset_details->'location'->>'address',
            asset_details->>'location'
        ),
        CASE 
            WHEN asset_details->'registration_steps'->'basic_info'->'location'->>'latitude' IS NOT NULL 
            THEN (asset_details->'registration_steps'->'basic_info'->'location'->>'latitude')::DECIMAL
            WHEN asset_details->'basic_info'->'location'->>'latitude' IS NOT NULL 
            THEN (asset_details->'basic_info'->'location'->>'latitude')::DECIMAL
            WHEN asset_details->'location'->>'latitude' IS NOT NULL 
            THEN (asset_details->'location'->>'latitude')::DECIMAL
            ELSE NULL
        END,
        CASE 
            WHEN asset_details->'registration_steps'->'basic_info'->'location'->>'longitude' IS NOT NULL 
            THEN (asset_details->'registration_steps'->'basic_info'->'location'->>'longitude')::DECIMAL
            WHEN asset_details->'basic_info'->'location'->>'longitude' IS NOT NULL 
            THEN (asset_details->'basic_info'->'location'->>'longitude')::DECIMAL
            WHEN asset_details->'location'->>'longitude' IS NOT NULL 
            THEN (asset_details->'location'->>'longitude')::DECIMAL
            ELSE NULL
        END,
        CASE 
            WHEN request_record.entity_type = 'FARM' THEN 'farm'::public.asset_type
            WHEN request_record.entity_type = 'FACTORY' THEN 'factory'::public.asset_type
            ELSE 'farm'::public.asset_type
        END,
        'approved'::public.asset_status,
        asset_details,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    ) RETURNING id INTO asset_id;
    
    -- Update the registration request to mark it as transferred
    UPDATE public.registration_requests 
    SET 
        updated_at = CURRENT_TIMESTAMP
    WHERE id = request_id;
    
    RETURN asset_id;
END;
$$;


ALTER FUNCTION "public"."transfer_registration_to_assets"("request_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_account_setup_step"("user_id" "uuid", "step_number" integer, "step_name" "text", "step_data" "jsonb" DEFAULT NULL::"jsonb") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    step_exists BOOLEAN;
BEGIN
    -- Check if step already exists
    SELECT EXISTS(
        SELECT 1 FROM public.account_setup_progress asp
        WHERE asp.user_id = update_account_setup_step.user_id 
        AND asp.step_number = update_account_setup_step.step_number
    ) INTO step_exists;
    
    IF step_exists THEN
        -- Update existing step
        UPDATE public.account_setup_progress asp
        SET 
            step_data = update_account_setup_step.step_data,
            is_completed = true,
            completed_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE asp.user_id = update_account_setup_step.user_id 
        AND asp.step_number = update_account_setup_step.step_number;
    ELSE
        -- Insert new step
        INSERT INTO public.account_setup_progress (
            user_id, step_number, step_name, step_data, is_completed, completed_at
        ) VALUES (
            update_account_setup_step.user_id,
            update_account_setup_step.step_number,
            update_account_setup_step.step_name,
            update_account_setup_step.step_data,
            true,
            CURRENT_TIMESTAMP
        );
    END IF;
    
    -- Update user's current step
    UPDATE public.profiles p
    SET account_setup_step = update_account_setup_step.step_number
    WHERE p.id = update_account_setup_step.user_id;
    
    RETURN true;
END;
$$;


ALTER FUNCTION "public"."update_account_setup_step"("user_id" "uuid", "step_number" integer, "step_name" "text", "step_data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_expert_verification_status"("p_expert_id" "uuid", "p_new_status" "public"."expert_verification_status", "p_admin_notes" "text" DEFAULT NULL::"text", "p_rejection_reasons" "text"[] DEFAULT NULL::"text"[]) RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_current_status expert_verification_status;
    v_admin_id UUID;
    v_result JSONB;
BEGIN
    -- Check if user is admin
    IF NOT public.is_profile_admin(auth.uid()) THEN
        RAISE EXCEPTION 'Unauthorized: Only admins can update verification status';
    END IF;
    
    v_admin_id := auth.uid();

    -- Get current verification status
    SELECT verification_status INTO v_current_status
    FROM public.expert_profiles
    WHERE id = p_expert_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Expert not found with id: %', p_expert_id;
    END IF;

    -- Update expert_profiles
    UPDATE public.expert_profiles
    SET
        verification_status = p_new_status,
        verified_at = CASE
            WHEN p_new_status = 'approved' THEN NOW()
            ELSE NULL
        END,
        verified_by = CASE
            WHEN p_new_status = 'approved' THEN v_admin_id
            ELSE NULL
        END,
        updated_at = NOW()
    WHERE id = p_expert_id;

    -- Update main profiles table account_activated
    UPDATE public.profiles
    SET
        account_activated = CASE
            WHEN p_new_status = 'approved' THEN true
            ELSE false
        END,
        updated_at = NOW()
    WHERE id = p_expert_id;

    -- Log the change in verification history
    INSERT INTO public.expert_verification_history (
        expert_id,
        previous_status,
        new_status,
        changed_by,
        admin_notes,
        rejection_reasons
    ) VALUES (
        p_expert_id,
        v_current_status,
        p_new_status,
        v_admin_id,
        p_admin_notes,
        p_rejection_reasons
    );

    -- Build result
    v_result := jsonb_build_object(
        'success', true,
        'expert_id', p_expert_id,
        'previous_status', v_current_status,
        'new_status', p_new_status,
        'message', 'Expert verification status updated successfully'
    );

    RETURN v_result;
END;
$$;


ALTER FUNCTION "public"."update_expert_verification_status"("p_expert_id" "uuid", "p_new_status" "public"."expert_verification_status", "p_admin_notes" "text", "p_rejection_reasons" "text"[]) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."upload_expert_verification_document"("p_document_type" character varying, "p_document_url" "text", "p_document_name" character varying, "p_file_size" integer DEFAULT NULL::integer, "p_file_type" character varying DEFAULT NULL::character varying) RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_document_id UUID;
    v_expert_id UUID;
BEGIN
    -- Get current user ID and verify they are an expert
    SELECT p.id INTO v_expert_id 
    FROM public.profiles p
    JOIN public.user_roles ur ON ur.user_id = p.id
    JOIN public.roles r ON r.id = ur.role_id
    WHERE p.id = auth.uid() AND r.name = 'expert';

    IF v_expert_id IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: Only experts can upload verification documents';
    END IF;

    -- Validate document type
    IF p_document_type NOT IN ('qualification', 'certification', 'experience', 'id_document') THEN
        RAISE EXCEPTION 'Invalid document type: %', p_document_type;
    END IF;

    -- Insert document record
    INSERT INTO public.expert_documents (
        expert_id,
        document_type,
        document_url,
        document_name,
        file_size,
        file_type
    ) VALUES (
        v_expert_id,
        p_document_type,
        p_document_url,
        p_document_name,
        p_file_size,
        p_file_type
    ) RETURNING id INTO v_document_id;

    RETURN v_document_id;
END;
$$;


ALTER FUNCTION "public"."upload_expert_verification_document"("p_document_type" character varying, "p_document_url" "text", "p_document_name" character varying, "p_file_size" integer, "p_file_type" character varying) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."validate_consultation_asset"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Check if the asset exists and is approved
    IF NOT EXISTS (
        SELECT 1 FROM public.assets 
        WHERE id = NEW.asset_id 
        AND status = 'approved'
    ) THEN
        RAISE EXCEPTION 'Consultation must be linked to an approved asset';
    END IF;
    
    -- Check if the requester owns the asset
    IF NOT EXISTS (
        SELECT 1 FROM public.assets 
        WHERE id = NEW.asset_id 
        AND owner_id = NEW.requester_id
    ) THEN
        RAISE EXCEPTION 'Consultation requester must own the linked asset';
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."validate_consultation_asset"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."validate_expert_consultation_access"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- If this is an expert trying to access consultations
    IF TG_TABLE_NAME = 'consultations' AND NEW.expert_id IS NOT NULL THEN
        -- Check if expert has consultation access
        IF NOT public.check_expert_consultation_access(NEW.expert_id) THEN
            RAISE EXCEPTION 'Access denied: Expert verification required before accessing consultations';
        END IF;
    END IF;

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."validate_expert_consultation_access"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."account_activation_history" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "action" "public"."ActivationAction" NOT NULL,
    "performed_by" "uuid",
    "notes" "text",
    "old_status" boolean,
    "new_status" boolean NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."account_activation_history" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."account_setup_progress" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "step_number" integer NOT NULL,
    "step_name" character varying(100) NOT NULL,
    "is_completed" boolean DEFAULT false,
    "step_data" "jsonb",
    "completed_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."account_setup_progress" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."assets" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "owner_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "location_address" "text",
    "latitude" numeric(10,8),
    "longitude" numeric(11,8),
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "asset_type" "public"."asset_type" DEFAULT 'farm'::"public"."asset_type" NOT NULL,
    "status" "public"."asset_status" DEFAULT 'approved'::"public"."asset_status" NOT NULL,
    "details" "jsonb"
);


ALTER TABLE "public"."assets" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."client_profiles" (
    "id" "uuid" NOT NULL,
    "preferred_contact_method" character varying(20) DEFAULT 'email'::character varying,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "farm_name" "text",
    "farm_size_hectares" numeric,
    "preferred_crops" "text",
    "client_type" "public"."client_type"
);


ALTER TABLE "public"."client_profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."consultation_requests" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "requester_id" "uuid" NOT NULL,
    "expert_id" "uuid",
    "asset_id" "uuid" NOT NULL,
    "problem_description" "text" NOT NULL,
    "status" character varying(20) DEFAULT 'pending'::character varying,
    "preferred_date" timestamp with time zone,
    "urgency_level" character varying(20) DEFAULT 'normal'::character varying,
    "expert_response" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "attachment_paths" "jsonb" DEFAULT '[]'::"jsonb"
);


ALTER TABLE "public"."consultation_requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."consultations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "request_id" "uuid" NOT NULL,
    "farmer_id" "uuid" NOT NULL,
    "expert_id" "uuid" NOT NULL,
    "scheduled_start" timestamp with time zone NOT NULL,
    "scheduled_end" timestamp with time zone NOT NULL,
    "actual_start" timestamp with time zone,
    "actual_end" timestamp with time zone,
    "status" character varying(20) DEFAULT 'scheduled'::character varying,
    "cancellation_reason" "text",
    "cancelled_by" "uuid",
    "expert_phone" character varying(20),
    "farmer_phone" character varying(20),
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."consultations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."expert_documents" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "expert_id" "uuid" NOT NULL,
    "document_type" character varying(50) NOT NULL,
    "document_url" "text" NOT NULL,
    "document_name" character varying(255) NOT NULL,
    "file_size" integer,
    "file_type" character varying(50),
    "upload_date" timestamp with time zone DEFAULT "now"(),
    "verification_status" "public"."expert_verification_status" DEFAULT 'pending'::"public"."expert_verification_status",
    "admin_notes" "text",
    "reviewed_by" "uuid",
    "reviewed_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "mime_type" character varying(100)
);


ALTER TABLE "public"."expert_documents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."expert_profiles" (
    "id" "uuid" NOT NULL,
    "bio" "text",
    "years_of_experience" integer,
    "education" "text",
    "verification_status" "public"."expert_verification_status" DEFAULT 'pending'::"public"."expert_verification_status",
    "is_available" boolean DEFAULT true,
    "average_rating" numeric(3,2),
    "total_reviews" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "expertise_area" "text",
    "qualifications" "text",
    "documents_required" boolean DEFAULT true,
    "verified_at" timestamp with time zone,
    "verified_by" "uuid",
    "expert_type" "public"."expert_type" NOT NULL,
    "awards_honors" "text",
    "certifications" "text",
    "current_position" "text",
    "organization" "text",
    "languages_spoken" "text",
    "professional_memberships" "text",
    "document_urls" "text"[]
);


ALTER TABLE "public"."expert_profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."expert_verification_history" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "expert_id" "uuid" NOT NULL,
    "previous_status" "public"."expert_verification_status",
    "new_status" "public"."expert_verification_status" NOT NULL,
    "changed_by" "uuid",
    "admin_notes" "text",
    "rejection_reasons" "text"[],
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."expert_verification_history" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."messages" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "consultation_id" "uuid" NOT NULL,
    "sender_id" "uuid" NOT NULL,
    "content" "text",
    "attachment_path" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "content_or_attachment_not_null" CHECK ((("content" IS NOT NULL) OR ("attachment_path" IS NOT NULL)))
);


ALTER TABLE "public"."messages" OWNER TO "postgres";


COMMENT ON TABLE "public"."messages" IS 'Stores chat messages for consultations.';



COMMENT ON COLUMN "public"."messages"."attachment_path" IS 'Path to an uploaded file in the consultation-attachments storage bucket.';



CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "email" character varying(255) NOT NULL,
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "phone_number" character varying(20),
    "profile_picture_url" character varying(255),
    "email_verified" boolean DEFAULT false,
    "phone_verified" boolean DEFAULT false,
    "is_active" boolean DEFAULT true,
    "language_preference" character varying(10) DEFAULT 'en'::character varying,
    "password_set" boolean DEFAULT false,
    "is_available" boolean DEFAULT false,
    "account_activated" boolean DEFAULT false,
    "account_setup_completed" boolean DEFAULT false,
    "entity_type" "public"."EntityType",
    "account_setup_step" integer DEFAULT 0,
    "avatar_url" "text"
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


COMMENT ON COLUMN "public"."profiles"."avatar_url" IS 'URL to user avatar image stored in storage';



CREATE TABLE IF NOT EXISTS "public"."registration_approval_history" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "request_id" "uuid" NOT NULL,
    "action" "public"."RegistrationAction" NOT NULL,
    "performed_by" "uuid",
    "notes" "text",
    "previous_status" "public"."RegistrationStatus",
    "new_status" "public"."RegistrationStatus" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."registration_approval_history" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."registration_requests" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "entity_type" "public"."EntityType" NOT NULL,
    "registration_data" "jsonb" NOT NULL,
    "status" "public"."RegistrationStatus" DEFAULT 'PENDING'::"public"."RegistrationStatus",
    "submitted_at" timestamp with time zone DEFAULT "now"(),
    "reviewed_at" timestamp with time zone,
    "reviewed_by" "uuid",
    "admin_notes" "text",
    "rejection_reason" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."registration_requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."roles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(50) NOT NULL,
    "description" "text",
    "is_system" boolean DEFAULT false,
    "is_admin_role" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."roles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_roles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "role_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."user_roles" OWNER TO "postgres";


ALTER TABLE ONLY "public"."account_activation_history"
    ADD CONSTRAINT "account_activation_history_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."account_setup_progress"
    ADD CONSTRAINT "account_setup_progress_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."account_setup_progress"
    ADD CONSTRAINT "account_setup_progress_user_id_step_number_key" UNIQUE ("user_id", "step_number");



ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."consultation_requests"
    ADD CONSTRAINT "consultation_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."consultations"
    ADD CONSTRAINT "consultations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."expert_documents"
    ADD CONSTRAINT "expert_documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."expert_profiles"
    ADD CONSTRAINT "expert_profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."expert_verification_history"
    ADD CONSTRAINT "expert_verification_history_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."client_profiles"
    ADD CONSTRAINT "farmer_profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_phone_number_key" UNIQUE ("phone_number");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."registration_approval_history"
    ADD CONSTRAINT "registration_approval_history_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."registration_requests"
    ADD CONSTRAINT "registration_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."roles"
    ADD CONSTRAINT "roles_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."roles"
    ADD CONSTRAINT "roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_role_id_key" UNIQUE ("user_id", "role_id");



CREATE INDEX "idx_account_activation_history_action" ON "public"."account_activation_history" USING "btree" ("action");



CREATE INDEX "idx_account_activation_history_user_id" ON "public"."account_activation_history" USING "btree" ("user_id");



CREATE INDEX "idx_account_setup_progress_user_id" ON "public"."account_setup_progress" USING "btree" ("user_id");



CREATE INDEX "idx_account_setup_progress_user_step" ON "public"."account_setup_progress" USING "btree" ("user_id", "step_number");



CREATE INDEX "idx_assets_owner_status" ON "public"."assets" USING "btree" ("owner_id", "status");



CREATE INDEX "idx_consultation_requests_asset_id" ON "public"."consultation_requests" USING "btree" ("asset_id");



CREATE INDEX "idx_expert_documents_document_type" ON "public"."expert_documents" USING "btree" ("document_type");



CREATE INDEX "idx_expert_documents_expert_id" ON "public"."expert_documents" USING "btree" ("expert_id");



CREATE INDEX "idx_expert_documents_status" ON "public"."expert_documents" USING "btree" ("verification_status");



CREATE INDEX "idx_expert_documents_verification_status" ON "public"."expert_documents" USING "btree" ("verification_status");



CREATE INDEX "idx_expert_profiles_verification_status" ON "public"."expert_profiles" USING "btree" ("verification_status");



CREATE INDEX "idx_expert_profiles_verified_at" ON "public"."expert_profiles" USING "btree" ("verified_at");



CREATE INDEX "idx_expert_verification_history_created_at" ON "public"."expert_verification_history" USING "btree" ("created_at");



CREATE INDEX "idx_expert_verification_history_expert_id" ON "public"."expert_verification_history" USING "btree" ("expert_id");



CREATE INDEX "idx_profiles_avatar_url" ON "public"."profiles" USING "btree" ("avatar_url") WHERE ("avatar_url" IS NOT NULL);



CREATE INDEX "idx_registration_approval_history_request_id" ON "public"."registration_approval_history" USING "btree" ("request_id");



CREATE INDEX "idx_registration_requests_entity_type" ON "public"."registration_requests" USING "btree" ("entity_type");



CREATE INDEX "idx_registration_requests_entity_type_status" ON "public"."registration_requests" USING "btree" ("entity_type", "status", "submitted_at" DESC);



CREATE INDEX "idx_registration_requests_status" ON "public"."registration_requests" USING "btree" ("status");



CREATE INDEX "idx_registration_requests_status_submitted" ON "public"."registration_requests" USING "btree" ("status", "submitted_at" DESC);



CREATE INDEX "idx_registration_requests_user_id" ON "public"."registration_requests" USING "btree" ("user_id");



CREATE INDEX "idx_registration_requests_user_id_status" ON "public"."registration_requests" USING "btree" ("user_id", "status");



CREATE INDEX "idx_user_roles_user_id" ON "public"."user_roles" USING "btree" ("user_id");



CREATE INDEX "messages_consultation_id_idx" ON "public"."messages" USING "btree" ("consultation_id");



CREATE INDEX "messages_sender_id_idx" ON "public"."messages" USING "btree" ("sender_id");



CREATE OR REPLACE TRIGGER "on_registration_approved" AFTER UPDATE ON "public"."registration_requests" FOR EACH ROW WHEN ((("old"."status" IS DISTINCT FROM "new"."status") AND ("new"."status" = 'APPROVED'::"public"."RegistrationStatus"))) EXECUTE FUNCTION "public"."handle_registration_approval"();



CREATE OR REPLACE TRIGGER "validate_consultation_asset_trigger" BEFORE INSERT OR UPDATE ON "public"."consultation_requests" FOR EACH ROW EXECUTE FUNCTION "public"."validate_consultation_asset"();



ALTER TABLE ONLY "public"."account_activation_history"
    ADD CONSTRAINT "account_activation_history_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."account_setup_progress"
    ADD CONSTRAINT "account_setup_progress_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."consultation_requests"
    ADD CONSTRAINT "consultation_requests_asset_id_fkey" FOREIGN KEY ("asset_id") REFERENCES "public"."assets"("id");



ALTER TABLE ONLY "public"."consultation_requests"
    ADD CONSTRAINT "consultation_requests_expert_id_fkey" FOREIGN KEY ("expert_id") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."consultation_requests"
    ADD CONSTRAINT "consultation_requests_requester_id_fkey" FOREIGN KEY ("requester_id") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."consultations"
    ADD CONSTRAINT "consultations_cancelled_by_fkey" FOREIGN KEY ("cancelled_by") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."consultations"
    ADD CONSTRAINT "consultations_expert_id_fkey" FOREIGN KEY ("expert_id") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."consultations"
    ADD CONSTRAINT "consultations_farmer_id_fkey" FOREIGN KEY ("farmer_id") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."consultations"
    ADD CONSTRAINT "consultations_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."consultation_requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."expert_documents"
    ADD CONSTRAINT "expert_documents_expert_id_fkey" FOREIGN KEY ("expert_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."expert_documents"
    ADD CONSTRAINT "expert_documents_reviewed_by_fkey" FOREIGN KEY ("reviewed_by") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."expert_profiles"
    ADD CONSTRAINT "expert_profiles_verified_by_fkey" FOREIGN KEY ("verified_by") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."expert_verification_history"
    ADD CONSTRAINT "expert_verification_history_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."expert_verification_history"
    ADD CONSTRAINT "expert_verification_history_expert_id_fkey" FOREIGN KEY ("expert_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."client_profiles"
    ADD CONSTRAINT "farmer_profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



COMMENT ON CONSTRAINT "farmer_profiles_id_fkey" ON "public"."client_profiles" IS 'Foreign key constraint linking farmer_profiles to profiles table';



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_consultation_id_fkey" FOREIGN KEY ("consultation_id") REFERENCES "public"."consultation_requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_sender_id_fkey" FOREIGN KEY ("sender_id") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."registration_approval_history"
    ADD CONSTRAINT "registration_approval_history_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."registration_requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."registration_requests"
    ADD CONSTRAINT "registration_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



CREATE POLICY "Admins can delete any asset" ON "public"."assets" FOR DELETE USING ("public"."is_user_admin"());



CREATE POLICY "Admins can delete approval history" ON "public"."registration_approval_history" FOR DELETE USING ("public"."is_user_admin"());



CREATE POLICY "Admins can insert any asset" ON "public"."assets" FOR INSERT WITH CHECK ("public"."is_user_admin"());



CREATE POLICY "Admins can insert approval history" ON "public"."registration_approval_history" FOR INSERT WITH CHECK ("public"."is_user_admin"());



CREATE POLICY "Admins can insert verification history" ON "public"."expert_verification_history" FOR INSERT WITH CHECK (( SELECT "public"."is_profile_admin"("auth"."uid"()) AS "is_profile_admin"));



CREATE POLICY "Admins can update all documents" ON "public"."expert_documents" FOR UPDATE USING (( SELECT "public"."is_profile_admin"("auth"."uid"()) AS "is_profile_admin"));



CREATE POLICY "Admins can update all profiles" ON "public"."profiles" FOR UPDATE USING ("public"."is_profile_admin"()) WITH CHECK ("public"."is_profile_admin"());



CREATE POLICY "Admins can update any asset" ON "public"."assets" FOR UPDATE USING ("public"."is_user_admin"()) WITH CHECK ("public"."is_user_admin"());



CREATE POLICY "Admins can update approval history" ON "public"."registration_approval_history" FOR UPDATE USING ("public"."is_user_admin"()) WITH CHECK ("public"."is_user_admin"());



CREATE POLICY "Admins can update registration requests" ON "public"."registration_requests" FOR UPDATE USING ("public"."is_user_admin"());



CREATE POLICY "Admins can view all approval history" ON "public"."registration_approval_history" FOR SELECT USING ("public"."is_user_admin"());



CREATE POLICY "Admins can view all assets" ON "public"."assets" FOR SELECT USING ("public"."is_user_admin"());



CREATE POLICY "Admins can view all client profiles" ON "public"."client_profiles" FOR SELECT USING ("public"."is_profile_admin"());



CREATE POLICY "Admins can view all documents" ON "public"."expert_documents" FOR SELECT USING (( SELECT "public"."is_profile_admin"("auth"."uid"()) AS "is_profile_admin"));



CREATE POLICY "Admins can view all expert documents" ON "public"."expert_documents" FOR SELECT USING (( SELECT "public"."is_profile_admin"("auth"."uid"()) AS "is_profile_admin"));



CREATE POLICY "Admins can view all expert profiles" ON "public"."expert_profiles" FOR SELECT USING ("public"."is_profile_admin"());



CREATE POLICY "Admins can view all profiles" ON "public"."profiles" FOR SELECT USING ("public"."is_profile_admin"());



CREATE POLICY "Admins can view all registration requests" ON "public"."registration_requests" FOR SELECT USING ("public"."is_user_admin"());



CREATE POLICY "Admins can view all verification history" ON "public"."expert_verification_history" FOR SELECT USING (( SELECT "public"."is_profile_admin"("auth"."uid"()) AS "is_profile_admin"));



CREATE POLICY "Admins have full access to activation history" ON "public"."account_activation_history" USING ((EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."roles" "r" ON (("ur"."role_id" = "r"."id")))
  WHERE (("ur"."user_id" = "auth"."uid"()) AND (("r"."name")::"text" = 'admin'::"text")))));



CREATE POLICY "Admins have full access to consultations" ON "public"."consultation_requests" USING (("public"."get_my_role"() = 'admin'::"text"));



CREATE POLICY "Admins have full access to setup progress" ON "public"."account_setup_progress" USING ((EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."roles" "r" ON (("ur"."role_id" = "r"."id")))
  WHERE (("ur"."user_id" = "auth"."uid"()) AND (("r"."name")::"text" = 'admin'::"text")))));



CREATE POLICY "Experts can insert own documents" ON "public"."expert_documents" FOR INSERT WITH CHECK (("auth"."uid"() = "expert_id"));



CREATE POLICY "Experts can update own documents" ON "public"."expert_documents" FOR UPDATE USING (("auth"."uid"() = "expert_id"));



CREATE POLICY "Experts can update own pending documents" ON "public"."expert_documents" FOR UPDATE USING ((("auth"."uid"() = "expert_id") AND ("verification_status" = 'pending'::"public"."expert_verification_status")));



CREATE POLICY "Experts can view own documents" ON "public"."expert_documents" FOR SELECT USING (("auth"."uid"() = "expert_id"));



CREATE POLICY "Experts can view own verification history" ON "public"."expert_verification_history" FOR SELECT USING (("auth"."uid"() = "expert_id"));



CREATE POLICY "Experts can view requests assigned to them" ON "public"."consultation_requests" FOR SELECT USING ((("public"."get_my_role"() = 'expert'::"text") AND ("auth"."uid"() = "expert_id")));



CREATE POLICY "Users can delete their own assets" ON "public"."assets" FOR DELETE USING (("owner_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can insert their own activation history" ON "public"."account_activation_history" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert their own assets" ON "public"."assets" FOR INSERT WITH CHECK (("owner_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can insert their own registration requests" ON "public"."registration_requests" FOR INSERT WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can manage their own consultation requests" ON "public"."consultation_requests" USING (("auth"."uid"() = "requester_id"));



CREATE POLICY "Users can manage their own setup progress" ON "public"."account_setup_progress" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their own assets" ON "public"."assets" FOR UPDATE USING (("owner_id" = ( SELECT "auth"."uid"() AS "uid"))) WITH CHECK (("owner_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can update their own profile" ON "public"."profiles" FOR UPDATE USING (("id" = ( SELECT "auth"."uid"() AS "uid"))) WITH CHECK (("id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view their own activation history" ON "public"."account_activation_history" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own approval history" ON "public"."registration_approval_history" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."registration_requests" "rr"
  WHERE (("rr"."id" = "registration_approval_history"."request_id") AND ("rr"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))));



CREATE POLICY "Users can view their own assets" ON "public"."assets" FOR SELECT USING (("owner_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view their own profile" ON "public"."profiles" FOR SELECT USING (("id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view their own registration requests" ON "public"."registration_requests" FOR SELECT USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



ALTER TABLE "public"."account_activation_history" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."account_setup_progress" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."assets" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."consultation_requests" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."expert_documents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."expert_profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."expert_verification_history" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."messages" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "messages: insert participants" ON "public"."messages" FOR INSERT TO "authenticated" WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."consultation_requests" "c"
  WHERE (("c"."id" = "messages"."consultation_id") AND (("c"."requester_id" = "auth"."uid"()) OR ("c"."expert_id" = "auth"."uid"()))))) AND ("sender_id" = "auth"."uid"())));



CREATE POLICY "messages: select participants" ON "public"."messages" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."consultation_requests" "c"
  WHERE (("c"."id" = "messages"."consultation_id") AND (("c"."requester_id" = "auth"."uid"()) OR ("c"."expert_id" = "auth"."uid"()))))));



ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."registration_approval_history" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."registration_requests" ENABLE ROW LEVEL SECURITY;


REVOKE USAGE ON SCHEMA "public" FROM PUBLIC;
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";



GRANT ALL ON FUNCTION "public"."activate_user_account"("user_id" "uuid", "performed_by" "uuid", "notes" "text") TO "authenticated";



GRANT ALL ON FUNCTION "public"."check_expert_consultation_access"("p_expert_id" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."extract_asset_data_from_registration"("p_registration_data" "jsonb") TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_account_setup_progress"("user_id" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_expert_verification_details"("p_expert_id" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_experts_pending_verification"() TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_my_role"() TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_my_verification_status"() TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_registration_request_details"("p_request_id" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_registration_requests"("p_limit" integer, "p_offset" integer, "p_entity_type" "public"."EntityType", "p_status" "public"."RegistrationStatus", "p_search_term" "text") TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_user_role"() TO "authenticated";



GRANT ALL ON FUNCTION "public"."get_user_role"("user_uuid" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."handle_registration_approval"() TO "authenticated";



GRANT ALL ON FUNCTION "public"."is_account_activated"("user_id" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."is_admin"() TO "authenticated";



GRANT ALL ON FUNCTION "public"."is_admin_simple"() TO "authenticated";



GRANT ALL ON FUNCTION "public"."is_profile_admin"("user_uuid" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."is_user_admin"("user_uuid" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."migrate_approved_registrations_to_assets"() TO "authenticated";



GRANT ALL ON FUNCTION "public"."reject_registration_request"("p_request_id" "uuid", "p_performed_by" "uuid", "p_rejection_reason" "text", "p_admin_notes" "text") TO "authenticated";



GRANT ALL ON FUNCTION "public"."submit_registration_request"("user_id" "uuid", "entity_type" "text", "registration_data" "jsonb") TO "authenticated";



GRANT ALL ON FUNCTION "public"."submit_registration_request_typed"("user_id" "uuid", "entity_type" "public"."EntityType", "registration_data" "jsonb") TO "authenticated";



GRANT ALL ON FUNCTION "public"."transfer_registration_to_assets"("request_id" "uuid") TO "authenticated";



GRANT ALL ON FUNCTION "public"."update_expert_verification_status"("p_expert_id" "uuid", "p_new_status" "public"."expert_verification_status", "p_admin_notes" "text", "p_rejection_reasons" "text"[]) TO "authenticated";



GRANT ALL ON FUNCTION "public"."upload_expert_verification_document"("p_document_type" character varying, "p_document_url" "text", "p_document_name" character varying, "p_file_size" integer, "p_file_type" character varying) TO "authenticated";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."account_activation_history" TO "anon";
GRANT ALL ON TABLE "public"."account_activation_history" TO "authenticated";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."account_setup_progress" TO "anon";
GRANT ALL ON TABLE "public"."account_setup_progress" TO "authenticated";



GRANT ALL ON TABLE "public"."assets" TO "anon";
GRANT ALL ON TABLE "public"."assets" TO "authenticated";



GRANT ALL ON TABLE "public"."client_profiles" TO "anon";
GRANT ALL ON TABLE "public"."client_profiles" TO "authenticated";



GRANT ALL ON TABLE "public"."consultation_requests" TO "anon";
GRANT ALL ON TABLE "public"."consultation_requests" TO "authenticated";



GRANT ALL ON TABLE "public"."consultations" TO "anon";
GRANT ALL ON TABLE "public"."consultations" TO "authenticated";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."expert_documents" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."expert_documents" TO "authenticated";



GRANT ALL ON TABLE "public"."expert_profiles" TO "anon";
GRANT ALL ON TABLE "public"."expert_profiles" TO "authenticated";



GRANT UPDATE("verification_status") ON TABLE "public"."expert_profiles" TO "authenticated";



GRANT UPDATE("updated_at") ON TABLE "public"."expert_profiles" TO "authenticated";



GRANT UPDATE("documents_required") ON TABLE "public"."expert_profiles" TO "authenticated";



GRANT UPDATE("verified_at") ON TABLE "public"."expert_profiles" TO "authenticated";



GRANT UPDATE("verified_by") ON TABLE "public"."expert_profiles" TO "authenticated";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."expert_verification_history" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."expert_verification_history" TO "authenticated";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."messages" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."messages" TO "authenticated";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."registration_approval_history" TO "anon";
GRANT ALL ON TABLE "public"."registration_approval_history" TO "authenticated";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."registration_requests" TO "anon";
GRANT ALL ON TABLE "public"."registration_requests" TO "authenticated";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."roles" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."roles" TO "authenticated";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."user_roles" TO "anon";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."user_roles" TO "authenticated";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT,USAGE ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT,USAGE ON SEQUENCES  TO "authenticated";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT,INSERT,DELETE,UPDATE ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT SELECT,INSERT,DELETE,UPDATE ON TABLES  TO "authenticated";



RESET ALL;
