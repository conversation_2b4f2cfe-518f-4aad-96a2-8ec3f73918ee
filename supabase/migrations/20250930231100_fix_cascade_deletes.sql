-- This script fixes the foreign key constraints for expert-related tables to ensure
-- that when a profile is deleted, all associated expert data is also deleted.

-- Fix for expert_documents table
ALTER TABLE public.expert_documents
DROP CONSTRAINT IF EXISTS expert_documents_expert_id_fkey;

ALTER TABLE public.expert_documents
ADD CONSTRAINT expert_documents_expert_id_fkey
FOREIGN KEY (expert_id)
REFERENCES public.profiles(id)
ON DELETE CASCADE;

-- Fix for expert_verification_history table
ALTER TABLE public.expert_verification_history
DROP CONSTRAINT IF EXISTS expert_verification_history_expert_id_fkey;

ALTER TABLE public.expert_verification_history
ADD CONSTRAINT expert_verification_history_expert_id_fkey
FOREIGN KEY (expert_id)
REFERENCES public.profiles(id)
ON DELETE CASCADE;
