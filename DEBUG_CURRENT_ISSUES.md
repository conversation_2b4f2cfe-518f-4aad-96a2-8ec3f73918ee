# Debug Current Issues

## 🔍 **Issue 1: User Type Shows 'Unknown'**

### **Debug Steps Added:**
1. **Enhanced logging in getUserTypeDisplay()** - will show what expert_type values are found
2. **Added expert_profiles data logging** - will show if expert_profiles are being fetched
3. **Added fallback display** - shows role if expert_type is missing

### **What to Check in Console:**
```
Getting user type for user: [id] expert_type: [value]
Expert type found: [AGRICULTURE/INDUSTRIAL]
Expert profiles data: [array of expert profile objects]
User type display results: [array of display results]
```

### **Expected Values:**
- `expert_type` should be `'AGRICULTURE'` or `'INDUSTRIAL'`
- If showing 'Unknown', check if `expert_profiles` is null or `expert_type` is null

## 🔍 **Issue 2: Expert Details Page Problems**

### **Debug Steps Added:**
1. **Enhanced data fetching** - removed complex database function, using direct query
2. **Added comprehensive logging** - shows user data, expert profiles, isExpert status
3. **Fixed title setting** - no longer uses expert name in title

### **What to Check in Console:**
```
Fetching user details for ID: [id]
User data fetched: [user object]
Expert profiles data: [expert profile object]
User object: [complete user data]
Is expert: [true/false]
Expert profiles: [expert profile data]
Verification status: [pending/approved/etc]
```

### **Expected Behavior:**
- `User data fetched` should show complete user object with expert_profiles
- `Is expert` should be `true` for expert users
- `Expert profiles` should show all verification form fields
- Tabs should appear if `isExpert` is true

## 🚀 **Testing Steps**

### **Step 1: Test Users List Page**
1. Go to `/dashboard/users`
2. Check browser console for:
   - "Expert profiles data: [array]"
   - "Getting user type for user: [id] expert_type: [value]"
   - "Expert type found: AGRICULTURE" or "Expert type found: INDUSTRIAL"

### **Step 2: Test Expert Details Page**
1. Go to `/dashboard/users/[expert-id]`
2. Check browser console for:
   - "Fetching user details for ID: [id]"
   - "User data fetched: [object with expert_profiles]"
   - "Is expert: true"
   - "Expert profiles: [object with all fields]"

### **Step 3: Verify UI Elements**
1. **Users List**: User Type column should show "Agriculture Expert" or "Industrial Expert"
2. **Expert Details**: Should show tabs (Profile, Verification Status, Documents, History)
3. **Expert Header**: Should NOT show expert name, only email

## 🔧 **Potential Issues & Solutions**

### **If User Type Still Shows 'Unknown':**
**Possible Causes:**
1. `expert_profiles.expert_type` is NULL in database
2. Query not fetching expert_profiles properly
3. RLS policy blocking expert_profiles access

**Solutions:**
```sql
-- Check if expert_type values exist
SELECT id, expert_type FROM expert_profiles WHERE expert_type IS NOT NULL;

-- Update NULL expert_type values
UPDATE expert_profiles SET expert_type = 'AGRICULTURE' WHERE expert_type IS NULL;
```

### **If Expert Details Page Shows No Tabs:**
**Possible Causes:**
1. `user.expert_profiles` is null (isExpert = false)
2. Query not fetching expert_profiles data
3. User is not actually an expert

**Solutions:**
```sql
-- Check if user has expert_profiles record
SELECT p.id, p.first_name, p.last_name, ep.expert_type, ep.verification_status
FROM profiles p
LEFT JOIN expert_profiles ep ON p.id = ep.id
WHERE p.id = '[user-id]';
```

### **If Expert Name Still Shows in Header:**
- Clear browser cache and refresh
- Check if the correct file was edited

## 📋 **Quick Database Checks**

### **Check Expert Types:**
```sql
SELECT expert_type, COUNT(*) 
FROM expert_profiles 
GROUP BY expert_type;
```

### **Check Specific User:**
```sql
SELECT p.*, ep.expert_type, ep.verification_status
FROM profiles p
LEFT JOIN expert_profiles ep ON p.id = ep.id
WHERE p.id = 'c6645c34-ea98-4852-b56b-ca4de7d1c01c';
```

### **Update Expert Types if NULL:**
```sql
-- Set default expert types for users without them
UPDATE expert_profiles 
SET expert_type = 'AGRICULTURE' 
WHERE expert_type IS NULL;
```

## 🎯 **Next Steps**

1. **Refresh both pages** and check console logs
2. **Report what the console shows** for both issues
3. **Run database queries** if data seems missing
4. **Apply fixes** based on what the debugging reveals

The enhanced logging will help us identify exactly where the issues are occurring!
