# RLS Issue Identified and Fixed

## 🎯 **Root Cause: RLS Policy Mismatch**

The issue is that the RLS policies are using the wrong admin check function.

### **Current RLS Policy:**
```sql
CREATE POLICY "Admins can view all profiles" ON "public"."profiles" 
FOR SELECT 
USING ("public"."is_user_admin"());
```

### **The Problem:**
The `is_user_admin()` function checks for admin role in a **different table structure**:
```sql
-- is_user_admin() looks for:
SELECT EXISTS (
    SELECT 1
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid()
    AND r.name = 'admin'
);
```

### **But Your Admin Users Are Stored As:**
```sql
-- Your admins are in profiles table:
SELECT * FROM profiles WHERE role = 'admin';
-- Returns: 2 admin users
```

## 🔧 **Solution: New RLS Migration**

Created `20251004_fix_admin_rls_policies.sql` that:

### **1. Creates New Admin Check Function:**
```sql
CREATE OR REPLACE FUNCTION "public"."is_profile_admin"("user_uuid" "uuid" DEFAULT "auth"."uid"()) 
RETURNS boolean 
LANGUAGE "sql" 
STABLE 
SECURITY DEFINER 
AS $$
SELECT EXISTS (
    SELECT 1
    FROM public.profiles p
    WHERE p.id = COALESCE(user_uuid, auth.uid())
    AND p.role = 'admin'
);
$$;
```

### **2. Updates RLS Policies:**
```sql
-- Drop old policies
DROP POLICY IF EXISTS "Admins can view all profiles" ON "public"."profiles";
DROP POLICY IF EXISTS "Admins can update all profiles" ON "public"."profiles";

-- Create new policies with correct function
CREATE POLICY "Admins can view all profiles" ON "public"."profiles" 
FOR SELECT 
USING ("public"."is_profile_admin"());

CREATE POLICY "Admins can update all profiles" ON "public"."profiles" 
FOR UPDATE 
USING ("public"."is_profile_admin"()) 
WITH CHECK ("public"."is_profile_admin"());
```

### **3. Adds Policies for Related Tables:**
```sql
-- Expert profiles access
CREATE POLICY "Admins can view all expert profiles" ON "public"."expert_profiles" 
FOR SELECT 
USING ("public"."is_profile_admin"());

-- Client profiles access
CREATE POLICY "Admins can view all client profiles" ON "public"."client_profiles" 
FOR SELECT 
USING ("public"."is_profile_admin"());
```

## 🚀 **How to Apply the Fix**

### **Option 1: Apply Migration File**
```bash
cd /path/to/your/project
supabase db push
```

### **Option 2: Run SQL Directly in Supabase Dashboard**
Copy and paste the contents of `supabase/migrations/verification_fixes/20251004_fix_admin_rls_policies.sql` into the SQL editor.

### **Option 3: Quick Test (Temporary)**
If you want to test immediately, you can temporarily disable RLS:
```sql
-- TEMPORARY - for testing only
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
-- Remember to re-enable after testing:
-- ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
```

## 🔍 **What to Expect After Fix**

### **Console Output Should Show:**
```
is_user_admin() result: false (or error)
is_profile_admin() result: true
Fetched expert users: [array with 6 users]
Expert count: 6
Expert roles found: ["agriculture_expert", "industrial_expert", "expert", ...]
```

### **Users Page Should Show:**
- **Experts Tab**: 6 users (3 agriculture_expert + 2 industrial_expert + 1 expert)
- **Farmers Tab**: 2 users (1 farmer + 1 factory_worker)
- **User Type Column**: Proper labels for each user type

## 📋 **Debug Steps After Migration**

1. **Apply the migration**
2. **Refresh `/dashboard/users` page**
3. **Check console for:**
   - `is_profile_admin() result: true`
   - `Fetched expert users: [array with data]`
   - `Expert count: 6`
4. **Verify users appear in both tabs**

## 🎯 **Summary**

**Issue**: RLS policies using wrong admin check function
**Root Cause**: `is_user_admin()` checks `user_roles` table, but admins are in `profiles.role`
**Solution**: New `is_profile_admin()` function that checks `profiles.role = 'admin'`
**Status**: ✅ **MIGRATION READY TO APPLY**

**After applying this migration, you should see all 8 users (6 experts + 2 farmers) in the admin panel!** 🎉
