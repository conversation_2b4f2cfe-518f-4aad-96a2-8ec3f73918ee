# Expert Verification Feature - Comprehensive Development Plan

## 📋 Executive Summary

This comprehensive development plan addresses the complete finalization of the expert verification feature, incorporating both expert-facing UI/UX enhancements and critical admin-facing functionalities. The plan resolves existing backend console errors and implements the requirements specified in the project context.

**Key Issues Identified:**
- 406 Not Acceptable errors from `.single()` queries on potentially missing profiles
- 400 Bad Request errors from querying `verification_status` on wrong table
- No test data causing empty admin dashboards
- UI/UX improvements needed for expert verification flow
- Missing admin functionality for expert management

---

## 🚨 Phase 1: Critical Backend Error Resolution

### Task 1.1: Fix 406 Not Acceptable Errors
**Priority: CRITICAL | Files: `layout.tsx`, `user-profile.tsx`**

**Problem:** Queries using `.single()` fail when no profile exists, causing 406 errors.

**Solution:**
```typescript
// BEFORE (Causes 406 errors)
const { data: profileData, error } = await supabase
  .from("profiles")
  .select("*")
  .eq("id", userId)
  .single(); // ❌ Throws error if no rows found

// AFTER (Handles missing profiles gracefully)
const { data: profileData, error } = await supabase
  .from("profiles")
  .select("*")
  .eq("id", userId)
  .maybeSingle(); // ✅ Returns null if no rows found
```

### Task 1.2: Fix 400 Bad Request Errors
**Priority: CRITICAL | File: `src/app/dashboard/users/page.tsx`**

**Problem:** Querying `verification_status` from `profiles` table instead of `expert_profiles`.

**Current Broken Query:**
```typescript
.select(`id, first_name, last_name, email, created_at, verification_status`)
```

**Fixed Query:**
```typescript
.select(`
  id, first_name, last_name, email, created_at, account_activated,
  expert_profiles!inner(
    verification_status,
    bio,
    years_of_experience,
    education,
    expert_type
  )
`)
```

### Task 1.3: Create Test Data for Expert Verification
**Priority: HIGH | Database seeding**

**Implementation:**
```sql
-- Insert test expert profiles with different statuses
INSERT INTO public.expert_profiles (id, verification_status, bio, expertise_area, years_of_experience)
VALUES 
  ('expert-1-uuid', 'pending', 'Agricultural expert with 5 years experience', 'Agriculture', 5),
  ('expert-2-uuid', 'under_review', 'Industrial engineer specializing in automation', 'Industrial', 8),
  ('expert-3-uuid', 'approved', 'Senior agricultural consultant', 'Agriculture', 12);
```

---

## 🎨 Phase 2: Expert-Facing UI/UX Enhancements

### Task 2.1: Redesign Verification Status Banner
**Priority: HIGH | File: `src/components/verification-status-banner.tsx`**

**Changes Required:**
1. **Styling Updates:**
   - Background: Light yellow (`#FFFFE0`)
   - Text: Black (`#000000`)
   - Remove status badges and exclamation icons
   - Button: Black background with white text

2. **Arabic Translations:**
```json
{
  "verification": {
    "completeVerification": "أكمل عملية التحقق من الخبرة",
    "waitingForVerification": "في انتظار التحقق",
    "startVerification": "ابدأ التحقق"
  }
}
```

3. **Status Logic Updates:**
```typescript
const getStatusStyling = () => ({
  backgroundColor: '#FFFFE0',
  color: '#000000',
  border: '1px solid #E5E5E5'
});
```

### Task 2.2: Overhaul Verification Form Page
**Priority: HIGH | File: `src/app/dashboard/verification-pending/page.tsx`**

**Major Changes:**
1. **Remove Tabbed Interface:** Single scrollable form with horizontal separators
2. **Update Content:**
   - Title: "Verify Your Account to Get Started"
   - Description: "Fill in the form below to be reviewed by our team and get started"
3. **Document Upload Integration:** Add upload components under each section
4. **RTL Support:** Full Arabic layout support
5. **Remove Status Icons:** Clean, professional appearance

### Task 2.3: Implement Non-Invasive Feature Locking
**Priority: MEDIUM | New Component: `src/components/verification-overlay.tsx`**

**Implementation:**
```typescript
export function VerificationOverlay() {
  const { role, expertProfile } = useUserProfile();
  
  const shouldShow = role === 'expert' && 
    expertProfile?.verification_status !== 'approved';

  if (!shouldShow) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50">
      {/* Overlay content */}
    </div>
  );
}
```

---

## 🛠️ Phase 3: Admin Dashboard Functionality

### Task 3.1: Fix User Management Data Fetching
**Priority: CRITICAL | File: `src/app/dashboard/users/page.tsx`**

**Expert Query Fix:**
```typescript
// Fixed query with proper joins
const query = supabase
  .from("profiles")
  .select(`
    id, first_name, last_name, email, created_at, account_activated,
    expert_profiles!inner(
      verification_status,
      bio,
      years_of_experience,
      education,
      expert_type
    )
  `);
```

**Client Query Implementation:**
```typescript
const { data: clients } = await supabase
  .from('profiles')
  .select(`
    id, first_name, last_name, email, created_at, is_active,
    client_profiles!inner(client_type, created_at)
  `);
```

### Task 3.2: Enhance Expert Detail Page Functionality
**Priority: HIGH | File: `src/app/dashboard/users/[id]/page.tsx`**

**Data Fetching Enhancement:**
```typescript
const { data, error } = await supabase.rpc('get_expert_verification_details', {
  p_expert_id: id
});
```

**Add Consultations Tab:**
```typescript
{expertData?.verification_status === 'approved' && (
  <TabsContent value="consultations">
    <ConsultationsList expertId={id} />
  </TabsContent>
)}
```

### Task 3.3: Build Client Detail Page
**Priority: MEDIUM | New File: `src/app/dashboard/clients/[id]/page.tsx`**

**Features:**
- Complete client profile display
- Associated assets listing
- Registration history
- Contact information management

---

## 📋 Implementation Checklist

### Phase 1 - Backend Fixes
- [ ] Replace `.single()` with `.maybeSingle()` in profile queries
- [ ] Fix verification_status queries in users page
- [ ] Create test data for expert verification system
- [ ] Verify console errors are resolved

### Phase 2 - Expert UI/UX
- [ ] Update verification banner styling and translations
- [ ] Redesign verification form page layout
- [ ] Implement verification overlay component
- [ ] Test RTL support for Arabic

### Phase 3 - Admin Dashboard
- [ ] Fix expert and client data fetching
- [ ] Enhance expert detail page with database functions
- [ ] Add consultations tab for approved experts
- [ ] Build complete client detail page
- [ ] Implement client routing from users page

---

## 🎯 Success Criteria

1. **No Console Errors:** All 406 and 400 errors resolved
2. **Data Visibility:** Admin dashboard shows experts and clients
3. **Functional Verification:** Complete approve/reject workflow
4. **UI/UX Compliance:** Matches design requirements
5. **Internationalization:** Full Arabic support
6. **Feature Access Control:** Proper verification-based restrictions

---

## 📊 Estimated Timeline

- **Phase 1:** 1-2 days (Critical fixes)
- **Phase 2:** 2-3 days (UI/UX enhancements)
- **Phase 3:** 2-3 days (Admin functionality)
- **Total:** 5-8 days

This plan builds upon the existing `expert_verification_finalization_plan.md` and addresses all identified issues comprehensively.
