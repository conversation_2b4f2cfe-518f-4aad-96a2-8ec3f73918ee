# Asset Registration System - Manual Testing Guide

This guide provides comprehensive test cases for the asset registration approval system implemented in tasks 2.1-2.4.

## Prerequisites

1. **Admin Access**: Ensure you have admin role access to the dashboard
2. **Test Data**: Have some registration requests in the database (status: PENDING, APPROVED, REJECTED)
3. **Database Access**: Verify the database functions are properly installed
4. **Authentication**: Ensure you're logged in as an admin user

## Test Cases

### 1. Asset Registration List Display

#### Test Case 1.1: Basic List Display
**Objective**: Verify the registration requests list displays correctly

**Steps**:
1. Navigate to `/dashboard/management`
2. Verify the page loads without errors
3. Check that the page title shows "Asset Registration Management"
4. Verify the table displays with correct columns:
   - User
   - Asset Details
   - Type
   - Status
   - Submitted
   - Actions

**Expected Results**:
- Page loads successfully
- Table displays registration requests
- All columns are visible and properly formatted
- Data is displayed correctly (user names, asset names, locations, etc.)

#### Test Case 1.2: Empty State
**Objective**: Test behavior when no registration requests exist

**Steps**:
1. Ensure database has no registration requests (or filter to show none)
2. Navigate to `/dashboard/management`
3. Verify empty state message appears

**Expected Results**:
- Empty state shows appropriate message
- No table rows are displayed
- Page doesn't crash or show errors

### 2. Filtering and Search Functionality

#### Test Case 2.1: Entity Type Filtering
**Objective**: Test filtering by FARM/FACTORY entity types

**Steps**:
1. Navigate to `/dashboard/management`
2. Use the "Filter by Type" dropdown
3. Select "Farm" - verify only farm registrations show
4. Select "Factory" - verify only factory registrations show
5. Select "All Types" - verify all registrations show

**Expected Results**:
- Filtering works correctly for each entity type
- URL parameters update appropriately
- Page resets to page 1 when filter changes
- Results match the selected filter

#### Test Case 2.2: Status Filtering
**Objective**: Test filtering by registration status

**Steps**:
1. Navigate to `/dashboard/management`
2. Use the "Filter by Status" dropdown
3. Test each status: Pending, Approved, Rejected, Resubmitted
4. Verify "All Statuses" shows everything

**Expected Results**:
- Each status filter shows only matching records
- Status badges display correctly
- Filtering works in combination with entity type filter

#### Test Case 2.3: Search Functionality
**Objective**: Test search across user names, asset names, and locations

**Steps**:
1. Navigate to `/dashboard/management`
2. Enter a user's name in the search box
3. Verify results show matching users
4. Clear search and enter an asset name
5. Verify results show matching assets
6. Test partial matches and case-insensitive search

**Expected Results**:
- Search works across multiple fields
- Results update as you type
- Partial matches work correctly
- Case-insensitive search functions properly

### 3. Registration Details Modal

#### Test Case 3.1: Modal Opening and Display
**Objective**: Test the registration details modal functionality

**Steps**:
1. Navigate to `/dashboard/management`
2. Click "View Details" on any registration request
3. Verify modal opens with complete information
4. Check all tabs: Basic Info, Location, Contact, Media
5. Verify user information displays correctly

**Expected Results**:
- Modal opens without errors
- All registration data displays correctly
- Tabs switch properly
- User information is accurate
- Asset data is properly formatted

#### Test Case 3.2: Image Gallery
**Objective**: Test image display in the modal

**Steps**:
1. Open details for a registration with images
2. Navigate to the "Media" tab
3. Verify images display correctly
4. Test image loading and error handling

**Expected Results**:
- Images load and display properly
- Image count is accurate
- Error handling works for broken images
- Gallery layout is responsive

#### Test Case 3.3: Approval History
**Objective**: Test approval history display

**Steps**:
1. Open details for a registration with history
2. Scroll to approval history section
3. Verify chronological order
4. Check action badges and timestamps

**Expected Results**:
- History displays in correct order
- Action badges show appropriate colors
- Timestamps are formatted correctly
- Notes display when available

### 4. Approval Workflow

#### Test Case 4.1: Registration Approval
**Objective**: Test the complete approval workflow

**Steps**:
1. Find a PENDING registration request
2. Click "Approve" button (either in list or modal)
3. Verify success notification appears
4. Check that status changes to APPROVED
5. Verify asset is created in assets table
6. Check approval history is logged

**Expected Results**:
- Success toast notification appears
- Registration status updates to APPROVED
- New asset record is created
- User account is activated (if not already)
- Approval history is logged with admin details
- List refreshes automatically

#### Test Case 4.2: Approval Error Handling
**Objective**: Test error scenarios during approval

**Steps**:
1. Test approving an already approved request
2. Test network errors (disconnect internet briefly)
3. Test with invalid request ID

**Expected Results**:
- Appropriate error messages display
- System handles errors gracefully
- No partial state changes occur
- User is informed of the issue

### 5. Rejection Workflow

#### Test Case 5.1: Registration Rejection
**Objective**: Test the complete rejection workflow

**Steps**:
1. Find a PENDING registration request
2. Click "Reject" button
3. Verify rejection modal opens
4. Enter rejection reason (required field)
5. Optionally add admin notes
6. Click "Reject Request"
7. Verify success notification and status change

**Expected Results**:
- Rejection modal opens correctly
- Form validation works (rejection reason required)
- Success notification appears
- Status changes to REJECTED
- Rejection reason is saved
- Admin notes are saved (if provided)
- Rejection history is logged

#### Test Case 5.2: Rejection Modal Validation
**Objective**: Test form validation in rejection modal

**Steps**:
1. Open rejection modal
2. Try to submit without rejection reason
3. Verify validation prevents submission
4. Add rejection reason and submit
5. Test modal cancellation

**Expected Results**:
- Submit button is disabled without rejection reason
- Validation messages appear appropriately
- Cancel button works correctly
- Form resets when reopened

### 6. Pagination

#### Test Case 6.1: Pagination Functionality
**Objective**: Test pagination with large datasets

**Steps**:
1. Ensure there are more than 10 registration requests
2. Navigate through pages using pagination controls
3. Verify page numbers update correctly
4. Test first/last page navigation

**Expected Results**:
- Pagination controls appear when needed
- Page navigation works correctly
- Results update for each page
- Page numbers are accurate

### 7. Edge Cases and Error Handling

#### Test Case 7.1: Malformed JSON Data
**Objective**: Test handling of corrupted registration data

**Steps**:
1. Create a test registration with malformed JSON
2. Verify system handles it gracefully
3. Check error messages are user-friendly

**Expected Results**:
- System doesn't crash with bad data
- Appropriate fallback values are shown
- Error messages are helpful

#### Test Case 7.2: Missing Required Fields
**Objective**: Test handling of incomplete registration data

**Steps**:
1. Test with registrations missing asset names
2. Test with missing location data
3. Verify fallback values display

**Expected Results**:
- "N/A" or appropriate fallbacks show
- System remains stable
- No JavaScript errors occur

#### Test Case 7.3: Concurrent Approvals
**Objective**: Test handling of simultaneous admin actions

**Steps**:
1. Have two admin users attempt to approve the same request
2. Verify only one succeeds
3. Check appropriate error handling

**Expected Results**:
- First approval succeeds
- Second attempt shows appropriate error
- No duplicate assets are created

### 8. Performance Testing

#### Test Case 8.1: Large Dataset Performance
**Objective**: Test system performance with many requests

**Steps**:
1. Test with 100+ registration requests
2. Verify page load times are acceptable
3. Test search and filtering performance

**Expected Results**:
- Page loads within reasonable time (< 3 seconds)
- Search and filtering remain responsive
- Pagination works smoothly

### 9. Mobile Responsiveness

#### Test Case 9.1: Mobile Layout
**Objective**: Test responsive design on mobile devices

**Steps**:
1. Access the page on mobile device or browser dev tools
2. Verify table is responsive
3. Test modal functionality on mobile
4. Check button accessibility

**Expected Results**:
- Layout adapts to mobile screens
- All functionality remains accessible
- Modals display properly on mobile
- Touch interactions work correctly

## Test Data Setup

### SQL Scripts for Test Data Creation

Execute these SQL scripts in your Supabase SQL editor to create comprehensive test data:

#### 1. Create Test Users (if not exists)

```sql
-- Create test users with different profiles
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at, raw_app_meta_data, raw_user_meta_data)
VALUES 
  ('********-1111-1111-1111-********1111', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), now(), now(), '{"provider": "email", "providers": ["email"]}', '{}'),
  ('2222**************-2222-************', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), now(), now(), '{"provider": "email", "providers": ["email"]}', '{}'),
  ('3333**************-3333-************', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), now(), now(), '{"provider": "email", "providers": ["email"]}', '{}'),
  ('********-4444-4444-4444-********4444', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), now(), now(), '{"provider": "email", "providers": ["email"]}', '{}'),
  ('********-5555-5555-5555-********5555', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), now(), now(), '{"provider": "email", "providers": ["email"]}', '{}')
ON CONFLICT (id) DO NOTHING;

-- Create corresponding profiles
INSERT INTO public.profiles (id, email, first_name, last_name, phone_number, entity_type, account_activated, created_at, updated_at)
VALUES 
  ('********-1111-1111-1111-********1111', '<EMAIL>', 'Ahmed', 'Hassan', '+************', 'FARM', false, now(), now()),
  ('2222**************-2222-************', '<EMAIL>', 'Fatima', 'Ali', '+************', 'FARM', false, now(), now()),
  ('3333**************-3333-************', '<EMAIL>', 'Mohamed', 'Ibrahim', '+************', 'FACTORY', false, now(), now()),
  ('********-4444-4444-4444-********4444', '<EMAIL>', 'Aisha', 'Omar', '+************', 'FACTORY', false, now(), now()),
  ('********-5555-5555-5555-********5555', '<EMAIL>', 'Admin', 'User', '+************', null, true, now(), now())
ON CONFLICT (id) DO NOTHING;

-- Create admin role and assign to admin user
INSERT INTO public.roles (id, name, description, is_admin_role, created_at, updated_at)
VALUES ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'admin', 'Administrator role', true, now(), now())
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.user_roles (user_id, role_id, created_at)
VALUES ('********-5555-5555-5555-********5555', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', now())
ON CONFLICT (user_id, role_id) DO NOTHING;
```

#### 2. Create Test Registration Requests

```sql
-- Create test registration requests for user '3424f9f8-6985-4d5f-8602-e7c430fffa5f'
INSERT INTO public.registration_requests (
  id, user_id, entity_type, registration_data, status, submitted_at, created_at, updated_at
) VALUES 
-- PENDING Farm Request 1
(
  'f1111111-1111-1111-1111-********1111',
  '3424f9f8-6985-4d5f-8602-e7c430fffa5f',
  'FARM',
  '{
    "registration_steps": [
      {
        "step_name": "farm_basic_info",
        "step_data": {
          "name": "Green Valley Farm",
          "owner_name": "Test User",
          "size": "50 acres",
          "description": "Organic vegetable farm specializing in tomatoes and cucumbers",
          "location_address": "Kafr El Sheikh, Egypt"
        }
      },
      {
        "step_name": "farm_location",
        "step_data": {
          "location_address": "Village Road 123, Kafr El Sheikh Governorate, Egypt",
          "city": "Kafr El Sheikh",
          "governorate": "Kafr El Sheikh",
          "coordinates": "31.1107, 30.9388"
        }
      },
      {
        "step_name": "farm_contact",
        "step_data": {
          "contact_person": "Test User",
          "phone": "+************",
          "email": "<EMAIL>",
          "alternative_phone": "+201234567800"
        }
      },
      {
        "step_name": "farm_images",
        "step_data": {
          "images": [
            {"url": "https://example.com/farm1_1.jpg", "name": "Farm Overview"},
            {"url": "https://example.com/farm1_2.jpg", "name": "Greenhouse"},
            {"url": "https://example.com/farm1_3.jpg", "name": "Irrigation System"}
          ]
        }
      }
    ]
  }',
  'PENDING',
  now() - interval '2 days',
  now() - interval '2 days',
  now() - interval '2 days'
),

-- PENDING Farm Request 2
(
  'f222**************-2222-************',
  '3424f9f8-6985-4d5f-8602-e7c430fffa5f',
  'FARM',
  '{
    "registration_steps": [
      {
        "step_name": "farm_basic_info",
        "step_data": {
          "name": "Nile Delta Farm",
          "owner_name": "Test User",
          "size": "75 acres",
          "description": "Mixed crop farm with rice, wheat, and corn production",
          "location_address": "Dakahlia, Egypt"
        }
      },
      {
        "step_name": "farm_location",
        "step_data": {
          "location_address": "Rural Road 456, Dakahlia Governorate, Egypt",
          "city": "Mansoura",
          "governorate": "Dakahlia",
          "coordinates": "31.0364, 31.3807"
        }
      },
      {
        "step_name": "farm_contact",
        "step_data": {
          "contact_person": "Test User",
          "phone": "+************",
          "email": "<EMAIL>"
        }
      }
    ]
  }',
  'PENDING',
  now() - interval '1 day',
  now() - interval '1 day',
  now() - interval '1 day'
),

-- APPROVED Farm Request
(
  'f333**************-3333-************',
  '3424f9f8-6985-4d5f-8602-e7c430fffa5f',
  'FARM',
  '{
    "registration_steps": [
      {
        "step_name": "farm_basic_info",
        "step_data": {
          "name": "Sunrise Organic Farm",
          "owner_name": "Test User",
          "size": "30 acres",
          "description": "Certified organic farm producing herbs and leafy greens",
          "location_address": "Giza, Egypt"
        }
      }
    ]
  }',
  'APPROVED',
  now() - interval '5 days',
  now() - interval '5 days',
  now() - interval '3 days'
),

-- Factory Registration Requests
-- PENDING Factory Request 1
(
  'c1111111-1111-1111-1111-********1111',
  '3424f9f8-6985-4d5f-8602-e7c430fffa5f',
  'FACTORY',
  '{
    "registration_steps": [
      {
        "step_name": "factory_basic_info",
        "step_data": {
          "name": "Cairo Food Processing Plant",
          "owner_name": "Test User",
          "size": "5000 sqm",
          "description": "Modern food processing facility for fruits and vegetables",
          "location_address": "Industrial Zone, 6th of October City, Egypt"
        }
      },
      {
        "step_name": "factory_location",
        "step_data": {
          "location_address": "Industrial Complex Block A, 6th of October City, Giza, Egypt",
          "city": "6th of October City",
          "governorate": "Giza",
          "coordinates": "29.9097, 30.9746"
        }
      },
      {
        "step_name": "factory_contact",
        "step_data": {
          "contact_person": "Test User",
          "phone": "+************",
          "email": "<EMAIL>",
          "alternative_phone": "+201234567802"
        }
      },
      {
        "step_name": "factory_images",
        "step_data": {
          "images": [
            {"url": "https://example.com/factory1_1.jpg", "name": "Factory Exterior"},
            {"url": "https://example.com/factory1_2.jpg", "name": "Production Line"},
            {"url": "https://example.com/factory1_3.jpg", "name": "Quality Control Lab"},
            {"url": "https://example.com/factory1_4.jpg", "name": "Storage Area"}
          ]
        }
      }
    ]
  }',
  'PENDING',
  now() - interval '3 days',
  now() - interval '3 days',
  now() - interval '3 days'
),

-- APPROVED Factory Request
(
  'c222**************-2222-************',
  '3424f9f8-6985-4d5f-8602-e7c430fffa5f',
  'FACTORY',
  '{
    "registration_steps": [
      {
        "step_name": "factory_basic_info",
        "step_data": {
          "name": "Alexandria Textile Factory",
          "owner_name": "Test User",
          "size": "3000 sqm",
          "description": "Textile manufacturing facility",
          "location_address": "Alexandria, Egypt"
        }
      }
    ]
  }',
  'APPROVED',
  now() - interval '7 days',
  now() - interval '7 days',
  now() - interval '4 days'
),

-- RESUBMITTED Request
(
  'r1111111-1111-1111-1111-********1111',
  '3424f9f8-6985-4d5f-8602-e7c430fffa5f',
  'FARM',
  '{
    "registration_steps": [
      {
        "step_name": "farm_basic_info",
        "step_data": {
          "name": "Updated Valley Farm",
          "owner_name": "Test User",
          "size": "60 acres",
          "description": "Updated farm information with additional certifications",
          "location_address": "Updated Location, Egypt"
        }
      }
    ]
  }',
  'RESUBMITTED',
  now() - interval '1 hour',
  now() - interval '1 hour',
  now() - interval '1 hour'
)
ON CONFLICT (id) DO NOTHING;
```

#### 2.1 Fix Existing Registration Data (Run this to fix the "N/A" issue)

```sql
-- Update existing registration requests to have proper JSON structure
-- This will fix the asset_name and asset_location extraction

-- Update the existing requests to add proper structure
UPDATE public.registration_requests 
SET registration_data = jsonb_set(
  COALESCE(registration_data, '{}'),
  '{registration_steps}',
  CASE 
    WHEN entity_type = 'FARM' THEN
      '[{
        "step_name": "farm_basic_info",
        "step_data": {
          "name": "' || COALESCE(user_id::text, 'Test Farm') || ' Farm",
          "owner_name": "Farm Owner",
          "size": "50 acres",
          "description": "Farm description",
          "location_address": "Egypt"
        }
      }]'::jsonb
    ELSE
      '[{
        "step_name": "factory_basic_info", 
        "step_data": {
          "name": "' || COALESCE(user_id::text, 'Test Factory') || ' Factory",
          "owner_name": "Factory Owner",
          "size": "1000 sqm",
          "description": "Factory description",
          "location_address": "Egypt"
        }
      }]'::jsonb
  END
)
WHERE registration_data IS NULL 
   OR NOT registration_data ? 'registration_steps'
   OR jsonb_array_length(registration_data->'registration_steps') = 0;

-- Specifically update the requests you mentioned to have better names
UPDATE public.registration_requests 
SET registration_data = jsonb_set(
  registration_data,
  '{registration_steps,0,step_data,name}',
  CASE 
    WHEN entity_type = 'FARM' THEN '"Green Valley Farm"'
    ELSE '"Modern Processing Factory"'
  END::jsonb
)
WHERE id IN (
  'd66a9bee-fbf6-49e0-a2e6-d3566a0fca56',
  '9252f8ee-8614-4bdc-9844-4a42a6fd2c8d',
  'b887a3fd-1cdf-41b0-ba10-bb4dd1c6934a',
  '55d90327-ca3a-4a58-91da-b66928bc0cea',
  '75217d4a-7ff8-4ec5-a5ed-140d3a44683f',
  '72f85154-c942-45c7-bab5-37d86b2a0517',
  '06e1cc50-1b52-429a-a9d7-0b52c0718a46',
  'bd8f5475-8ae6-4619-9cb7-1abf6ecba95f',
  'cb59e118-3b77-4245-ad57-747db96107ac',
  'd6a9225c-c6ba-4d2b-ac41-a2858754b003',
  'a1d2cfdd-e219-48ff-92ff-03cdf1db2038',
  'c6c5fcb9-6680-4d75-b577-5193cdf29fc7'
);

-- Update location addresses for the existing requests
UPDATE public.registration_requests 
SET registration_data = jsonb_set(
  registration_data,
  '{registration_steps,0,step_data,location_address}',
  CASE 
    WHEN entity_type = 'FARM' THEN '"Rural Area, Egypt"'
    ELSE '"Industrial Zone, Egypt"'
  END::jsonb
)
WHERE id IN (
  'd66a9bee-fbf6-49e0-a2e6-d3566a0fca56',
  '9252f8ee-8614-4bdc-9844-4a42a6fd2c8d',
  'b887a3fd-1cdf-41b0-ba10-bb4dd1c6934a',
  '55d90327-ca3a-4a58-91da-b66928bc0cea',
  '75217d4a-7ff8-4ec5-a5ed-140d3a44683f',
  '72f85154-c942-45c7-bab5-37d86b2a0517',
  '06e1cc50-1b52-429a-a9d7-0b52c0718a46',
  'bd8f5475-8ae6-4619-9cb7-1abf6ecba95f',
  'cb59e118-3b77-4245-ad57-747db96107ac',
  'd6a9225c-c6ba-4d2b-ac41-a2858754b003',
  'a1d2cfdd-e219-48ff-92ff-03cdf1db2038',
  'c6c5fcb9-6680-4d75-b577-5193cdf29fc7'
);
```

#### 3. Create Sample Approval History

```sql
-- Add approval history for the rejected request
INSERT INTO public.registration_approval_history (
  id, request_id, action, performed_by, notes, previous_status, new_status, performed_at
) VALUES 
(
  'h1111111-1111-1111-1111-********1111',
  'c222**************-2222-************',
  'REJECTED',
  '********-5555-5555-5555-********5555',
  'Insufficient documentation provided. Missing environmental compliance certificates.',
  'PENDING',
  'REJECTED',
  now() - interval '4 days'
),
(
  'h222**************-2222-************',
  'f333**************-3333-************',
  'APPROVED',
  '********-5555-5555-5555-********5555',
  'All documentation verified. Farm meets organic certification standards.',
  'PENDING',
  'APPROVED',
  now() - interval '3 days'
)
ON CONFLICT (id) DO NOTHING;
```

#### 4. Create Sample Assets (from approved registrations)

```sql
-- Create assets for approved registrations
INSERT INTO public.assets (
  id, owner_id, name, location_address, asset_type, status, details, created_at, updated_at
) VALUES 
(
  'a1111111-1111-1111-1111-********1111',
  '********-1111-1111-1111-********1111',
  'Sunrise Organic Farm',
  'Kafr El Sheikh, Egypt',
  'farm',
  'approved',
  '{
    "registration_steps": [
      {
        "step_name": "farm_basic_info",
        "step_data": {
          "name": "Sunrise Organic Farm",
          "owner_name": "Ahmed Hassan",
          "size": "30 acres",
          "description": "Certified organic farm producing herbs and leafy greens"
        }
      }
    ]
  }',
  now() - interval '3 days',
  now() - interval '3 days'
)
ON CONFLICT (id) DO NOTHING;
```

### Troubleshooting Existing Data Issues

If you're seeing registration requests in the database but they're not displaying in the UI, run these diagnostic scripts:

#### Check Registration Data Structure

```sql
-- Check the structure of existing registration_data
SELECT 
  id,
  entity_type,
  status,
  registration_data,
  -- Check if registration_steps exists
  CASE 
    WHEN registration_data ? 'registration_steps' THEN 'HAS_STEPS'
    ELSE 'MISSING_STEPS'
  END as steps_status,
  -- Try to extract name using our function
  COALESCE(
    (registration_data->'registration_steps'->0->'step_data'->>'name'),
    (registration_data->'registration_steps'->1->'step_data'->>'name'),
    'EXTRACTION_FAILED'
  ) as extracted_name
FROM public.registration_requests 
WHERE id IN (
  'd66a9bee-fbf6-49e0-a2e6-d3566a0fca56',
  '9252f8ee-8614-4bdc-9844-4a42a6fd2c8d',
  'b887a3fd-1cdf-41b0-ba10-bb4dd1c6934a'
)
ORDER BY submitted_at DESC;
```

#### Test the Database Function Directly

```sql
-- Test if the function works with existing data
SELECT * FROM public.get_registration_requests(
  p_limit := 5,
  p_offset := 0,
  p_entity_type := NULL,
  p_status := 'PENDING',
  p_search_term := NULL
);
```

#### Check Function Permissions

```sql
-- Verify function exists and has correct permissions
SELECT 
  p.proname as function_name,
  p.proacl as permissions,
  n.nspname as schema_name
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE p.proname = 'get_registration_requests';
```

### Database Verification Scripts

Use these scripts to check the current state of your database:

#### 1. Check Registration Requests

```sql
-- View all registration requests with user information
SELECT 
  rr.id,
  rr.entity_type,
  rr.status,
  rr.submitted_at,
  p.first_name,
  p.last_name,
  p.email,
  -- Extract asset name from JSON
  COALESCE(
    (rr.registration_data->'registration_steps'->0->'step_data'->>'name'),
    (rr.registration_data->'registration_steps'->1->'step_data'->>'name'),
    'N/A'
  ) as asset_name,
  -- Extract location from JSON
  COALESCE(
    (rr.registration_data->'registration_steps'->0->'step_data'->>'location_address'),
    (rr.registration_data->'registration_steps'->1->'step_data'->>'location_address'),
    'N/A'
  ) as asset_location
FROM public.registration_requests rr
LEFT JOIN public.profiles p ON rr.user_id = p.id
ORDER BY rr.submitted_at DESC;
```

#### 2. Check Registration Requests by Status

```sql
-- Count requests by status
SELECT 
  status,
  COUNT(*) as count,
  entity_type
FROM public.registration_requests 
GROUP BY status, entity_type
ORDER BY status, entity_type;
```

#### 3. Check Assets Created from Approvals

```sql
-- View all assets with owner information
SELECT 
  a.id,
  a.name,
  a.location_address,
  a.asset_type,
  a.status,
  a.created_at,
  p.first_name,
  p.last_name,
  p.email
FROM public.assets a
LEFT JOIN public.profiles p ON a.owner_id = p.id
ORDER BY a.created_at DESC;
```

#### 4. Check Approval History

```sql
-- View approval history with admin information
SELECT 
  rah.id,
  rah.request_id,
  rah.action,
  rah.performed_at,
  rah.notes,
  rah.previous_status,
  rah.new_status,
  p.first_name as admin_first_name,
  p.last_name as admin_last_name,
  p.email as admin_email
FROM public.registration_approval_history rah
LEFT JOIN public.profiles p ON rah.performed_by = p.id
ORDER BY rah.performed_at DESC;
```

#### 5. Test Database Functions

```sql
-- Test the get_registration_requests function
SELECT * FROM public.get_registration_requests(
  p_limit := 10,
  p_offset := 0,
  p_entity_type := NULL,
  p_status := NULL,
  p_search_term := NULL
);

-- Test filtering by entity type
SELECT * FROM public.get_registration_requests(
  p_limit := 10,
  p_offset := 0,
  p_entity_type := 'FARM',
  p_status := NULL,
  p_search_term := NULL
);

-- Test filtering by status
SELECT * FROM public.get_registration_requests(
  p_limit := 10,
  p_offset := 0,
  p_entity_type := NULL,
  p_status := 'PENDING',
  p_search_term := NULL
);

-- Test search functionality
SELECT * FROM public.get_registration_requests(
  p_limit := 10,
  p_offset := 0,
  p_entity_type := NULL,
  p_status := NULL,
  p_search_term := 'Ahmed'
);
```

#### 6. Test Registration Details Function

```sql
-- Test get_registration_request_details function
SELECT public.get_registration_request_details('f1111111-1111-1111-1111-********1111');
```

#### 7. Check Admin Users

```sql
-- Verify admin users exist
SELECT 
  p.id,
  p.first_name,
  p.last_name,
  p.email,
  r.name as role_name,
  r.is_admin_role
FROM public.profiles p
JOIN public.user_roles ur ON p.id = ur.user_id
JOIN public.roles r ON ur.role_id = r.id
WHERE r.is_admin_role = true;
```

#### 8. Debug UI Issues

If the management page is not showing data, check these:

```sql
-- Check if RLS (Row Level Security) is blocking access
SELECT 
  schemaname,
  tablename,
  rowsecurity,
  policies
FROM pg_tables t
LEFT JOIN (
  SELECT 
    schemaname,
    tablename,
    array_agg(policyname) as policies
  FROM pg_policies 
  GROUP BY schemaname, tablename
) p ON t.schemaname = p.schemaname AND t.tablename = p.tablename
WHERE t.tablename = 'registration_requests';

-- Check current user context (run this while logged in as admin)
SELECT 
  auth.uid() as current_user_id,
  auth.jwt() as jwt_claims;

-- Test direct table access (should work for admin)
SELECT COUNT(*) as total_requests
FROM public.registration_requests;
```

### Cleanup Scripts (Use After Testing)

```sql
-- Clean up test data (BE CAREFUL - only run in test environment)
DELETE FROM public.registration_approval_history WHERE request_id IN (
  'f1111111-1111-1111-1111-********1111',
  'f222**************-2222-************',
  'f333**************-3333-************',
  'c1111111-1111-1111-1111-********1111',
  'c222**************-2222-************',
  'r1111111-1111-1111-1111-********1111'
);

DELETE FROM public.assets WHERE id = 'a1111111-1111-1111-1111-********1111';

DELETE FROM public.registration_requests WHERE id IN (
  'f1111111-1111-1111-1111-********1111',
  'f222**************-2222-************',
  'f333**************-3333-************',
  'c1111111-1111-1111-1111-********1111',
  'c222**************-2222-************',
  'r1111111-1111-1111-1111-********1111'
);

DELETE FROM public.user_roles WHERE user_id IN (
  '********-1111-1111-1111-********1111',
  '2222**************-2222-************',
  '3333**************-3333-************',
  '********-4444-4444-4444-********4444',
  '********-5555-5555-5555-********5555'
);

DELETE FROM public.profiles WHERE id IN (
  '********-1111-1111-1111-********1111',
  '2222**************-2222-************',
  '3333**************-3333-************',
  '********-4444-4444-4444-********4444',
  '********-5555-5555-5555-********5555'
);

-- Note: auth.users cleanup should be done through Supabase Auth admin panel
```

## Test Data Requirements

After running the setup scripts, you will have:

1. **Registration Requests** with different statuses:
   - 3 PENDING requests (2 farms, 1 factory)
   - 1 APPROVED request (1 farm)
   - 1 REJECTED request (1 factory)
   - 1 RESUBMITTED request (1 farm)

2. **Different Entity Types**:
   - Farm registrations with detailed information
   - Factory registrations with comprehensive data

3. **Varied Data**:
   - Requests with multiple images
   - Requests with complete location and contact info
   - Different user profiles and submission dates
   - Sample approval history

4. **Admin Users**:
   - Test admin user (<EMAIL> / password123)
   - Proper admin role assignment

## Success Criteria

The asset registration system passes testing if:

1. ✅ All registration requests display correctly
2. ✅ Filtering and search work as expected
3. ✅ Registration details modal shows complete information
4. ✅ Approval workflow creates assets and updates status
5. ✅ Rejection workflow properly logs reasons and updates status
6. ✅ Error handling is robust and user-friendly
7. ✅ Performance is acceptable with realistic data volumes
8. ✅ System is responsive and accessible on different devices

## Reporting Issues

When reporting issues, include:

1. **Steps to reproduce** the problem
2. **Expected vs actual behavior**
3. **Browser and device information**
4. **Console errors** (if any)
5. **Screenshots** of the issue
6. **Test data** used (without sensitive information)

## Post-Testing Cleanup

After testing:

1. Reset test data if needed
2. Verify no test artifacts remain in production
3. Document any configuration changes made
4. Update this guide based on findings