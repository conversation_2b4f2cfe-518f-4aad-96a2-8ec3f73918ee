# RovoDev - AI Development Assistant System

Welcome to the RovoDev system for the Agri-Industrial Project! This folder contains instructions, templates, and documentation to help you work effectively with AI coding assistants.

## 📁 Folder Structure

```
.rovodev/
├── README.md                 # This file - overview and getting started
├── instructions.yml          # Registry of all available instructions
├── generate-prp.md          # PRP generation instruction
├── templates/               # Reusable templates
│   └── prp-base.md         # Base template for PRPs
├── guides/                  # Usage guides and documentation
│   ├── getting-started.md   # How to get started with RovoDev
│   ├── prp-guide.md        # Complete guide to PRPs
│   └── best-practices.md   # Best practices for AI development
└── examples/               # Example files and use cases
    └── sample-prp.md       # Example PRP for reference
```

## 🚀 Quick Start

### 1. Using Instructions
Instructions are defined in `instructions.yml` and can be called by referencing their name:

```bash
# Generate a PRP for a feature defined in Initial.md
@generate-prp Initial.md

# Execute an existing PRP to implement the feature
@execute-prp .rovodev/PRPs/my-feature.md
```

### 2. Available Instructions
- **generate-prp**: Creates comprehensive Product Requirements Prompts for AI coding assistants
- **execute-prp**: Implements features using existing PRP files with comprehensive validation

### 3. Creating New Instructions
1. Add instruction content to a markdown file in this folder
2. Register it in `instructions.yml` with name, description, and content file
3. Follow the established patterns for consistency

## 🎯 What is RovoDev?

RovoDev is a system designed to:
- **Standardize AI interactions** for development tasks
- **Provide comprehensive context** to AI coding assistants
- **Enable one-pass implementations** through detailed requirements
- **Maintain consistency** across development workflows
- **Accelerate development** by reducing back-and-forth iterations

## 📋 Core Concepts

### Product Requirements Prompts (PRPs)
PRPs are enhanced versions of Product Requirements Documents (PRDs) specifically designed for AI coding assistants. They include:
- Detailed implementation context
- Code examples and patterns
- Validation criteria
- Error handling strategies
- Complete technical specifications

### Instructions
Reusable commands that automate common development tasks like:
- Generating PRPs
- Setting up development environments
- Creating boilerplate code
- Running tests and validations

## 🛠 For This Project

This Agri-Industrial Project uses:
- **Next.js** with TypeScript
- **Supabase** for backend services
- **Tailwind CSS** for styling
- **React Native** for mobile app
- **Multi-language support** (Arabic/English)

When creating PRPs or instructions, consider:
- Existing authentication patterns
- Database schema and relationships
- UI component library usage
- Internationalization requirements
- Mobile-first design principles

## 📚 Learn More

- Read `guides/getting-started.md` for detailed setup instructions
- Check `guides/prp-guide.md` for comprehensive PRP creation guide
- Review `examples/sample-prp.md` for a complete example
- Follow `guides/best-practices.md` for optimal AI development workflows

## 🤝 Contributing

When adding new instructions or templates:
1. Follow the established naming conventions
2. Include comprehensive documentation
3. Test with actual AI assistants
4. Update this README if needed

---

**Happy coding with AI! 🤖✨**