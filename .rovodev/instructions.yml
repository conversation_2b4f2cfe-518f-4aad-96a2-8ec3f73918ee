instructions:
  - name: generate-prp
    description: generation of product requirements prompts, similar to PRDs but they are specifically designed to instruct AI coding assistants.
    content_file: instructions/generate-prp.md
    usage: "generate-prp {feature-file.md}"
    output: ".rovodev/PRPs/{feature-name}.md"
    tags: ["prp", "documentation", "ai-assistant", "feature-development"]
  
  - name: execute-prp
    description: command to execute prp
    content_file: instructions/execute-prp.md
    usage: "execute-prp {prp-file.md}"
    output: "implemented feature code"
    tags: ["prp", "implementation", "ai-assistant", "execution"]