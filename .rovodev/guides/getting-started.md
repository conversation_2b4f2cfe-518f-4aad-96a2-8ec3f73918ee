# Getting Started with RovoDev

This guide will help you understand and use the RovoDev system effectively for AI-assisted development.

## 🎯 What You'll Learn

- How to use existing instructions
- How to create new instructions
- Best practices for working with AI assistants
- Project-specific patterns and conventions

## 📋 Prerequisites

- Basic understanding of the Agri-Industrial Project structure
- Familiarity with Next.js, React, and TypeScript
- Access to an AI coding assistant (<PERSON>, <PERSON><PERSON>, etc.)

## 🚀 Using Instructions

### Step 1: Check Available Instructions

Look at `.rovodev/instructions.yml` to see all available instructions:

```yaml
instructions:
  - name: generate-prp
    description: generation of product requirements prompts, similar to PRDs but they are specifically designed to instruct AI coding assistants.
    content_file: generate-prp.md
```

### Step 2: Prepare Your Feature File

Create a markdown file describing the feature you want to implement. For example, `Initial.md`:

```markdown
# New Feature: Real-time Chat System

## Overview
Implement a real-time chat system for farmer-expert consultations.

## Requirements
- Real-time messaging using Supabase Realtime
- File sharing capabilities
- Message history and search
- Mobile and web support
- Arabic/English support

## Acceptance Criteria
- [ ] Users can send and receive messages instantly
- [ ] Files can be shared and downloaded
- [ ] Message history is preserved
- [ ] Works on both mobile and web platforms
```

### Step 3: Run the Instruction

Use the instruction with your AI assistant:

```
@generate-prp Initial.md
```

or

```
Please use the generate-prp instruction with the file Initial.md
```

### Step 4: Review the Generated PRP

The instruction will create a comprehensive PRP in the `PRPs/` folder with:
- Detailed technical specifications
- Code examples from your codebase
- Implementation steps
- Validation criteria
- Quality checklist

## 🔧 Creating New Instructions

### Step 1: Define the Instruction Content

Create a new markdown file in `.rovodev/` with your instruction content:

```markdown
# My New Instruction

## Purpose
Brief description of what this instruction does.

## Usage
How to use this instruction.

## Steps
1. Step one
2. Step two
3. Step three

## Output
What the instruction produces.
```

### Step 2: Register the Instruction

Add it to `.rovodev/instructions.yml`:

```yaml
instructions:
  - name: generate-prp
    description: generation of product requirements prompts
    content_file: generate-prp.md
  - name: my-new-instruction
    description: description of what it does
    content_file: my-new-instruction.md
```

### Step 3: Test the Instruction

Try using it with your AI assistant to ensure it works as expected.

## 🏗 Project-Specific Guidelines

### For the Agri-Industrial Project

When creating PRPs or instructions, consider:

#### Authentication Patterns
- Use Supabase Auth patterns from `src/utils/auth-helpers.ts`
- Follow role-based access control (farmers, experts, admins)
- Reference existing auth components

#### Database Patterns
- Follow the schema in `.database/schema.sql`
- Use Row Level Security (RLS) policies
- Reference existing database functions

#### UI Patterns
- Use components from `src/components/ui/`
- Follow the design system in `style-guide.html`
- Support RTL/LTR layouts for Arabic/English

#### Mobile Considerations
- Ensure features work on React Native mobile app
- Consider offline capabilities
- Follow mobile-first design principles

## 🎯 Success Metrics

A good instruction should:
- ✅ Produce consistent, high-quality results
- ✅ Reduce back-and-forth with AI assistants
- ✅ Include all necessary context
- ✅ Follow project conventions
- ✅ Enable one-pass implementations

## 🆘 Troubleshooting

### Common Issues

**Instruction not working?**
- Check the YAML syntax in `instructions.yml`
- Ensure the content file exists
- Verify the instruction format

**PRP missing context?**
- Add more codebase examples
- Include relevant documentation URLs
- Specify validation criteria clearly

**AI assistant confused?**
- Break down complex instructions into steps
- Provide more specific examples
- Include error handling guidance

## 📚 Next Steps

1. Read `prp-guide.md` for detailed PRP creation
2. Check `best-practices.md` for optimization tips
3. Review `../examples/sample-prp.md` for a complete example
4. Start creating your first PRP!

---

**Ready to accelerate your development with AI? Let's go! 🚀**