# RovoDev Examples

This directory contains example feature descriptions and generated PRPs to help you understand how to use RovoDev effectively.

## 📁 Contents

### Feature Examples
- **[expert-search-feature.md](expert-search-feature.md)** - Comprehensive example of a well-structured feature description for expert search and filtering functionality

### Generated PRP Examples
*Coming soon - examples of PRPs generated from the feature descriptions*

## 🎯 How to Use These Examples

### 1. Study the Structure
Each example follows the recommended structure:
- Clear description and business value
- Detailed user stories
- Comprehensive requirements (functional and non-functional)
- Specific acceptance criteria
- Technical considerations
- Dependencies and constraints

### 2. Adapt to Your Needs
Use these examples as templates:
- Copy the structure for your own features
- Modify the content to match your specific requirements
- Add or remove sections based on complexity

### 3. Compare Quality Levels
Notice the difference between:
- ❌ Vague requirements vs ✅ Specific, testable criteria
- ❌ Generic descriptions vs ✅ Project-specific context
- ❌ Missing details vs ✅ Comprehensive coverage

## 📝 Writing Your Own Features

### Quick Checklist
When writing feature descriptions, ensure you have:
- [ ] Clear business value statement
- [ ] Specific user stories with roles and benefits
- [ ] Detailed functional requirements
- [ ] Non-functional requirements (performance, accessibility, etc.)
- [ ] Testable acceptance criteria
- [ ] Technical considerations and constraints
- [ ] Dependencies on existing systems
- [ ] Success metrics

### Common Patterns in This Project
- **Authentication**: All features should consider user roles (farmer, expert, admin)
- **Internationalization**: Support for Arabic (RTL) and English (LTR)
- **Mobile-First**: Responsive design for mobile and desktop
- **Supabase Integration**: Database, auth, and real-time features
- **Performance**: Fast loading and responsive interactions

## 🚀 Next Steps

1. **Read the examples** to understand the expected quality level
2. **Try writing a feature** using the same structure
3. **Generate a PRP** using the `generate-prp` instruction
4. **Compare results** with the examples to improve your approach

## 💡 Tips for Success

### Be Specific
- Instead of "users can search", write "farmers can search experts by crop type, location, and rating"
- Include exact UI elements, data fields, and user interactions

### Think Like a Developer
- Consider database implications
- Think about API design
- Plan for error handling and edge cases
- Consider performance and scalability

### Include Context
- Reference existing similar features in the codebase
- Mention specific technologies and constraints
- Consider integration points with existing systems

### Plan for Quality
- Include validation steps
- Define success metrics
- Plan for testing scenarios
- Consider accessibility and internationalization

Remember: The goal is to provide enough context for an AI assistant to implement the feature successfully in one pass!