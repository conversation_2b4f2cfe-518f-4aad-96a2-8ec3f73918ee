# Complete Guide to Product Requirements Prompts (PRPs)

PRPs are enhanced Product Requirements Documents specifically designed for AI coding assistants. This guide covers everything you need to know about creating effective PRPs.

## 🎯 What Makes a Great PRP?

A great PRP enables an AI assistant to implement a feature in **one pass** without additional clarification. It should be:

- **Comprehensive**: All necessary context included
- **Specific**: Clear, actionable requirements
- **Contextual**: References existing codebase patterns
- **Executable**: Includes validation steps
- **Complete**: From planning to testing

## 📋 PRP Structure

### 1. Feature Overview
```markdown
# Feature Name: Real-time Chat System

## Executive Summary
Brief description of what needs to be built and why.

## Business Context
- User story and use case
- Business value and impact
- Success metrics
```

### 2. Technical Requirements
```markdown
## Technical Specifications

### Architecture
- System design overview
- Component interactions
- Data flow diagrams

### Technology Stack
- Frontend: Next.js, TypeScript, Tailwind
- Backend: Supabase Realtime
- Database: PostgreSQL with RLS
- Mobile: React Native integration
```

### 3. Implementation Context
```markdown
## Codebase Context

### Existing Patterns
Reference similar implementations:
- Authentication: `src/utils/auth-helpers.ts`
- Database: `src/supabase/client.ts`
- Components: `src/components/ui/`

### Code Examples
```typescript
// Example of existing pattern to follow
const { data, error } = await supabase
  .from('consultations')
  .select('*')
  .eq('farmer_id', userId);
```

### Integration Points
- How it connects to existing features
- Dependencies and requirements
- API endpoints to use/create
```

### 4. Detailed Requirements
```markdown
## Functional Requirements

### Core Features
1. **Real-time Messaging**
   - Send/receive messages instantly
   - Message status indicators
   - Typing indicators

2. **File Sharing**
   - Upload images, documents
   - File size limits and validation
   - Secure file storage

### User Interface
- Mobile-responsive design
- Arabic/English support (RTL/LTR)
- Accessibility compliance
- Dark/light theme support

### Data Model
```sql
-- Chat messages table
CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  consultation_id UUID REFERENCES consultations(id),
  sender_id UUID REFERENCES profiles(id),
  message_text TEXT,
  file_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔧 Implementation Blueprint

### Phase 1: Database Setup
```markdown
## Step 1: Database Schema
1. Create chat_messages table
2. Set up RLS policies
3. Create database functions
4. Add indexes for performance

## Step 2: Real-time Subscriptions
1. Configure Supabase Realtime
2. Set up message subscriptions
3. Handle connection states
```

### Phase 2: Backend Implementation
```markdown
## Step 3: API Endpoints
1. Send message endpoint
2. File upload endpoint
3. Message history endpoint
4. Message status updates

## Step 4: Real-time Logic
1. Message broadcasting
2. Typing indicators
3. Online status tracking
```

### Phase 3: Frontend Implementation
```markdown
## Step 5: UI Components
1. ChatWindow component
2. MessageBubble component
3. FileUpload component
4. TypingIndicator component

## Step 6: State Management
1. Message state handling
2. Real-time updates
3. Optimistic updates
4. Error handling
```

## 🧪 Validation Gates

### Code Quality
```bash
# Linting and type checking
npm run lint
npm run type-check

# Build verification
npm run build
```

### Testing
```bash
# Unit tests
npm run test

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e
```

### Manual Testing
- [ ] Send/receive messages work
- [ ] File upload/download works
- [ ] Real-time updates function
- [ ] Mobile responsiveness
- [ ] Arabic/English support
- [ ] Error handling works

## 📚 External Resources

### Documentation
- [Supabase Realtime Docs](https://supabase.com/docs/guides/realtime)
- [Next.js API Routes](https://nextjs.org/docs/api-routes/introduction)
- [React Native WebSocket](https://reactnative.dev/docs/network)

### Examples
- [Supabase Chat Example](https://github.com/supabase/supabase/tree/master/examples/nextjs-chat)
- [Real-time Chat Tutorial](https://supabase.com/blog/nextjs-supabase-chat-app)

### Best Practices
- [Real-time App Patterns](https://supabase.com/docs/guides/realtime/concepts)
- [Database Design for Chat](https://supabase.com/blog/database-design-for-chat-apps)

## ⚠️ Common Gotchas

### Supabase Realtime
- Enable Realtime on tables in Supabase dashboard
- Handle connection drops gracefully
- Manage subscription cleanup

### Performance
- Implement message pagination
- Optimize database queries
- Handle large file uploads

### Security
- Validate all inputs
- Implement proper RLS policies
- Secure file upload endpoints

## 🎯 Success Criteria

### Technical Success
- [ ] All tests pass
- [ ] No TypeScript errors
- [ ] Performance benchmarks met
- [ ] Security review passed

### User Experience
- [ ] Intuitive interface
- [ ] Fast message delivery (<100ms)
- [ ] Reliable file sharing
- [ ] Smooth mobile experience

### Business Success
- [ ] Increases user engagement
- [ ] Reduces support tickets
- [ ] Improves consultation quality

## 📊 Quality Score: 9/10

**Confidence Level**: High - This PRP provides comprehensive context, clear implementation steps, and thorough validation criteria for successful one-pass implementation.

**Areas for Improvement**:
- Could include more specific error handling scenarios
- Additional performance optimization details

---

This PRP structure ensures AI assistants have all the context needed for successful implementation while maintaining consistency with existing codebase patterns.