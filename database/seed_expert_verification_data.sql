-- Expert Verification Test Data
-- This script creates sample data for testing the expert verification system
-- Run this after the expert verification migration has been applied

-- First, ensure we have some test users in the profiles table
-- Note: These UUIDs should correspond to actual auth.users entries

-- Insert test profiles if they don't exist
INSERT INTO public.profiles (
    id,
    email,
    first_name,
    last_name,
    role,
    account_activated,
    created_at,
    updated_at
) VALUES
    (
        '********-1111-1111-1111-********1111',
        '<EMAIL>',
        '<PERSON>',
        '<PERSON><PERSON><PERSON>',
        'expert',
        false,
        NOW() - INTERVAL '30 days',
        NOW() - INTERVAL '30 days'
    ),
    (
        '*************-2222-2222-************',
        '<EMAIL>',
        '<PERSON><PERSON>',
        '<PERSON>-<PERSON><PERSON><PERSON>',
        'expert',
        false,
        NOW() - INTERVAL '25 days',
        NOW() - INTERVAL '25 days'
    ),
    (
        '*************-3333-3333-************',
        '<EMAIL>',
        '<PERSON>',
        '<PERSON>-<PERSON><PERSON>',
        'expert',
        true,
        NOW() - INTERVAL '60 days',
        NOW() - INTER<PERSON>L '10 days'
    ),
    (
        '********-4444-4444-4444-************',
        '<EMAIL>',
        'Aisha',
        'Al-Andalusi',
        'expert',
        false,
        NOW() - INTERVAL '15 days',
        NOW() - INTERVAL '15 days'
    ),
    (
        '55555555-5555-5555-5555-555555555555',
        '<EMAIL>',
        'Mohammed',
        'Al-Farmer',
        'user',
        true,
        NOW() - INTERVAL '45 days',
        NOW() - INTERVAL '45 days'
    ),
    (
        '66666666-6666-6666-6666-666666666666',
        '<EMAIL>',
        'Layla',
        'Al-Factory',
        'user',
        true,
        NOW() - INTERVAL '40 days',
        NOW() - INTERVAL '40 days'
    )
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    role = EXCLUDED.role,
    updated_at = NOW();

-- Insert expert profiles with different verification statuses
INSERT INTO public.expert_profiles (
    id,
    verification_status,
    bio,
    expertise_area,
    years_of_experience,
    education,
    qualifications,
    created_at,
    updated_at
) VALUES
    (
        '********-1111-1111-1111-********1111',
        'pending',
        'Agricultural engineer with expertise in sustainable farming practices and crop optimization.',
        'Sustainable Agriculture',
        5,
        'Bachelor of Agricultural Engineering - King Saud University (2018), Master of Sustainable Agriculture - KFUPM (2020)',
        'Certified Organic Farming Specialist, Irrigation Systems Expert',
        NOW() - INTERVAL '30 days',
        NOW() - INTERVAL '30 days'
    ),
    (
        '*************-2222-2222-************',
        'under_review',
        'Industrial automation specialist with focus on manufacturing process optimization.',
        'Industrial Automation',
        8,
        'Bachelor of Mechanical Engineering - KAUST (2015), Master of Industrial Engineering - KFUPM (2017)',
        'PLC Programming Certified, Six Sigma Black Belt, Project Management Professional',
        NOW() - INTERVAL '25 days',
        NOW() - INTERVAL '20 days'
    ),
    (
        '*************-3333-3333-************',
        'approved',
        'Senior agricultural consultant with extensive experience in precision farming and agricultural technology.',
        'Precision Agriculture',
        12,
        'Bachelor of Agricultural Sciences - King Abdulaziz University (2011), PhD in Agricultural Technology - KAUST (2015)',
        'Precision Agriculture Certified, Drone Technology Specialist, Agricultural Data Analytics Expert',
        NOW() - INTERVAL '60 days',
        NOW() - INTERVAL '10 days'
    ),
    (
        '********-4444-4444-4444-************',
        'resubmission_required',
        'Food processing engineer specializing in quality control and safety standards.',
        'Food Processing',
        6,
        'Bachelor of Food Engineering - King Faisal University (2017), Master of Food Safety - KFUPM (2019)',
        'HACCP Certified, ISO 22000 Lead Auditor',
        NOW() - INTERVAL '15 days',
        NOW() - INTERVAL '10 days'
    )
ON CONFLICT (id) DO UPDATE SET
    verification_status = EXCLUDED.verification_status,
    bio = EXCLUDED.bio,
    expertise_area = EXCLUDED.expertise_area,
    years_of_experience = EXCLUDED.years_of_experience,
    education = EXCLUDED.education,
    qualifications = EXCLUDED.qualifications,
    updated_at = NOW();

-- Insert some sample expert documents
INSERT INTO public.expert_documents (
    expert_id,
    document_type,
    document_name,
    document_url,
    file_size,
    file_type,
    verification_status,
    upload_date
) VALUES 
    (
        '********-1111-1111-1111-********1111',
        'qualification',
        'Agricultural Engineering Degree.pdf',
        '/documents/expert1/degree.pdf',
        2048576,
        'application/pdf',
        'pending',
        NOW() - INTERVAL '30 days'
    ),
    (
        '********-1111-1111-1111-********1111',
        'certification',
        'Organic Farming Certificate.pdf',
        '/documents/expert1/organic_cert.pdf',
        1024768,
        'application/pdf',
        'pending',
        NOW() - INTERVAL '29 days'
    ),
    (
        '*************-2222-2222-************',
        'qualification',
        'Industrial Engineering Masters.pdf',
        '/documents/expert2/masters.pdf',
        3145728,
        'application/pdf',
        'under_review',
        NOW() - INTERVAL '25 days'
    ),
    (
        '*************-3333-3333-************',
        'qualification',
        'PhD Agricultural Technology.pdf',
        '/documents/expert3/phd.pdf',
        4194304,
        'application/pdf',
        'approved',
        NOW() - INTERVAL '60 days'
    ),
    (
        '*************-3333-3333-************',
        'certification',
        'Precision Agriculture Certificate.pdf',
        '/documents/expert3/precision_cert.pdf',
        1572864,
        'application/pdf',
        'approved',
        NOW() - INTERVAL '59 days'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert verification history for some experts
INSERT INTO public.expert_verification_history (
    expert_id,
    previous_status,
    new_status,
    changed_by,
    admin_notes,
    created_at
) VALUES 
    (
        '*************-2222-2222-************',
        'pending',
        'under_review',
        NULL, -- Admin user ID would go here
        'Documents received and under review by verification team.',
        NOW() - INTERVAL '20 days'
    ),
    (
        '*************-3333-3333-************',
        'pending',
        'under_review',
        NULL,
        'Initial review started.',
        NOW() - INTERVAL '50 days'
    ),
    (
        '*************-3333-3333-************',
        'under_review',
        'approved',
        NULL,
        'All documents verified. Expert approved for consultation services.',
        NOW() - INTERVAL '10 days'
    ),
    (
        '********-4444-4444-4444-************',
        'pending',
        'resubmission_required',
        NULL,
        'Additional certification documents required for food safety compliance.',
        NOW() - INTERVAL '10 days'
    )
ON CONFLICT (id) DO NOTHING;

-- Note: Client profiles and assets tables don't exist in current schema
-- These would be created when the full registration system is implemented

-- Print summary of created data
DO $$
BEGIN
    RAISE NOTICE 'Expert verification test data created successfully!';
    RAISE NOTICE 'Created profiles: %', (SELECT COUNT(*) FROM public.profiles WHERE role = 'expert');
    RAISE NOTICE 'Created expert profiles: %', (SELECT COUNT(*) FROM public.expert_profiles);
    RAISE NOTICE 'Created expert documents: %', (SELECT COUNT(*) FROM public.expert_documents);
    RAISE NOTICE 'Created verification history entries: %', (SELECT COUNT(*) FROM public.expert_verification_history);
END $$;
