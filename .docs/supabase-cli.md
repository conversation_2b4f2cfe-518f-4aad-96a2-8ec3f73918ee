========================
CODE SNIPPETS
========================
TITLE: Example Output of db-replication-slots Command
DESCRIPTION: An example showing the tabular output of the `db-replication-slots` command, detailing replication slot names, active status, WAL sender state, replication client address, and the current replication lag in gigabytes.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-replication-slots.md#_snippet_0

LANGUAGE: Console Output
CODE:
```
                       NAME                    │ ACTIVE │ STATE   │ REPLICATION CLIENT ADDRESS │ REPLICATION LAG GB
  ─────────────────────────────────────────────┼────────┼─────────┼────────────────────────────┼─────────────────────
    supabase_realtime_replication_slot         │ t      │ N/A     │ N/A                        │                  0
    datastream                                 │ t      │ catchup │ *************              │                 45
```

----------------------------------------

TITLE: Install Supabase CLI on Windows via Scoop
DESCRIPTION: Instructions for installing and upgrading the Supabase CLI on Windows using the Scoop package manager. This involves adding the Supabase bucket and then installing the CLI.

SOURCE: https://github.com/supabase/cli/blob/develop/README.md#_snippet_2

LANGUAGE: powershell
CODE:
```
scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
scoop install supabase
```

LANGUAGE: powershell
CODE:
```
scoop update supabase
```

----------------------------------------

TITLE: Example Output: db-cache-hit Command
DESCRIPTION: An example of the output from the `db-cache-hit` command, showing the index and table hit rates. The ratios indicate the efficiency of reading data from the buffer cache versus disk.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-cache-hit.md#_snippet_0

LANGUAGE: Shell
CODE:
```
         NAME      │  RATIO
  ─────────────────┼───────────
    index hit rate │ 0.996621
    table hit rate │ 0.999341
```

----------------------------------------

TITLE: Start Supabase Local Development Stack
DESCRIPTION: Commands to initialize and start the Supabase local development stack. The second command excludes specific services (GoTrue, Storage API, Imgproxy) from the startup process.

SOURCE: https://github.com/supabase/cli/blob/develop/internal/utils/templates/initial_schemas/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
go run . init
go run . start -x gotrue,storage-api,imgproxy
```

----------------------------------------

TITLE: Install Supabase CLI via Go Modules
DESCRIPTION: Instructions for installing the Supabase CLI using Go modules and creating a symlink in your PATH for easier command-line access. This method works on various non-standard Linux distributions.

SOURCE: https://github.com/supabase/cli/blob/develop/README.md#_snippet_5

LANGUAGE: sh
CODE:
```
go install github.com/supabase/cli@latest
```

LANGUAGE: sh
CODE:
```
ln -s "$(go env GOPATH)/bin/cli" /usr/bin/supabase
```

----------------------------------------

TITLE: CLI Output: Table Index Sizes Example
DESCRIPTION: An example of the output generated by the `db-table-index-sizes` command, showing the index size for various tables in a database. Sizes are displayed in kilobytes (kB) or bytes.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-table-index-sizes.md#_snippet_0

LANGUAGE: CLI
CODE:
```
                 TABLE               │ INDEX SIZE
  ───────────────────────────────────┼─────────────
    job_run_details                  │ 10104 kB
    users                            │ 128 kB
    job                              │ 32 kB
    instances                        │ 8192 bytes
    http_request_queue               │ 0 bytes
```

----------------------------------------

TITLE: Deploy Supabase Functions using CLI
DESCRIPTION: Demonstrates how to deploy Supabase functions using the Supabase CLI library. Functions should be placed under the `supabase/functions` directory. This example requires `SUPABASE_PROJECT_ID` and `SUPABASE_ACCESS_TOKEN` environment variables to be set before execution.

SOURCE: https://github.com/supabase/cli/blob/develop/examples/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
# Place your functions under supabase/functions
export SUPABASE_PROJECT_ID="zeoxvqpvpyrxygmmatng"
export SUPABASE_ACCESS_TOKEN="sbp_..."
go run examples/deploy-functions/main.go
```

----------------------------------------

TITLE: Install Supabase CLI on Linux via Homebrew
DESCRIPTION: Steps to install and upgrade the Supabase CLI on Linux using Homebrew, similar to the macOS installation process.

SOURCE: https://github.com/supabase/cli/blob/develop/README.md#_snippet_3

LANGUAGE: sh
CODE:
```
brew install supabase/tap/supabase
```

LANGUAGE: sh
CODE:
```
brew upgrade supabase
```

----------------------------------------

TITLE: Install Supabase CLI via pkgx
DESCRIPTION: Command to install the Supabase CLI in your current working directory using the pkgx package manager. This is a community-maintained package.

SOURCE: https://github.com/supabase/cli/blob/develop/README.md#_snippet_6

LANGUAGE: bash
CODE:
```
pkgx install supabase
```

----------------------------------------

TITLE: Seed Supabase Storage Buckets using CLI
DESCRIPTION: Shows how to seed Supabase storage buckets using the Supabase CLI library. This example requires `SUPABASE_PROJECT_ID` and `SUPABASE_SERVICE_ROLE_KEY` environment variables to be set for authentication and project identification.

SOURCE: https://github.com/supabase/cli/blob/develop/examples/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
export SUPABASE_PROJECT_ID="zeoxvqpvpyrxygmmatng"
export SUPABASE_SERVICE_ROLE_KEY="eyJh..."
go run examples/migrate-database/main.go
```

----------------------------------------

TITLE: Run Supabase CLI Bootstrap Command
DESCRIPTION: How to initialize a Supabase project using the `bootstrap` command, either directly if the CLI is in your PATH, or via npx. This command guides you through setting up a project with a starter template.

SOURCE: https://github.com/supabase/cli/blob/develop/README.md#_snippet_7

LANGUAGE: bash
CODE:
```
supabase bootstrap
```

LANGUAGE: bash
CODE:
```
npx supabase bootstrap
```

----------------------------------------

TITLE: Example Output: Total Database Table Sizes
DESCRIPTION: This snippet shows a typical output from the `db-total-table-sizes` command, listing various tables and their aggregated sizes in a human-readable format.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-total-table-sizes.md#_snippet_0

LANGUAGE: cli
CODE:
```
                NAME               │    SIZE
───────────────────────────────────┼─────────────
  job_run_details                  │ 395 MB
  slack_msgs                       │ 648 kB
  emails                           │ 640 kB
```

----------------------------------------

TITLE: Install Supabase CLI via NPM/Yarn
DESCRIPTION: Instructions for installing the Supabase CLI as a development dependency using npm or yarn, including the beta channel and a workaround for yarn 4's experimental fetch. For Bun versions below v1.0.17, ensure 'supabase' is added as a trusted dependency.

SOURCE: https://github.com/supabase/cli/blob/develop/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm i supabase --save-dev
```

LANGUAGE: bash
CODE:
```
npm i supabase@beta --save-dev
```

LANGUAGE: bash
CODE:
```
NODE_OPTIONS=--no-experimental-fetch yarn add supabase
```

----------------------------------------

TITLE: Example Output of `db-blocking` Command
DESCRIPTION: An example showing the structured output of the `db-blocking` command, detailing blocked and blocking process IDs (PIDs), their respective SQL statements, and the duration for which they have been blocking or blocked.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-blocking.md#_snippet_0

LANGUAGE: CLI Output
CODE:
```
    BLOCKED PID │ BLOCKING STATEMENT           │ BLOCKING DURATION │ BLOCKING PID │ BLOCKED STATEMENT                                                                      │ BLOCKED DURATION
  ──────────────┼──────────────────────────────┼───────────────────┼──────────────┼────────────────────────────────────────────────────────────────────────────────────────┼───────────────────
    253         │ select count(*) from mytable │ 00:00:03.838314   │        13495 │ UPDATE "mytable" SET "updated_at" = '2023─08─03 14:07:04.746688' WHERE "id" = 83719341 │ 00:00:03.821826
```

----------------------------------------

TITLE: Install Supabase CLI on macOS via Homebrew
DESCRIPTION: Steps to install and upgrade the Supabase CLI on macOS using Homebrew, including the beta release channel. The beta version requires an additional link command.

SOURCE: https://github.com/supabase/cli/blob/develop/README.md#_snippet_1

LANGUAGE: sh
CODE:
```
brew install supabase/tap/supabase
```

LANGUAGE: sh
CODE:
```
brew install supabase/tap/supabase-beta
brew link --overwrite supabase-beta
```

LANGUAGE: sh
CODE:
```
brew upgrade supabase
```

----------------------------------------

TITLE: Install Supabase CLI on Linux via Package Managers
DESCRIPTION: Commands to install the Supabase CLI on various Linux distributions using their respective package managers (apk, dpkg, rpm, pacman) from downloaded release files. Replace '<...>' with the actual file path.

SOURCE: https://github.com/supabase/cli/blob/develop/README.md#_snippet_4

LANGUAGE: sh
CODE:
```
sudo apk add --allow-untrusted <...>.apk
```

LANGUAGE: sh
CODE:
```
sudo dpkg -i <...>.deb
```

LANGUAGE: sh
CODE:
```
sudo rpm -i <...>.rpm
```

LANGUAGE: sh
CODE:
```
sudo pacman -U <...>.pkg.tar.zst
```

----------------------------------------

TITLE: Migrate Supabase Database using CLI
DESCRIPTION: Illustrates how to perform database migrations using the Supabase CLI library. Database schemas should be placed under the `supabase/migrations` directory. This example requires PostgreSQL connection details (`PGHOST`, `PGPORT`, `PGUSER`, `PGPASS`, `PGDATABASE`) to be set as environment variables.

SOURCE: https://github.com/supabase/cli/blob/develop/examples/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
# Place your schemas under supabase/migrations
export PGHOST="db.zeoxvqpvpyrxygmmatng.supabase.co"
export PGPORT="5432"
export PGUSER="postgres"
export PGPASS="<your-password>"
export PGDATABASE="postgres"
go run examples/migrate-database/main.go
```

----------------------------------------

TITLE: Example Output of db-index-sizes Command
DESCRIPTION: This table illustrates the typical output of the `db-index-sizes` command, showing a list of database indexes along with their respective sizes, presented in various units such as MB, kB, and bytes.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-index-sizes.md#_snippet_0

LANGUAGE: CLI Output
CODE:
```
              NAME              │    SIZE
  ──────────────────────────────┼─────────────
    user_events_index           │ 2082 MB
    job_run_details_pkey        │ 3856 kB
    schema_migrations_pkey      │ 16 kB
    refresh_tokens_token_unique │ 8192 bytes
    users_instance_id_idx       │ 0 bytes
    buckets_pkey                │ 0 bytes
```

----------------------------------------

TITLE: Example Output: Estimated Row Counts for Database Tables
DESCRIPTION: This snippet shows a typical output from the `db-table-record-counts` command. It lists database tables along with their estimated row counts, ordered in descending order by count. This example illustrates how the command presents the `n_live_tup` derived estimations.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-table-record-counts.md#_snippet_0

LANGUAGE: CLI Output
CODE:
```
       NAME    │ ESTIMATED COUNT
  ─────────────┼──────────────────
    logs       │          322943
    emails     │            1103
    job        │               1
    migrations │               0
```

----------------------------------------

TITLE: Example Output of Supabase CLI db-table-sizes Command
DESCRIPTION: This snippet illustrates the typical console output generated by the `supabase db-table-sizes` command. It presents a formatted table showing various database tables and their corresponding sizes, providing a clear overview of storage consumption per table.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-table-sizes.md#_snippet_0

LANGUAGE: Console
CODE:
```
                  NAME               │    SIZE
  ───────────────────────────────────┼─────────────
    job_run_details                  │ 385 MB
    emails                           │ 584 kB
    job                              │ 40 kB
    sessions                         │ 0 bytes
    prod_resource_notifications_meta │ 0 bytes
```

----------------------------------------

TITLE: Supabase CLI `db-calls` Output Example
DESCRIPTION: Example output from the `supabase inspect db-calls` command, showing a table of database queries, their total execution time, proportion of total execution time, number of calls, and sync I/O time. This output helps identify the most frequently called queries for potential optimization.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-calls.md#_snippet_0

LANGUAGE: Text
CODE:
```
                        QUERY                      │ TOTAL EXECUTION TIME │ PROPORTION OF TOTAL EXEC TIME │ NUMBER CALLS │  SYNC IO TIME
  ─────────────────────────────────────────────────┼──────────────────────┼───────────────────────────────┼──────────────┼──────────────────
    SELECT * FROM users WHERE id = $1              │ 14:50:11.828939      │ 89.8%                         │  183,389,757 │ 00:00:00.002018
    SELECT * FROM user_events                      │ 01:20:23.466633      │ 1.4%                          │       78,325 │ 00:00:00
    INSERT INTO users (email, name) VALUES ($1, $2)│ 00:40:11.616882      │ 0.8%                          │       54,003 │ 00:00:00.000322
```

----------------------------------------

TITLE: View Supabase Migration List (Out-of-Sync Example)
DESCRIPTION: This snippet demonstrates an example of a Supabase migration list where local and remote histories are out of sync, showing missing entries in either the local or remote record.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/migration/repair.md#_snippet_0

LANGUAGE: bash
CODE:
```
$ supabase migration list
        LOCAL      │     REMOTE     │     TIME (UTC)
  ─────────────────┼────────────────┼──────────────────────
                   │ 20230103054303 │ 2023-01-03 05:43:03
   20230103054315  │                │ 2023-01-03 05:43:15
```

----------------------------------------

TITLE: Example Output: Total Database Index Size
DESCRIPTION: This snippet shows a typical output from the `db-total-index-size` command, indicating the aggregated size of all indexes in the database.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-total-index-size.md#_snippet_0

LANGUAGE: CLI Output
CODE:
```
    SIZE
  ─────────
    12 MB
```

----------------------------------------

TITLE: Run all Go tests for Supabase CLI
DESCRIPTION: This command executes all unit and integration tests for the Supabase CLI project. It includes race detection, verbose output, runs each test once, and stops immediately on the first test failure.

SOURCE: https://github.com/supabase/cli/blob/develop/CONTRIBUTING.md#_snippet_0

LANGUAGE: bash
CODE:
```
go test ./... -race -v -count=1 -failfast
```

----------------------------------------

TITLE: Example Output of db-unused-indexes Command
DESCRIPTION: This snippet shows a typical tabular output from the `db-unused-indexes` command. It lists database tables, their associated indexes, the size of each index, and the number of scans recorded against it, highlighting indexes that are potentially unused.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-unused-indexes.md#_snippet_0

LANGUAGE: CLI Output
CODE:
```
        TABLE        │                   INDEX                    │ INDEX SIZE │ INDEX SCANS
─────────────────────┼────────────────────────────────────────────┼────────────┼──────────────
 public.users        │ user_id_created_at_idx                     │ 97 MB      │           0
```

----------------------------------------

TITLE: CLI Output: Sequential Scan Counts by Table
DESCRIPTION: An example of the output generated by the `db-seq-scans` command, showing a list of database tables and the total number of sequential scans performed on each, ordered by count in descending order. This output helps identify tables that might benefit from further indexing optimization.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-seq-scans.md#_snippet_0

LANGUAGE: CLI
CODE:
```
                  NAME               │ COUNT
  ───────────────────────────────────┼─────────
    emails                           │ 182435
    users                            │  25063
    job_run_details                  │     60
    schema_migrations                │      0
    migrations                       │      0
```

----------------------------------------

TITLE: Example Output of `db-outliers` Command
DESCRIPTION: This table illustrates the output of the `db-outliers` command, providing a breakdown of PostgreSQL query performance. It includes the query text, total execution time, its proportion of overall execution time, the number of calls, and time spent on synchronous I/O. This data is crucial for identifying and optimizing performance bottlenecks in a PostgreSQL database.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-outliers.md#_snippet_0

LANGUAGE: CLI Output
CODE:
```
                 QUERY                   │ EXECUTION TIME   │ PROPORTION OF EXEC TIME │ NUMBER CALLS │ SYNC IO TIME
─────────────────────────────────────────┼──────────────────┼─────────────────────────┼──────────────┼───────────────
 SELECT * FROM archivable_usage_events.. │ 154:39:26.431466 │ 72.2%                   │ 34,211,877   │ 00:00:00
 COPY public.archivable_usage_events (.. │ 50:38:33.198418  │ 23.6%                   │ 13           │ 13:34:21.00108
 COPY public.usage_events (id, reporte.. │ 02:32:16.335233  │ 1.2%                    │ 13           │ 00:34:19.784318
 INSERT INTO usage_events (id, retaine.. │ 01:42:59.436532  │ 0.8%                    │ 12,328,187   │ 00:00:00
 SELECT * FROM usage_events WHERE (alp.. │ 01:18:10.754354  │ 0.6%                    │ 102,114,301  │ 00:00:00
```

----------------------------------------

TITLE: Example Output of Supabase CLI db-bloat Command
DESCRIPTION: This snippet shows a sample output from the `supabase db-bloat` command, illustrating how it presents bloat statistics for various database objects like tables and indexes. The output includes the object type, schema name, object name, bloat percentage, and wasted space.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-bloat.md#_snippet_0

LANGUAGE: Text
CODE:
```
    TYPE  │ SCHEMA NAME │        OBJECT NAME         │ BLOAT │ WASTE
  ────────┼─────────────┼────────────────────────────┼───────┼─────────────
    table │ public      │ very_bloated_table         │  41.0 │ 700 MB
    table │ public      │ my_table                   │   4.0 │ 76 MB
    table │ public      │ happy_table                │   1.0 │ 1472 kB
    index │ public      │ happy_table::my_nice_index │   0.7 │ 880 kB
```

----------------------------------------

TITLE: Example Output: Long-Running PostgreSQL Queries
DESCRIPTION: This output displays a table of PostgreSQL queries that have been running for over 5 minutes. Each row includes the Process ID (PID), the duration of the query, and the full SQL query text. This information is crucial for identifying and troubleshooting performance bottlenecks or blocking operations.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-long-running-queries.md#_snippet_0

LANGUAGE: SQL
CODE:
```
  PID  │     DURATION    │                                         QUERY
───────┼─────────────────┼───────────────────────────────────────────────────────────────────────────────────────
 19578 | 02:29:11.200129 | EXPLAIN SELECT  "students".* FROM "students"  WHERE "students"."id" = 1450645 LIMIT 1
 19465 | 02:26:05.542653 | EXPLAIN SELECT  "students".* FROM "students"  WHERE "students"."id" = 1889881 LIMIT 1
 19632 | 02:24:46.962818 | EXPLAIN SELECT  "students".* FROM "students"  WHERE "students"."id" = 1581884 LIMIT 1
```

----------------------------------------

TITLE: Delete Local Migration File and Re-list
DESCRIPTION: This example shows how to delete a specific local migration file to prepare for history repair, followed by re-listing migrations to confirm its removal from the local history.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/migration/repair.md#_snippet_1

LANGUAGE: bash
CODE:
```
$ rm supabase/migrations/20230103054315_remote_commit.sql

$ supabase migration list
        LOCAL      │     REMOTE     │     TIME (UTC)
  ─────────────────┼────────────────┼──────────────────────
                   │ 20230103054303 │ 2023-01-03 05:43:03
```

----------------------------------------

TITLE: Pull Remote Database Schema and Apply Migration
DESCRIPTION: This example demonstrates using `supabase db pull` to dump the remote schema into a new local migration file. It also shows how the tool automatically prompts to update the remote migration history, marking the new migration as 'applied' to bring local and remote states back into sync.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/migration/repair.md#_snippet_3

LANGUAGE: bash
CODE:
```
$ supabase db pull
Connecting to remote database...
Schema written to supabase/migrations/20240414044403_remote_schema.sql
Update remote migration history table? [Y/n]
Repaired migration history: [20240414044403] => applied
Finished supabase db pull.

$ supabase migration list
        LOCAL      │     REMOTE     │     TIME (UTC)
  ─────────────────┼────────────────┼──────────────────────
    20240414044403 │ 20240414044403 │ 2024-04-14 04:44:03
```

----------------------------------------

TITLE: Develop Supabase CLI from Source
DESCRIPTION: Instructions for running the Supabase CLI directly from its source code using Go. This requires Go version 1.22 or higher.

SOURCE: https://github.com/supabase/cli/blob/develop/README.md#_snippet_8

LANGUAGE: sh
CODE:
```
go run . help
```

----------------------------------------

TITLE: Build CLI Command Reference YAML
DESCRIPTION: This command executes a Go program to generate a YAML file containing the Supabase CLI command reference. The output is directed to `cli_v1_commands.yaml`.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
go run docs/main.go > cli_v1_commands.yaml
```

----------------------------------------

TITLE: Dump Initial Schema to Versioned SQL File
DESCRIPTION: Command to execute the `dump_initial_schema.sh` script, which generates the initial database schema. The output is then redirected and saved into a version-specific SQL file, such as `15.sql`.

SOURCE: https://github.com/supabase/cli/blob/develop/internal/utils/templates/initial_schemas/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
./tools/dump_initial_schema.sh > internal/utils/templates/initial_schemas/15.sql
```

----------------------------------------

TITLE: Move and Format CLI Command Reference YAML
DESCRIPTION: These commands are part of the release process. The first command moves the generated CLI reference YAML file into the `specs/` directory. The second command uses `npx prettier` to reformat the YAML file, ensuring consistent styling.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
mv ../cli/cli_v1_commands.yaml specs/
npx prettier -w specs/cli_v1_commands.yaml
```

----------------------------------------

TITLE: Supabase CLI: `supabase/config.toml` Edge Runtime Configuration
DESCRIPTION: This section describes the customizable properties within the `edge_runtime` section of the `supabase/config.toml` file. These settings allow fine-tuning of the local Edge Function environment, particularly concerning the inspector port and HTTP request forwarding policy.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/functions/serve.md#_snippet_1

LANGUAGE: APIDOC
CODE:
```
Configuration File: supabase/config.toml
  Section: [edge_runtime]
    Properties:
      inspector_port:
        Type: Integer
        Description: The port used to listen to the Inspector session.
        Default: 8083
      policy:
        Type: String (enum)
        Description: A value that indicates how the edge-runtime should forward incoming HTTP requests to the worker.
        Values:
          per_worker: Allows multiple HTTP requests to be forwarded to a worker that has already been created.
          oneshot: Will force the worker to process a single HTTP request and then exit. (Debugging purpose, especially useful for immediate reflection of changes.)
```

----------------------------------------

TITLE: Create New Supabase Edge Function
DESCRIPTION: Initializes a new Supabase Edge Function project with boilerplate TypeScript code. This command creates a new directory containing an `index.ts` file with Deno imports and a basic function structure, ready for local development.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/functions/new.md#_snippet_0

LANGUAGE: Shell
CODE:
```
supabase functions new <function-name>
```

----------------------------------------

TITLE: Supabase CLI: `supabase functions serve` Debugging Flags
DESCRIPTION: This section details the command-line flags available for `supabase functions serve` that enable and control the v8 inspector protocol. These flags are crucial for debugging Edge Functions locally and integrating with development tools like Chrome DevTools, VS Code, and IntelliJ IDEA.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/functions/serve.md#_snippet_0

LANGUAGE: APIDOC
CODE:
```
Command: supabase functions serve
  Flags:
    --inspect:
      Description: Alias of --inspect-mode brk.
    --inspect-mode [run | brk | wait]:
      Description: Activates the inspector capability.
      Modes:
        run: Allows a connection without additional behavior. Not ideal for short scripts, but useful for long-running scripts where occasional breakpoints are needed.
        brk: Same as run mode, but additionally sets a breakpoint at the first line to pause script execution before any code runs.
        wait: Similar to brk mode, but instead of setting a breakpoint at the first line, it pauses script execution until an inspector session is connected.
    --inspect-main:
      Description: Can only be used when --inspect or --inspect-mode is enabled. By default, creating an inspector session for the main worker is not allowed, but this flag allows it. Other behaviors follow the inspect-mode flag.
```

----------------------------------------

TITLE: Update Supabase OpenAPI Specification YAML
DESCRIPTION: This command downloads the latest OpenAPI specification YAML from a local development server (typically running on port 8080) and saves it to the `api/beta.yaml` file within the project directory. This step ensures the local specification is up-to-date with the latest API definitions.

SOURCE: https://github.com/supabase/cli/blob/develop/api/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
curl -o api/beta.yaml http://127.0.0.1:8080/api/v1-yaml
```

----------------------------------------

TITLE: Regenerate Go Client and API Types
DESCRIPTION: After updating the OpenAPI specification, this command is used to regenerate the Go client and API types. It leverages Go's built-in `go generate` tool, which executes commands specified in `//go:generate` directives within the Go source files, ensuring the Go code reflects the latest API structure.

SOURCE: https://github.com/supabase/cli/blob/develop/api/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
go generate
```

----------------------------------------

TITLE: Serve Supabase Edge Function Locally
DESCRIPTION: Runs a Supabase Edge Function locally for testing and development. This command allows developers to test their function's behavior before deploying it to the Supabase platform.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/functions/new.md#_snippet_1

LANGUAGE: Shell
CODE:
```
supabase functions serve
```

----------------------------------------

TITLE: Supabase CLI Output: Active Database Role Connections
DESCRIPTION: This snippet displays the typical output of the `supabase db-role-connections` command. It provides a table listing each database role and its corresponding number of active connections, followed by a summary of total active connections versus the maximum allowed.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-role-connections.md#_snippet_0

LANGUAGE: Shell
CODE:
```
            ROLE NAME         │ ACTIVE CONNCTION
  ────────────────────────────┼───────────────────
    authenticator             │                5
    postgres                  │                5
    supabase_admin            │                1
    pgbouncer                 │                1
    anon                      │                0
    authenticated             │                0
    service_role              │                0
    dashboard_user            │                0
    supabase_auth_admin       │                0
    supabase_storage_admin    │                0
    supabase_functions_admin  │                0
    pgsodium_keyholder        │                0
    pg_read_all_data          │                0
    pg_write_all_data         │                0
    pg_monitor                │                0

Active connections 12/90
```

----------------------------------------

TITLE: Analyze Database Index Usage CLI Output
DESCRIPTION: Displays a table showing index usage percentage, total rows, and table names. This output helps identify tables with low index usage, indicating potential under-indexing or inefficient indexing strategies.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-index-usage.md#_snippet_0

LANGUAGE: CLI Output
CODE:
```
       TABLE NAME     │ PERCENTAGE OF TIMES INDEX USED │ ROWS IN TABLE
  ────────────────────┼────────────────────────────────┼────────────────
    user_events       │                             99 │       4225318 
    user_feed         │                             99 │       3581573
    unindexed_table   │                              0 │        322911
    job               │                            100 │         33242
    schema_migrations │                             97 │             0
    migrations        │ Insufficient data              │             0
```

----------------------------------------

TITLE: PostgreSQL Database Vacuum Statistics CLI Output
DESCRIPTION: This table displays the vacuum activity statistics for various PostgreSQL tables. It includes the schema, table name, timestamps for the last manual and auto vacuum operations, the current row count, the count of dead rows, and an indicator of whether autovacuum is expected to run for that table.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-vacuum-stats.md#_snippet_0

LANGUAGE: CLI
CODE:
```
        SCHEMA        │              TABLE               │ LAST VACUUM │ LAST AUTO VACUUM │      ROW COUNT       │ DEAD ROW COUNT │ EXPECT AUTOVACUUM?
──────────────────────┼──────────────────────────────────┼─────────────┼──────────────────┼──────────────────────┼────────────────┼─────────────────────
 auth                 │ users                            │             │ 2023-06-26 12:34 │               18,030 │              0 │ no
 public               │ profiles                         │             │ 2023-06-26 23:45 │               13,420 │             28 │ no
 public               │ logs                             │             │ 2023-06-26 01:23 │            1,313,033 │      3,318,228 │ yes
 storage              │ objects                          │             │                  │             No stats │              0 │ no
 storage              │ buckets                          │             │                  │             No stats │              0 │ no
 supabase_migrations  │ schema_migrations                │             │                  │             No stats │              0 │ no
```

----------------------------------------

TITLE: Deploy Supabase Edge Function
DESCRIPTION: Deploys a Supabase Edge Function to the Supabase platform. This command pushes the local function code to the cloud, making it accessible via the Supabase API.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/functions/new.md#_snippet_2

LANGUAGE: Shell
CODE:
```
supabase functions deploy
```

----------------------------------------

TITLE: Force Terminate a PostgreSQL Backend Process
DESCRIPTION: If `pg_cancel_backend` does not stop a query, this command can be used to forcefully terminate the entire backend process associated with a specific PID. This should be used with caution as it can abruptly end ongoing transactions and should only be used when `pg_cancel_backend` is ineffective.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-locks.md#_snippet_1

LANGUAGE: PostgreSQL
CODE:
```
SELECT pg_terminate_backend(PID);
```

----------------------------------------

TITLE: Repair Supabase Migration History (Revert)
DESCRIPTION: This snippet illustrates how to use `supabase migration repair` to mark a specific remote migration as 'reverted'. This action effectively deletes the corresponding record from the remote migration history table.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/migration/repair.md#_snippet_2

LANGUAGE: bash
CODE:
```
$ supabase migration repair 20230103054303 --status reverted
Connecting to remote database...
Repaired migration history: [20220810154537] => reverted
Finished supabase migration repair.

$ supabase migration list
        LOCAL      │     REMOTE     │     TIME (UTC)
  ─────────────────┼────────────────┼──────────────────────
```

----------------------------------------

TITLE: Cancel a Hung PostgreSQL Query
DESCRIPTION: This command allows you to gracefully cancel a PostgreSQL query that is currently running and potentially causing blocking issues. It sends a signal to the backend process to stop the query, but the process itself remains active.

SOURCE: https://github.com/supabase/cli/blob/develop/docs/supabase/inspect/db-locks.md#_snippet_0

LANGUAGE: PostgreSQL
CODE:
```
SELECT pg_cancel_backend(PID);
```