{"config": {"configFile": "/home/<USER>/Desktop/agri project/project/Web-app/playwright.config.ts", "rootDir": "/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e/global-setup.ts", "globalTeardown": "/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/home/<USER>/Desktop/agri project/project/Web-app/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/Desktop/agri project/project/Web-app/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/Desktop/agri project/project/Web-app/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/Desktop/agri project/project/Web-app/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/Desktop/agri project/project/Web-app/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.1", "workers": 2, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1193/chrome-linux/headless_shell\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1193/chrome-linux/headless_shell\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝\n    at globalSetup (/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e/global-setup.ts:40:34)", "location": {"file": "/home/<USER>/Desktop/agri project/project/Web-app/tests/e2e/global-setup.ts", "column": 34, "line": 40}, "snippet": "\u001b[90m   at \u001b[39mglobal-setup.ts:40\n\n  38 |\n  39 |   // Create test data\n> 40 |   const browser = await chromium.launch();\n     |                                  ^\n  41 |   const page = await browser.newPage();\n  42 |   \n  43 |   try {"}], "stats": {"startTime": "2025-09-30T15:03:26.611Z", "duration": 27917.519, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}