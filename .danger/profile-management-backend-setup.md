# Profile Management: Backend SQL Setup

This file contains all the necessary SQL code to set up the backend for the user profile management and expert verification system.

## 1. Helper Function

This function checks if a user has an admin role and is used to simplify RLS policies.

```sql
-- Function to check if a user has an admin role
CREATE OR REPLACE FUNCTION is_admin(user_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = user_id AND r.is_admin_role = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 2. Row Level Security (RLS) Policies

These policies enforce access control at the database level.

### `profiles` Table RLS

```sql
-- Allow individual user to view their own profile
CREATE POLICY "Allow individual user to view their own profile"
ON public.profiles FOR SELECT
USING (auth.uid() = id);

-- Allow individual user to update their own profile
CREATE POLICY "Allow individual user to update their own profile"
ON public.profiles FOR UPDATE
USING (auth.uid() = id);

-- Allow admins to view all profiles
CREATE POLICY "Allow admins to view all profiles"
ON public.profiles FOR SELECT
USING (is_admin(auth.uid()));
```

### `expert_profiles` Table RLS

```sql
-- Allow expert to view their own expert profile
CREATE POLICY "Allow expert to view their own expert profile"
ON public.expert_profiles FOR SELECT
USING (auth.uid() = id);

-- Allow expert to update their own expert profile (but not verification_status)
CREATE POLICY "Allow expert to update their own expert profile"
ON public.expert_profiles FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id AND verification_status = (SELECT verification_status FROM public.expert_profiles WHERE id = auth.uid()));

-- Allow admins to view all expert profiles
CREATE POLICY "Allow admins to view all expert profiles"
ON public.expert_profiles FOR SELECT
USING (is_admin(auth.uid()));
```

### `user_roles` Table RLS

```sql
-- Allow admins to manage user roles
CREATE POLICY "Allow admins to manage user roles"
ON public.user_roles FOR ALL
USING (is_admin(auth.uid()))
WITH CHECK (is_admin(auth.uid()));

-- Allow users to view their own role
CREATE POLICY "Allow users to view their own role"
ON public.user_roles FOR SELECT
USING (auth.uid() = user_id);
```

## 3. PostgreSQL Functions (RPC)

These `SECURITY DEFINER` functions handle security-critical operations.

```sql
-- Function for admins to approve an expert
CREATE OR REPLACE FUNCTION approve_expert(expert_user_id uuid)
RETURNS void AS $$
BEGIN
  IF NOT is_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Only admins can approve experts';
  END IF;

  UPDATE public.expert_profiles
  SET verification_status = 'approved'
  WHERE id = expert_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function for admins to reject an expert
CREATE OR REPLACE FUNCTION reject_expert(expert_user_id uuid)
RETURNS void AS $$
BEGIN
  IF NOT is_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Only admins can reject experts';
  END IF;

  UPDATE public.expert_profiles
  SET verification_status = 'rejected'
  WHERE id = expert_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;