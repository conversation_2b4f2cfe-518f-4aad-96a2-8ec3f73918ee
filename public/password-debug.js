// Password Setup Debug Utility
// Load this script to debug authentication issues

console.log('🔍 Password Setup Debug Utility Loaded');

// Create global debug namespace
window.PasswordDebug = {
  version: '1.0.0',
  
  // Check current authentication state
  async checkAuth() {
    console.log('🔐 Checking authentication state...');
    
    try {
      // Check if Supabase is available
      if (!window.supabase) {
        console.error('❌ Supabase client not found');
        return { error: 'Supabase client not available' };
      }
      
      // Get current session
      const { data: sessionData, error: sessionError } = await window.supabase.auth.getSession();
      
      if (sessionError) {
        console.error('❌ Session error:', sessionError);
        return { error: sessionError };
      }
      
      // Get current user
      const { data: userData, error: userError } = await window.supabase.auth.getUser();
      
      if (userError) {
        console.error('❌ User error:', userError);
        return { error: userError };
      }
      
      // Display results in a table format
      console.log('🔑 Auth Results:');
      console.table({
        hasSession: !!sessionData.session,
        sessionUserID: sessionData.session?.user?.id || 'none',
        hasUser: !!userData.user,
        userID: userData.user?.id || 'none',
        userEmail: userData.user?.email || 'none',
        userRole: userData.user?.user_metadata?.role || 'none',
        authProvider: userData.user?.app_metadata?.provider || 'none'
      });
      
      return {
        session: sessionData.session,
        user: userData.user
      };
    } catch (error) {
      console.error('❌ Authentication check failed:', error);
      return { error };
    }
  },
  
  // Process authentication code manually
  async processCode(code) {
    if (!code) {
      code = new URLSearchParams(window.location.search).get('code');
      if (!code) {
        console.error('❌ No authentication code found in URL');
        return { error: 'No code parameter in URL' };
      }
    }
    
    console.log(`🔄 Processing code: ${code.substring(0, 8)}...`);
    
    try {
      const { data, error } = await window.supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error('❌ Code exchange failed:', error);
        return { error };
      }
      
      console.log('✅ Code exchange successful:', data);
      
      // Verify the result
      await this.checkAuth();
      
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error processing code:', error);
      return { error };
    }
  },
  
  // Manually set password
  async setPassword(password, userId) {
    if (!password) {
      console.error('❌ No password provided');
      return { error: 'Password is required' };
    }
    
    try {
      // If no userId provided, try to get current user
      if (!userId) {
        const { user } = await this.checkAuth();
        userId = user?.id;
        
        if (!userId) {
          console.error('❌ No user ID available');
          return { error: 'No authenticated user found' };
        }
      }
      
      console.log(`🔐 Setting password for user ${userId}`);
      
      // Update password using Supabase auth
      const { data, error } = await window.supabase.auth.updateUser({
        password
      });
      
      if (error) {
        console.error('❌ Password update failed:', error);
        return { error };
      }
      
      console.log('✅ Password updated successfully');
      
      // Update password_set flag in database
      const { error: updateError } = await window.supabase
        .from('profiles')
        .update({ 
          password_set: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);
      
      if (updateError) {
        console.warn('⚠️ Password set in auth but failed to update database flag:', updateError);
      } else {
        console.log('✅ Database updated successfully');
      }
      
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error setting password:', error);
      return { error };
    }
  },
  
  // Refresh session
  async refreshSession() {
    console.log('🔄 Refreshing session...');
    
    try {
      const { data, error } = await window.supabase.auth.refreshSession();
      
      if (error) {
        console.error('❌ Session refresh failed:', error);
        return { error };
      }
      
      console.log('✅ Session refreshed successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error refreshing session:', error);
      return { error };
    }
  },
  
  // Clear auth storage
    clearStorage() {
      console.log('🧹 Clearing auth storage...');
    
      try {
        // Clear localStorage items related to auth
        const authKeys = [];
        try {
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.includes('supabase.auth')) {
              localStorage.removeItem(key);
              authKeys.push(key);
            }
          }
          console.log(`✅ Cleared ${authKeys.length} items from localStorage`);
        } catch (storageError) {
          console.warn('⚠️ localStorage access failed:', storageError);
        }
      
        // Also clear session storage
        try {
          sessionStorage.clear();
          console.log('✅ Cleared sessionStorage');
        } catch (sessionError) {
          console.warn('⚠️ sessionStorage access failed:', sessionError);
        }
      
        return { success: true, clearedKeys: authKeys };
      } catch (error) {
        console.error('❌ Error clearing storage:', error);
        return { error };
      }
    },
  
    // Fix localStorage issues
    fixLocalStorage() {
      console.log('🔧 Checking and fixing localStorage...');
    
      // Don't attempt to fix if page has its own aggressive override
      if (window._pageHasAggressiveLocalStorageOverride) {
        console.log('ⓘ Not applying localStorage fix because page.tsx override is active');
        return { working: false, skipped: true, usingPageOverride: true };
      }
    
      // Test if localStorage is working
      try {
        const testKey = '__test_localStorage__';
        localStorage.setItem(testKey, 'test');
        const testValue = localStorage.getItem(testKey);
        localStorage.removeItem(testKey);
    
        if (testValue === 'test') {
          console.log('✅ localStorage is working properly');
          return { working: true };
        } else {
          console.warn('⚠️ localStorage is not working correctly');
        }
      } catch (error) {
        console.error('❌ localStorage access failed:', error);
      }
    
      // Set up localStorage polyfill
      try {
        console.log('🔧 Installing localStorage polyfill...');
      
        const memoryStorage = {};
      
        // Save original if it exists
        if (typeof window.originalLocalStorage === 'undefined') {
          window.originalLocalStorage = window.localStorage;
        }
      
        // Create polyfill
        const polyfill = {
          setItem: function(key, value) {
            console.log(`[Polyfill] setItem: ${key}`);
            memoryStorage[key] = String(value);
          },
          getItem: function(key) {
            console.log(`[Polyfill] getItem: ${key}`);
            return memoryStorage[key] || null;
          },
          removeItem: function(key) {
            console.log(`[Polyfill] removeItem: ${key}`);
            delete memoryStorage[key];
          },
          clear: function() {
            console.log('[Polyfill] clear');
            Object.keys(memoryStorage).forEach(key => delete memoryStorage[key]);
          },
          key: function(index) {
            const keys = Object.keys(memoryStorage);
            return (index >= 0 && index < keys.length) ? keys[index] : null;
          },
          get length() {
            return Object.keys(memoryStorage).length;
          }
        };
      
        // Apply polyfill
        Object.defineProperty(window, 'localStorage', {
          value: polyfill,
          writable: false,
          configurable: true
        });
      
        console.log('✅ localStorage polyfill installed');
      
        // Test again
        localStorage.setItem('__test_polyfill__', 'test');
        const testResult = localStorage.getItem('__test_polyfill__');
        localStorage.removeItem('__test_polyfill__');
      
        return { 
          working: testResult === 'test', 
          usingPolyfill: true 
        };
      } catch (polyfillError) {
        console.error('❌ Failed to install localStorage polyfill:', polyfillError);
        return { working: false, error: polyfillError };
      }
    },
  
  // Run diagnostic tests
  async runDiagnostics() {
    console.log('🩺 Running diagnostics...');
    
    // Test localStorage
    let localStorageWorking = false;
    let localStorageError = null;
    try {
      const testKey = '__test_localStorage__';
      localStorage.setItem(testKey, 'test');
      const testValue = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);
      localStorageWorking = (testValue === 'test');
    } catch (error) {
      localStorageError = error;
      localStorageWorking = false;
    }
    
    const results = {
      url: window.location.href,
      hasCode: new URLSearchParams(window.location.search).has('code'),
      browser: navigator.userAgent,
      localStorageWorking,
      localStorage: 0,
      authItems: 0,
      usingPolyfill: !!(window.originalLocalStorage),
      pageHasOverride: !!(window._pageHasAggressiveLocalStorageOverride)
    };
    
    // Count localStorage items if working
    if (localStorageWorking) {
      try {
        results.localStorage = localStorage.length;
        
        // Count auth items
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.includes('supabase.auth')) {
            results.authItems++;
          }
        }
      } catch (e) {
        console.warn('Failed to count localStorage items:', e);
      }
    }
    
    // Check auth state
    const authState = await this.checkAuth();
    results.hasSession = !!authState.session;
    results.hasUser = !!authState.user;
    
    console.log('📊 Diagnostic Results:');
    console.table(results);
    
    // Check for common issues
    const issues = [];
    
    if (results.hasCode && !results.hasSession) {
      issues.push('Has code parameter but no session - code exchange may have failed');
    }
    
    if (results.hasSession && !results.hasUser) {
      issues.push('Has session but no user - possible state synchronization issue');
    }
    
    if (!localStorageWorking) {
      if (results.pageHasOverride) {
        issues.push(`localStorage not working: ${localStorageError || 'Unknown error'} - but page has its own override active`);
      } else {
        issues.push(`localStorage not working: ${localStorageError || 'Unknown error'} - using polyfill is recommended`);
      }
    } else if (results.authItems === 0 && results.hasSession) {
      issues.push('No auth items in localStorage despite active session - session persistence issues likely');
    }
    
    if (issues.length > 0) {
      console.log('⚠️ Potential Issues:');
      issues.forEach(issue => console.log(`- ${issue}`));
    } else {
      console.log('✅ No issues detected');
    }
    
    return { results, issues, localStorageError };
  },
  
  // Fix common issues
  async quickFix() {
    console.log('🔧 Attempting quick fixes...');
    
    // 0. First, check and fix localStorage if needed (unless page has override)
    const diagnostics = await this.runDiagnostics();
    if (diagnostics.issues.some(issue => issue.includes('localStorage'))) {
      console.log('Step 0: Fixing localStorage issues...');
      if (window._pageHasAggressiveLocalStorageOverride) {
        console.log('ⓘ Page has its own localStorage override - skipping debug utility fix');
      } else {
        const localStorageFixed = this.fixLocalStorage();
        console.log('localStorage fix result:', localStorageFixed);
      }
    }
    
    // 1. Try refreshing the session
    console.log('Step 1: Refreshing session...');
    await this.refreshSession();
    
    // 2. Check if that fixed the issue
    const { user } = await this.checkAuth();
    if (user) {
      console.log('✅ User authenticated successfully');
      return { success: true, message: 'Session refresh fixed the issue' };
    }
    
    // 3. Try processing the code if present
    const code = new URLSearchParams(window.location.search).get('code');
    if (code) {
      console.log('Step 2: Processing code...');
      const result = await this.processCode(code);
      
      if (result.success) {
        console.log('✅ Code processing fixed the issue');
        return { success: true, message: 'Code processing fixed the issue' };
      }
    }
    
    // 4. Try re-authenticating directly
    console.log('Step 3: Re-authenticating directly...');
    try {
      // This forces a new session check with the server
      const { data, error } = await window.supabase.auth.getUser();
      
      if (error) {
        console.error('❌ Re-authentication failed:', error);
      } else if (data.user) {
        console.log('✅ Re-authentication successful');
        return { success: true, message: 'Direct re-authentication fixed the issue' };
      }
    } catch (e) {
      console.error('❌ Re-authentication error:', e);
    }
    
    // 5. Last resort - clear storage and suggest reload
    console.log('Step 4: Clearing auth storage and applying localStorage fix...');
    this.clearStorage();
    this.fixLocalStorage();
    
    console.log('⚠️ Basic fixes unsuccessful. Try refreshing the page.');
    return { 
      success: false,
      message: 'Quick fixes unsuccessful. Please refresh the page and try again.'
    };
  },
  
  // Create a simple in-memory backup of the auth state
  // Useful when localStorage isn't working
  createMemoryBackup() {
    console.log('📦 Creating in-memory auth backup...');
    
    window.AuthMemoryBackup = {
      user: null,
      session: null,
      
      save: async function() {
        try {
          const { data: { session } } = await window.supabase.auth.getSession();
          const { data: { user } } = await window.supabase.auth.getUser();
          
          this.session = session;
          this.user = user;
          
          console.log('✅ Auth backup created:', user?.id);
          return { success: true, user, session };
        } catch (error) {
          console.error('❌ Failed to create auth backup:', error);
          return { success: false, error };
        }
      },
      
      apply: function() {
        if (this.user && this.session) {
          console.log('✅ Restoring auth from backup:', this.user.id);
          return { user: this.user, session: this.session };
        }
        return null;
      },
      
      get isValid() {
        return !!(this.user && this.session);
      }
    };
    
    // Create initial backup
    window.AuthMemoryBackup.save();
    
    return window.AuthMemoryBackup;
  }
};

// Auto-run diagnostics and fixes for localStorage
if (window.location.pathname.includes('/auth/set-password')) {
  console.log('📋 Auto-running diagnostics for password setup page...');
  
  // Test localStorage first - if not working, fix immediately
  try {
    localStorage.setItem('__test__', 'test');
    if (localStorage.getItem('__test__') !== 'test') {
      if (!(window._pageHasAggressiveLocalStorageOverride)) {
        console.warn('⚠️ localStorage (from password-debug.js) not working properly - auto-applying fix');
        setTimeout(() => window.PasswordDebug.fixLocalStorage(), 200);
      } else {
        console.log('ⓘ localStorage test (from password-debug.js) failed, but page.tsx override is active. Skipping password-debug.js fix.');
      }
    }
    localStorage.removeItem('__test__');
  } catch (e) {
    if (!(window._pageHasAggressiveLocalStorageOverride)) {
      console.error('⚠️ localStorage error (from password-debug.js) detected - auto-applying fix');
      setTimeout(() => window.PasswordDebug.fixLocalStorage(), 200);
    } else {
      console.log('ⓘ localStorage error (from password-debug.js) detected, but page.tsx override is active. Skipping password-debug.js fix.');
    }
  }
  
  // Run full diagnostics
  setTimeout(() => {
    window.PasswordDebug.runDiagnostics();
    
    // Create memory backup in case localStorage fails
    window.PasswordDebug.createMemoryBackup();
  }, 1000);
}

console.log('✅ Password Debug Utility Ready!');
console.log('Usage examples:');
console.log('- PasswordDebug.checkAuth() - Check current auth state');
console.log('- PasswordDebug.processCode() - Process the code in the URL');
console.log('- PasswordDebug.quickFix() - Attempt to fix common issues');
console.log('- PasswordDebug.runDiagnostics() - Run all diagnostics');
console.log('- PasswordDebug.fixLocalStorage() - Fix localStorage issues');