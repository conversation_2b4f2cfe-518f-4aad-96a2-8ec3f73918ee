// Debug Auth State Helper for <PERSON>rowser Console
// Paste this into browser console on the password setup page to debug auth issues

console.log('🔧 Auth Debug Helper Loaded');

// Global debug object
window.AuthDebug = {
  
  // Check current Supabase auth state
  async checkAuthState() {
    console.log('🔍 Checking current auth state...');
    
    try {
      const { data: { session }, error: sessionError } = await window.supabase?.auth?.getSession();
      const { data: { user }, error: userError } = await window.supabase?.auth?.getUser();
      
      console.log('📊 Auth State Results:', {
        hasSupabase: !!window.supabase,
        session: {
          exists: !!session,
          userId: session?.user?.id,
          error: sessionError?.message
        },
        user: {
          exists: !!user,
          userId: user?.id,
          metadata: user?.user_metadata,
          error: userError?.message
        },
        currentURL: window.location.href,
        timestamp: new Date().toISOString()
      });
      
      return { session, user };
    } catch (error) {
      console.error('❌ Error checking auth state:', error);
      return { session: null, user: null, error };
    }
  },

  // Check if we're on password setup page and why it might be stuck
  async diagnosePage() {
    console.log('🏥 Diagnosing password setup page...');
    
    const isPasswordPage = window.location.pathname === '/auth/set-password';
    const hasCode = new URLSearchParams(window.location.search).get('code');
    
    console.log('📋 Page Diagnosis:', {
      isPasswordSetupPage: isPasswordPage,
      hasConfirmationCode: !!hasCode,
      codeValue: hasCode ? hasCode.substring(0, 8) + '...' : null,
      pageTitle: document.title,
      timestamp: new Date().toISOString()
    });

    if (isPasswordPage) {
      // Check for React state indicators
      const loadingIndicators = document.querySelectorAll('[class*="loading"], [class*="spin"]');
      const passwordForm = document.querySelector('form');
      
      console.log('🎯 Password Page Elements:', {
        hasLoadingIndicators: loadingIndicators.length > 0,
        hasPasswordForm: !!passwordForm,
        formVisible: passwordForm ? !passwordForm.hidden : false,
        loadingElements: Array.from(loadingIndicators).map(el => el.className)
      });
    }

    await this.checkAuthState();
  },

  // Force refresh auth state
  async forceRefresh() {
    console.log('🔄 Forcing auth state refresh...');
    
    try {
      const refreshResult = await window.supabase?.auth?.refreshSession();
      console.log('✅ Session refresh result:', refreshResult);
      
      setTimeout(async () => {
        await this.checkAuthState();
      }, 1000);
      
      return refreshResult;
    } catch (error) {
      console.error('❌ Error refreshing session:', error);
      return { error };
    }
  },

  // Clear auth cache and localStorage
  clearCache() {
    console.log('🧹 Clearing auth cache...');
    
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('supabase.auth.') || key.startsWith('processed_code_'))) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.log('🗑️ Removed:', key);
    });
    
    console.log(`✅ Cleared ${keysToRemove.length} cache entries`);
  },

  // Monitor auth state changes
  startMonitoring() {
    console.log('👁️ Starting auth state monitoring...');
    
    if (this.subscription) {
      this.stopMonitoring();
    }
    
    this.subscription = window.supabase?.auth?.onAuthStateChange((event, session) => {
      console.log('🔔 Auth State Change:', {
        event,
        userId: session?.user?.id,
        timestamp: new Date().toISOString()
      });
    });
    
    console.log('✅ Monitoring started');
  },

  // Stop monitoring auth state changes
  stopMonitoring() {
    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
      console.log('⏹️ Monitoring stopped');
    }
  },

  // Test password setup flow
  async testPasswordFlow() {
    console.log('🧪 Testing password setup flow...');
    
    // Check if we have the required elements
    const passwordInput = document.querySelector('input[type="password"]');
    const submitButton = document.querySelector('button[type="submit"]');
    
    if (!passwordInput || !submitButton) {
      console.error('❌ Password form elements not found');
      return;
    }
    
    console.log('✅ Password form elements found');
    
    // Check auth state
    const { user } = await this.checkAuthState();
    
    if (!user) {
      console.error('❌ No authenticated user found');
      return;
    }
    
    console.log('✅ User authenticated, password flow should work');
  },

  // Simulate code processing for testing
  async simulateCodeProcessing() {
    const code = new URLSearchParams(window.location.search).get('code');
    
    if (!code) {
      console.error('❌ No confirmation code in URL');
      return;
    }
    
    console.log('🔄 Simulating code processing...');
    
    try {
      const result = await window.supabase?.auth?.exchangeCodeForSession(code);
      console.log('✅ Code exchange result:', result);
      
      setTimeout(async () => {
        await this.checkAuthState();
      }, 1000);
      
      return result;
    } catch (error) {
      console.error('❌ Code exchange failed:', error);
      return { error };
    }
  },

  // Force state update (emergency button)
  forceStateUpdate() {
    console.log('⚡ Forcing state update...');
    
    // Try to trigger a re-render by dispatching events
    window.dispatchEvent(new Event('resize'));
    window.dispatchEvent(new CustomEvent('auth-debug-update'));
    
    // Force router refresh if Next.js is available
    if (window.next && window.next.router) {
      window.next.router.reload();
    }
    
    console.log('✅ State update events dispatched');
  },

  // Complete diagnostic report
  async fullDiagnosis() {
    console.log('🔬 Running full authentication diagnosis...');
    console.log('================================================');
    
    await this.diagnosePage();
    console.log('------------------------------------------------');
    await this.checkAuthState();
    console.log('------------------------------------------------');
    
    // Check for common issues
    const issues = [];
    
    const { session, user } = await this.checkAuthState();
    
    if (!session) issues.push('No active session found');
    if (!user) issues.push('No authenticated user found');
    
    const isPasswordPage = window.location.pathname === '/auth/set-password';
    const hasCode = new URLSearchParams(window.location.search).get('code');
    
    if (isPasswordPage && !hasCode) issues.push('On password page but no confirmation code');
    if (hasCode && !session) issues.push('Has confirmation code but no session');
    
    const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spin"]');
    const passwordForm = document.querySelector('form');
    
    if (loadingElements.length > 0 && !passwordForm) {
      issues.push('Stuck in loading state - form not visible');
    }
    
    console.log('🚨 Issues Found:', issues.length > 0 ? issues : ['No issues detected']);
    console.log('================================================');
    
    if (issues.length > 0) {
      console.log('💡 Suggested Actions:');
      console.log('1. Try: AuthDebug.forceRefresh()');
      console.log('2. Try: AuthDebug.clearCache() then refresh page');
      console.log('3. Try: AuthDebug.simulateCodeProcessing()');
      console.log('4. Try: AuthDebug.forceStateUpdate()');
    }
  },

  // Quick fix attempts
  async quickFix() {
    console.log('🔧 Attempting quick fixes...');
    
    // Step 1: Force refresh
    console.log('Step 1: Refreshing session...');
    await this.forceRefresh();
    
    // Step 2: Check if that resolved it
    const { user } = await this.checkAuthState();
    if (user) {
      console.log('✅ Quick fix successful! User found:', user.id);
      this.forceStateUpdate();
      return;
    }
    
    // Step 3: Try code processing if we have a code
    const code = new URLSearchParams(window.location.search).get('code');
    if (code) {
      console.log('Step 2: Processing confirmation code...');
      await this.simulateCodeProcessing();
      
      const { user: userAfterCode } = await this.checkAuthState();
      if (userAfterCode) {
        console.log('✅ Code processing successful! User found:', userAfterCode.id);
        this.forceStateUpdate();
        return;
      }
    }
    
    // Step 4: Clear cache and suggest manual refresh
    console.log('Step 3: Clearing cache...');
    this.clearCache();
    
    console.log('❌ Quick fix unsuccessful. Try refreshing the page manually.');
  }
};

// Auto-run diagnosis if we're on the password setup page
if (window.location.pathname === '/auth/set-password') {
  console.log('🚀 Auto-running diagnosis for password setup page...');
  setTimeout(() => {
    window.AuthDebug.fullDiagnosis();
  }, 1000);
}

console.log('✅ Auth Debug Helper Ready!');
console.log('Usage:');
console.log('- AuthDebug.fullDiagnosis() - Complete diagnostic');
console.log('- AuthDebug.quickFix() - Attempt automatic fixes');
console.log('- AuthDebug.checkAuthState() - Check current auth state');
console.log('- AuthDebug.clearCache() - Clear auth cache');
console.log('- AuthDebug.forceRefresh() - Force session refresh');