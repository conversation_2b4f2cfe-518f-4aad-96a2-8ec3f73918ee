import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');

  const { exec } = await import('child_process');
  const { promisify } = await import('util');
  const execAsync = promisify(exec);

  try {
    // Clean up test data
    console.log('🗑️ Cleaning up test data...');
    await execAsync('supabase db reset --linked=false');
    console.log('✅ Test data cleaned up');

    // Optionally stop Supabase (uncomment if you want to stop after tests)
    // console.log('🛑 Stopping Supabase...');
    // await execAsync('supabase stop');
    // console.log('✅ Supabase stopped');

  } catch (error) {
    console.warn('⚠️ Error during teardown:', error);
  }

  console.log('🎯 Global teardown completed!');
}

export default globalTeardown;