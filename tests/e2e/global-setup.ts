import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...');

  // Start Supabase if not already running
  const { exec } = await import('child_process');
  const { promisify } = await import('util');
  const execAsync = promisify(exec);

  try {
    // Check if Supabase is running
    const { stdout } = await execAsync('supabase status');
    if (!stdout.includes('supabase local development setup is running')) {
      console.log('📦 Starting Supabase...');
      await execAsync('supabase start');
      console.log('✅ Supabase started successfully');
    } else {
      console.log('✅ Supabase is already running');
    }
  } catch (error) {
    console.warn('⚠️ Could not start Supabase automatically:', error);
    console.log('Please ensure Supabase is running with: supabase start');
  }

  // Apply database migrations
  try {
    console.log('🔄 Applying database migrations...');
    await execAsync('supabase db reset --linked=false');
    console.log('✅ Database reset completed');

    // Apply expert verification migrations
    await execAsync('supabase db push');
    console.log('✅ Database migrations applied');
  } catch (error) {
    console.warn('⚠️ Could not apply migrations:', error);
  }

  // Create test data
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for the application to be available
    const baseURL = config.projects[0].use?.baseURL || 'http://localhost:3000';
    console.log(`🌐 Waiting for application at ${baseURL}...`);
    
    let retries = 30;
    while (retries > 0) {
      try {
        const response = await page.goto(`${baseURL}/api/health`, { 
          timeout: 5000,
          waitUntil: 'networkidle' 
        });
        if (response?.ok()) {
          console.log('✅ Application is ready');
          break;
        }
      } catch (error) {
        retries--;
        if (retries === 0) {
          throw new Error(`Application not available after 30 retries: ${error}`);
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // Create test admin account
    console.log('👤 Creating test admin account...');
    await page.goto(`${baseURL}/api/test/setup`, { method: 'POST' });
    console.log('✅ Test accounts created');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }

  console.log('🎉 Global setup completed successfully!');
}

export default globalSetup;