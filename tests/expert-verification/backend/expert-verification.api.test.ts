import { describe, expect, it, beforeEach, afterEach, vi } from 'vitest';
import { createClient } from '@supabase/supabase-js';

// Mock environment for testing
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

// Create service role client for testing
const supabase = createClient(supabaseUrl, supabaseServiceKey);

describe('Expert Verification API Functions', () => {
  let testExpertId: string;
  let testAdminId: string;

  beforeEach(async () => {
    // Create test admin
    const { data: adminData, error: adminError } = await supabase
      .from('profiles')
      .insert({
        id: '********-0000-0000-0000-************',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'Admin',
        role: 'admin',
        account_activated: true
      })
      .select()
      .single();

    if (adminError && adminError.code !== '23505') { // Ignore duplicate key error
      throw adminError;
    }
    testAdminId = '********-0000-0000-0000-************';

    // Create test expert
    const { data: expertData, error: expertError } = await supabase
      .from('profiles')
      .insert({
        id: '********-0000-0000-0000-************',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'Expert',
        role: 'expert',
        account_activated: false
      })
      .select()
      .single();

    if (expertError && expertError.code !== '23505') { // Ignore duplicate key error
      throw expertError;
    }
    testExpertId = '********-0000-0000-0000-************';

    // Create expert profile
    await supabase
      .from('expert_profiles')
      .insert({
        id: testExpertId,
        bio: 'Test expert for automated testing',
        years_of_experience: 5,
        education: 'Test University',
        expertise_area: 'Testing',
        qualifications: 'Test Certifications',
        verification_status: 'pending'
      });
  });

  afterEach(async () => {
    // Clean up test data
    await supabase.from('expert_verification_history').delete().eq('expert_id', testExpertId);
    await supabase.from('expert_documents').delete().eq('expert_id', testExpertId);
    await supabase.from('expert_profiles').delete().eq('id', testExpertId);
    await supabase.from('profiles').delete().eq('id', testExpertId);
    await supabase.from('profiles').delete().eq('id', testAdminId);
  });

  describe('Expert Document Upload', () => {
    it('should upload expert verification document', async () => {
      const { data, error } = await supabase.rpc('upload_expert_verification_document', {
        p_document_type: 'qualification',
        p_document_url: 'https://storage.example.com/test-doc.pdf',
        p_document_name: 'Test Qualification Document',
        p_file_size: 1024000,
        p_file_type: 'application/pdf'
      });

      // Note: This will fail without proper auth context, 
      // but tests the function exists and parameter validation
      expect(error).toBeDefined();
      expect(error?.message).toContain('Unauthorized');
    });

    it('should validate document parameters', async () => {
      const { error } = await supabase.rpc('upload_expert_verification_document', {
        p_document_type: '', // Invalid empty type
        p_document_url: 'https://storage.example.com/test-doc.pdf',
        p_document_name: 'Test Document',
        p_file_size: 1024000,
        p_file_type: 'application/pdf'
      });

      expect(error).toBeDefined();
    });
  });

  describe('Expert Verification Status', () => {
    it('should check expert consultation access', async () => {
      const { data, error } = await supabase.rpc('check_expert_consultation_access', {
        p_expert_id: testExpertId
      });

      expect(error).toBeNull();
      expect(data).toBe(false); // Should be false for pending expert
    });

    it('should return false for non-existent expert', async () => {
      const { data, error } = await supabase.rpc('check_expert_consultation_access', {
        p_expert_id: '00000000-0000-0000-0000-000000000000'
      });

      expect(error).toBeNull();
      expect(data).toBe(false);
    });

    it('should get expert verification details', async () => {
      const { data, error } = await supabase.rpc('get_expert_verification_details', {
        p_expert_id: testExpertId
      });

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data.expert_id).toBe(testExpertId);
      expect(data.verification_status).toBe('pending');
      expect(data.full_name).toBe('Test Expert');
      expect(data.documents).toBeDefined();
      expect(data.verification_history).toBeDefined();
    });

    it('should throw error for non-existent expert details', async () => {
      const { data, error } = await supabase.rpc('get_expert_verification_details', {
        p_expert_id: '00000000-0000-0000-0000-000000000000'
      });

      expect(error).toBeDefined();
      expect(error?.message).toContain('Expert not found');
    });
  });

  describe('Admin Verification Functions', () => {
    it('should get experts pending verification', async () => {
      const { data, error } = await supabase.rpc('get_experts_pending_verification');

      // Note: This will fail without admin auth context
      expect(error).toBeDefined();
      expect(error?.message).toContain('Unauthorized');
    });

    it('should update expert verification status', async () => {
      const { data, error } = await supabase.rpc('update_expert_verification_status', {
        p_expert_id: testExpertId,
        p_new_status: 'approved',
        p_admin_notes: 'Test approval',
        p_rejection_reasons: null
      });

      // Note: This will fail without admin auth context
      expect(error).toBeDefined();
      expect(error?.message).toContain('Unauthorized');
    });
  });

  describe('Database Constraints and Relationships', () => {
    it('should enforce foreign key constraints', async () => {
      const { error } = await supabase
        .from('expert_documents')
        .insert({
          expert_id: '00000000-0000-0000-0000-000000000000',
          document_type: 'qualification',
          document_url: 'https://example.com/doc.pdf',
          document_name: 'Test Document'
        });

      expect(error).toBeDefined();
      expect(error?.code).toBe('23503'); // Foreign key violation
    });

    it('should enforce verification status enum values', async () => {
      const { error } = await supabase
        .from('expert_profiles')
        .update({ verification_status: 'invalid_status' as any })
        .eq('id', testExpertId);

      expect(error).toBeDefined();
      expect(error?.code).toBe('22P02'); // Invalid enum value
    });

    it('should cascade delete related records', async () => {
      // Insert document
      await supabase
        .from('expert_documents')
        .insert({
          expert_id: testExpertId,
          document_type: 'qualification',
          document_url: 'https://example.com/doc.pdf',
          document_name: 'Test Document'
        });

      // Insert history
      await supabase
        .from('expert_verification_history')
        .insert({
          expert_id: testExpertId,
          previous_status: null,
          new_status: 'pending',
          admin_notes: 'Initial status'
        });

      // Delete expert profile
      await supabase.from('expert_profiles').delete().eq('id', testExpertId);

      // Check that related records are deleted
      const { data: documents } = await supabase
        .from('expert_documents')
        .select('*')
        .eq('expert_id', testExpertId);

      const { data: history } = await supabase
        .from('expert_verification_history')
        .select('*')
        .eq('expert_id', testExpertId);

      expect(documents).toHaveLength(0);
      expect(history).toHaveLength(0);
    });
  });

  describe('RLS Policy Testing', () => {
    it('should respect RLS policies for expert documents', async () => {
      // Insert document as service role
      const { data, error } = await supabase
        .from('expert_documents')
        .insert({
          expert_id: testExpertId,
          document_type: 'qualification',
          document_url: 'https://example.com/doc.pdf',
          document_name: 'Test Document'
        })
        .select();

      expect(error).toBeNull();
      expect(data).toHaveLength(1);
    });

    it('should respect RLS policies for verification history', async () => {
      // Insert history record as service role
      const { data, error } = await supabase
        .from('expert_verification_history')
        .insert({
          expert_id: testExpertId,
          previous_status: null,
          new_status: 'pending',
          admin_notes: 'Initial status'
        })
        .select();

      expect(error).toBeNull();
      expect(data).toHaveLength(1);
    });
  });

  describe('Data Validation', () => {
    it('should validate document types', async () => {
      const validTypes = ['qualification', 'certification', 'experience', 'id_document'];
      
      for (const type of validTypes) {
        const { error } = await supabase
          .from('expert_documents')
          .insert({
            expert_id: testExpertId,
            document_type: type,
            document_url: `https://example.com/${type}.pdf`,
            document_name: `Test ${type} Document`
          });

        expect(error).toBeNull();
      }
    });

    it('should validate verification status transitions', async () => {
      const validStatuses = ['pending', 'under_review', 'approved', 'rejected', 'suspended', 'resubmission_required'];
      
      for (const status of validStatuses) {
        const { error } = await supabase
          .from('expert_profiles')
          .update({ verification_status: status as any })
          .eq('id', testExpertId);

        expect(error).toBeNull();
      }
    });

    it('should handle null values appropriately', async () => {
      // Test that optional fields can be null
      const { error } = await supabase
        .from('expert_documents')
        .insert({
          expert_id: testExpertId,
          document_type: 'qualification',
          document_url: 'https://example.com/doc.pdf',
          document_name: 'Test Document',
          file_size: null,
          file_type: null,
          admin_notes: null
        });

      expect(error).toBeNull();
    });
  });
});