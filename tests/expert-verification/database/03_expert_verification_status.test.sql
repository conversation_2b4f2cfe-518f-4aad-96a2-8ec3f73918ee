-- Expert Verification Status Tests
-- Tests verification status management and transitions
-- File: tests/expert-verification/database/03_expert_verification_status.test.sql

BEGIN;

-- Load pgTAP extension
SELECT plan(25);

-- Setup test data
INSERT INTO public.profiles (id, email, first_name, last_name, role, account_activated)
VALUES 
    ('********-1111-1111-1111-************', '<EMAIL>', 'Test', 'Expert1', 'expert', false),
    ('********-3333-3333-3333-********3333', '<EMAIL>', 'Test', 'Admin', 'admin', true);

INSERT INTO public.expert_profiles (id, verification_status, bio, expertise_area)
VALUES ('********-1111-1111-1111-************', 'pending', 'Test expert bio', 'Testing');

-- Test 1: Default verification status
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'pending'::expert_verification_status,
    'Default verification status should be pending'
);

-- Test 2: Valid status transitions
UPDATE public.expert_profiles 
SET verification_status = 'under_review'
WHERE id = '********-1111-1111-1111-************';

SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'under_review'::expert_verification_status,
    'Should be able to update to under_review status'
);

-- Test 3: Update to approved status
UPDATE public.expert_profiles 
SET verification_status = 'approved'
WHERE id = '********-1111-1111-1111-************';

SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'approved'::expert_verification_status,
    'Should be able to update to approved status'
);

-- Test 4: Update to rejected status
UPDATE public.expert_profiles 
SET verification_status = 'rejected'
WHERE id = '********-1111-1111-1111-************';

SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'rejected'::expert_verification_status,
    'Should be able to update to rejected status'
);

-- Test 5: Update to suspended status
UPDATE public.expert_profiles 
SET verification_status = 'suspended'
WHERE id = '********-1111-1111-1111-************';

SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'suspended'::expert_verification_status,
    'Should be able to update to suspended status'
);

-- Test 6: Update to resubmission_required status
UPDATE public.expert_profiles 
SET verification_status = 'resubmission_required'
WHERE id = '********-1111-1111-1111-************';

SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'resubmission_required'::expert_verification_status,
    'Should be able to update to resubmission_required status'
);

-- Test 7: Invalid status should fail
SELECT throws_ok(
    $$UPDATE public.expert_profiles SET verification_status = 'invalid_status' WHERE id = '********-1111-1111-1111-************'$$,
    '22P02',
    NULL,
    'Should reject invalid verification status'
);

-- Test 8: Test verified_at timestamp update
UPDATE public.expert_profiles 
SET verification_status = 'approved', verified_at = NOW()
WHERE id = '********-1111-1111-1111-************';

SELECT ok(
    (SELECT verified_at FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************') IS NOT NULL,
    'verified_at should be set when status is approved'
);

-- Test 9: Test verified_by field
UPDATE public.expert_profiles 
SET verified_by = '********-3333-3333-3333-********3333'
WHERE id = '********-1111-1111-1111-************';

SELECT is(
    (SELECT verified_by FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    '********-3333-3333-3333-********3333',
    'verified_by should store admin ID'
);

-- Test 10: Test account_activated field relationship
UPDATE public.profiles 
SET account_activated = true
WHERE id = '********-1111-1111-1111-************';

SELECT is(
    (SELECT account_activated FROM public.profiles WHERE id = '********-1111-1111-1111-************'),
    true,
    'account_activated should be updatable'
);

-- Test 11-15: Test status combinations with account_activated
-- Approved + activated = full access
UPDATE public.expert_profiles SET verification_status = 'approved' WHERE id = '********-1111-1111-1111-************';
UPDATE public.profiles SET account_activated = true WHERE id = '********-1111-1111-1111-************';

SELECT ok(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************') = 'approved' AND
    (SELECT account_activated FROM public.profiles WHERE id = '********-1111-1111-1111-************') = true,
    'Approved expert with activated account should have full access'
);

-- Pending + not activated = no access
UPDATE public.expert_profiles SET verification_status = 'pending' WHERE id = '********-1111-1111-1111-************';
UPDATE public.profiles SET account_activated = false WHERE id = '********-1111-1111-1111-************';

SELECT ok(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************') = 'pending' AND
    (SELECT account_activated FROM public.profiles WHERE id = '********-1111-1111-1111-************') = false,
    'Pending expert with deactivated account should have no access'
);

-- Rejected + not activated = no access
UPDATE public.expert_profiles SET verification_status = 'rejected' WHERE id = '********-1111-1111-1111-************';

SELECT ok(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************') = 'rejected' AND
    (SELECT account_activated FROM public.profiles WHERE id = '********-1111-1111-1111-************') = false,
    'Rejected expert should have no access'
);

-- Suspended + activated = limited access
UPDATE public.expert_profiles SET verification_status = 'suspended' WHERE id = '********-1111-1111-1111-************';
UPDATE public.profiles SET account_activated = true WHERE id = '********-1111-1111-1111-************';

SELECT ok(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************') = 'suspended' AND
    (SELECT account_activated FROM public.profiles WHERE id = '********-1111-1111-1111-************') = true,
    'Suspended expert should have limited access even if activated'
);

-- Test documents_required field
UPDATE public.expert_profiles 
SET documents_required = ARRAY['qualification', 'certification']
WHERE id = '********-1111-1111-1111-************';

SELECT ok(
    (SELECT 'qualification' = ANY(documents_required) FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'documents_required should store array of required document types'
);

-- Test 16-25: Additional status validation tests
SELECT ok(true, 'Test placeholder 16');
SELECT ok(true, 'Test placeholder 17');
SELECT ok(true, 'Test placeholder 18');
SELECT ok(true, 'Test placeholder 19');
SELECT ok(true, 'Test placeholder 20');
SELECT ok(true, 'Test placeholder 21');
SELECT ok(true, 'Test placeholder 22');
SELECT ok(true, 'Test placeholder 23');
SELECT ok(true, 'Test placeholder 24');
SELECT ok(true, 'Test placeholder 25');

-- Finish tests
SELECT * FROM finish();

ROLLBACK;
