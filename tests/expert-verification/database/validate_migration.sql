-- Expert Verification Migration Validation Script
-- This script validates that the expert verification migration has been applied correctly
-- File: tests/expert-verification/database/validate_migration.sql

\echo '🔍 Validating Expert Verification Migration'
\echo '==========================================='

-- Check 1: Verify expert_verification_status enum exists
\echo ''
\echo '1. Checking expert_verification_status enum...'
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM pg_type WHERE typname = 'expert_verification_status') 
        THEN '✅ expert_verification_status enum exists'
        ELSE '❌ expert_verification_status enum NOT found'
    END as enum_status;

-- Check 2: Verify enum values
\echo ''
\echo '2. Checking enum values...'
SELECT enumlabel as enum_values 
FROM pg_enum 
WHERE enumtypid = 'expert_verification_status'::regtype
ORDER BY enumsortorder;

-- Check 3: Verify expert_documents table exists
\echo ''
\echo '3. Checking expert_documents table...'
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expert_documents') 
        THEN '✅ expert_documents table exists'
        ELSE '❌ expert_documents table NOT found'
    END as table_status;

-- Check 4: Verify expert_verification_history table exists
\echo ''
\echo '4. Checking expert_verification_history table...'
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expert_verification_history') 
        THEN '✅ expert_verification_history table exists'
        ELSE '❌ expert_verification_history table NOT found'
    END as table_status;

-- Check 5: Verify expert_profiles table has correct verification_status column
\echo ''
\echo '5. Checking expert_profiles.verification_status column...'
SELECT 
    column_name,
    data_type,
    udt_name,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'expert_profiles' 
AND column_name = 'verification_status';

-- Check 6: Verify database functions exist
\echo ''
\echo '6. Checking expert verification functions...'
SELECT 
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%expert_verification%'
ORDER BY routine_name;

-- Check 7: Test basic function accessibility
\echo ''
\echo '7. Testing function accessibility...'

-- Test check_expert_consultation_access function
SELECT 
    CASE 
        WHEN public.check_expert_consultation_access('00000000-0000-0000-0000-000000000000') IS NOT NULL
        THEN '✅ check_expert_consultation_access function works'
        ELSE '❌ check_expert_consultation_access function failed'
    END as function_test;

-- Test get_experts_pending_verification function
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM public.get_experts_pending_verification()) >= 0
        THEN '✅ get_experts_pending_verification function works'
        ELSE '❌ get_experts_pending_verification function failed'
    END as function_test;

-- Check 8: Verify RLS policies exist
\echo ''
\echo '8. Checking RLS policies...'
SELECT 
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename IN ('expert_documents', 'expert_verification_history', 'expert_profiles')
ORDER BY tablename, policyname;

-- Check 9: Verify indexes exist for performance
\echo ''
\echo '9. Checking performance indexes...'
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('expert_documents', 'expert_verification_history', 'expert_profiles')
AND indexname LIKE '%expert%'
ORDER BY tablename, indexname;

-- Check 10: Sample data validation
\echo ''
\echo '10. Checking current data compatibility...'
SELECT 
    verification_status,
    COUNT(*) as count
FROM public.expert_profiles 
GROUP BY verification_status
ORDER BY verification_status;

\echo ''
\echo '🎯 Migration Validation Summary'
\echo '==============================='
\echo 'If all checks show ✅, the migration was applied successfully.'
\echo 'If any checks show ❌, please run the migration script:'
\echo '  .database/migrations/expert_verification_functions_safe.sql'
\echo ''
