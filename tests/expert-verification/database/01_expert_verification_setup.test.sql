-- Expert Verification Setup Tests
-- Tests database schema setup and initial configuration
-- File: tests/expert-verification/database/01_expert_verification_setup.test.sql

BEGIN;

-- Load pgTAP extension
SELECT plan(13);

-- Test 1: Check if expert_verification_status enum exists
SELECT has_type(
    'public',
    'expert_verification_status',
    'expert_verification_status enum type should exist'
);

-- Test 2: Check enum values
SELECT enum_has_labels(
    'public',
    'expert_verification_status',
    ARRAY['pending', 'under_review', 'approved', 'rejected', 'suspended', 'resubmission_required'],
    'expert_verification_status should have correct enum values'
);

-- Test 3: Check if expert_documents table exists
SELECT has_table(
    'public',
    'expert_documents',
    'expert_documents table should exist'
);

-- Test 4: Check expert_documents table structure
SELECT has_column('public', 'expert_documents', 'id', 'expert_documents should have id column');
SELECT has_column('public', 'expert_documents', 'expert_id', 'expert_documents should have expert_id column');
SELECT has_column('public', 'expert_documents', 'document_type', 'expert_documents should have document_type column');
SELECT has_column('public', 'expert_documents', 'document_url', 'expert_documents should have document_url column');
SELECT has_column('public', 'expert_documents', 'document_name', 'expert_documents should have document_name column');
SELECT has_column('public', 'expert_documents', 'file_size', 'expert_documents should have file_size column');
SELECT has_column('public', 'expert_documents', 'mime_type', 'expert_documents should have mime_type column');
SELECT has_column('public', 'expert_documents', 'uploaded_at', 'expert_documents should have uploaded_at column');

-- Test 5: Check if expert_verification_history table exists
SELECT has_table(
    'public',
    'expert_verification_history',
    'expert_verification_history table should exist'
);

-- Test 6: Check expert_verification_history table structure
SELECT has_column('public', 'expert_verification_history', 'id', 'expert_verification_history should have id column');
SELECT has_column('public', 'expert_verification_history', 'expert_id', 'expert_verification_history should have expert_id column');
SELECT has_column('public', 'expert_verification_history', 'status', 'expert_verification_history should have status column');
SELECT has_column('public', 'expert_verification_history', 'admin_notes', 'expert_verification_history should have admin_notes column');

-- Test 7: Check if expert_profiles table has verification_status column with correct type
SELECT col_type_is(
    'public',
    'expert_profiles',
    'verification_status',
    'expert_verification_status',
    'expert_profiles.verification_status should be expert_verification_status type'
);

-- Finish tests
SELECT * FROM finish();

ROLLBACK;
