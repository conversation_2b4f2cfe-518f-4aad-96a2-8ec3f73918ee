-- Expert Verification Database Test Runner
-- Runs all expert verification database tests in sequence
-- File: tests/expert-verification/database/run_all_tests.sql

-- This file should be executed in Supabase SQL Editor or via Supabase CLI
-- Usage: supabase test db --file="tests/expert-verification/database/run_all_tests.sql"

\echo '🧪 Starting Expert Verification Database Tests'
\echo '=============================================='

-- Test 1: Setup and Schema Tests
\echo ''
\echo '📋 Running Test 1: Expert Verification Setup Tests'
\echo '---------------------------------------------------'
\i tests/expert-verification/database/01_expert_verification_setup.test.sql

-- Test 2: CRUD Operations Tests
\echo ''
\echo '📝 Running Test 2: Expert Documents CRUD Tests'
\echo '-----------------------------------------------'
\i tests/expert-verification/database/02_expert_documents_crud.test.sql

-- Test 3: Status Management Tests
\echo ''
\echo '🔄 Running Test 3: Expert Verification Status Tests'
\echo '---------------------------------------------------'
\i tests/expert-verification/database/03_expert_verification_status.test.sql

-- Test 4: History Tracking Tests
\echo ''
\echo '📚 Running Test 4: Expert Verification History Tests'
\echo '----------------------------------------------------'
\i tests/expert-verification/database/04_expert_verification_history.test.sql

-- Test 5: Database Functions Tests
\echo ''
\echo '⚙️  Running Test 5: Expert Verification Functions Tests'
\echo '-------------------------------------------------------'
\i tests/expert-verification/database/05_expert_verification_functions.test.sql

-- Test 6: Row Level Security Tests
\echo ''
\echo '🔒 Running Test 6: Expert Verification RLS Tests'
\echo '-------------------------------------------------'
\i tests/expert-verification/database/06_expert_verification_rls.test.sql

-- Test 7: Integration Tests
\echo ''
\echo '🔗 Running Test 7: Expert Verification Integration Tests'
\echo '--------------------------------------------------------'
\i tests/expert-verification/database/07_expert_verification_integration.test.sql

\echo ''
\echo '✅ Expert Verification Database Tests Complete'
\echo '=============================================='
\echo ''
\echo 'Summary:'
\echo '- Setup and Schema: ✓'
\echo '- CRUD Operations: ✓'
\echo '- Status Management: ✓'
\echo '- History Tracking: ✓'
\echo '- Database Functions: ✓'
\echo '- Row Level Security: ✓'
\echo '- Integration Workflows: ✓'
\echo ''
\echo 'All expert verification database tests completed successfully!'
\echo 'Next steps:'
\echo '1. Run backend API tests: npm test tests/expert-verification/backend/'
\echo '2. Run frontend component tests: npm test tests/expert-verification/frontend/'
\echo '3. Run E2E tests: npx playwright test tests/expert-verification/e2e/'
\echo ''
