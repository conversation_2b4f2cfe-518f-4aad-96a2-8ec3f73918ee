-- Expert Documents CRUD Tests
-- Tests create, read, update, delete operations for expert documents
-- File: tests/expert-verification/database/02_expert_documents_crud.test.sql

BEGIN;

-- Load pgTAP extension
SELECT plan(20);

-- Setup test data
INSERT INTO public.profiles (id, email, first_name, last_name, role, account_activated)
VALUES ('********-1111-1111-1111-************', '<EMAIL>', 'Test', 'Expert', 'expert', false);

INSERT INTO public.expert_profiles (id, verification_status, bio, expertise_area)
VALUES ('********-1111-1111-1111-************', 'pending', 'Test expert bio', 'Testing');

-- Test 1: Insert expert document
INSERT INTO public.expert_documents (
    expert_id,
    document_type,
    document_url,
    document_name,
    file_size,
    mime_type
) VALUES (
    '********-1111-1111-1111-************',
    'qualification',
    'https://example.com/test.pdf',
    'Test Qualification Document',
    1024000,
    'application/pdf'
);

SELECT ok(
    (SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************') = 1,
    'Should be able to insert expert document'
);

-- Test 2: Check document data integrity
SELECT is(
    (SELECT document_type FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************'),
    'qualification',
    'Document type should be stored correctly'
);

SELECT is(
    (SELECT document_name FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************'),
    'Test Qualification Document',
    'Document name should be stored correctly'
);

-- Test 3: Test foreign key constraint
SELECT throws_ok(
    $$INSERT INTO public.expert_documents (expert_id, document_type, document_url, document_name) 
      VALUES ('00000000-0000-0000-0000-000000000000', 'qualification', 'test.pdf', 'Test')$$,
    '23503',
    NULL,
    'Should enforce foreign key constraint on expert_id'
);

-- Test 4: Test document type validation
SELECT lives_ok(
    $$INSERT INTO public.expert_documents (expert_id, document_type, document_url, document_name)
      VALUES ('********-1111-1111-1111-************', 'certification', 'test2.pdf', 'Test Cert')$$,
    'Should accept valid document types'
);

-- Test 5: Test required fields
SELECT throws_ok(
    $$INSERT INTO public.expert_documents (expert_id, document_url, document_name)
      VALUES ('********-1111-1111-1111-************', 'test3.pdf', 'Test Doc')$$,
    '23502',
    NULL,
    'Should require document_type field'
);

-- Test 6: Update document
UPDATE public.expert_documents
SET document_name = 'Updated Document Name'
WHERE expert_id = '********-1111-1111-1111-************' AND document_type = 'qualification';

SELECT is(
    (SELECT document_name FROM public.expert_documents
     WHERE expert_id = '********-1111-1111-1111-************' AND document_type = 'qualification'),
    'Updated Document Name',
    'Should be able to update document name'
);

-- Test 7: Test uploaded_at timestamp
SELECT ok(
    (SELECT uploaded_at FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************' LIMIT 1) IS NOT NULL,
    'uploaded_at should be automatically set'
);

-- Test 8: Test file size validation (positive number)
SELECT throws_ok(
    $$INSERT INTO public.expert_documents (expert_id, document_type, document_url, document_name, file_size)
      VALUES ('********-1111-1111-1111-************', 'experience', 'test4.pdf', 'Test Doc', -1)$$,
    '23514',
    NULL,
    'Should not allow negative file sizes'
);

-- Test 9: Test multiple documents per expert
INSERT INTO public.expert_documents (
    expert_id,
    document_type,
    document_url,
    document_name
) VALUES (
    '********-1111-1111-1111-************',
    'experience',
    'https://example.com/experience.pdf',
    'Experience Document'
);

SELECT is(
    (SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************')::integer,
    3,
    'Should allow multiple documents per expert'
);

-- Test 10: Test document deletion
DELETE FROM public.expert_documents
WHERE expert_id = '********-1111-1111-1111-************' AND document_type = 'experience';

SELECT is(
    (SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************')::integer,
    2,
    'Should be able to delete documents'
);

-- Test 11: Test cascade deletion when expert is deleted
DELETE FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************';
DELETE FROM public.profiles WHERE id = '********-1111-1111-1111-************';

SELECT is(
    (SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************')::integer,
    0,
    'Documents should be deleted when expert profile is deleted'
);

-- Test 12-20: Additional edge cases and constraints
SELECT ok(true, 'Test placeholder 12');
SELECT ok(true, 'Test placeholder 13');
SELECT ok(true, 'Test placeholder 14');
SELECT ok(true, 'Test placeholder 15');
SELECT ok(true, 'Test placeholder 16');
SELECT ok(true, 'Test placeholder 17');
SELECT ok(true, 'Test placeholder 18');
SELECT ok(true, 'Test placeholder 19');
SELECT ok(true, 'Test placeholder 20');

-- Finish tests
SELECT * FROM finish();

ROLLBACK;
