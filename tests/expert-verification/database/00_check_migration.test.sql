-- Check Migration Status Test
-- This test checks if the expert verification migration has been applied
-- File: tests/expert-verification/database/00_check_migration.test.sql

BEGIN;

-- Load pgTAP extension
SELECT plan(8);

-- Test 1: Check if expert_verification_status enum exists
SELECT has_type(
    'public',
    'expert_verification_status',
    'expert_verification_status enum type should exist'
);

-- Test 2: Check if expert_documents table exists
SELECT has_table(
    'public',
    'expert_documents',
    'expert_documents table should exist'
);

-- Test 3: Check if expert_verification_history table exists
SELECT has_table(
    'public',
    'expert_verification_history',
    'expert_verification_history table should exist'
);

-- Test 4: Check if expert_profiles has verification_status column
SELECT has_column(
    'public',
    'expert_profiles',
    'verification_status',
    'expert_profiles should have verification_status column'
);

-- Test 5: Check if update_expert_verification_status function exists
SELECT has_function(
    'public',
    'update_expert_verification_status',
    'update_expert_verification_status function should exist'
);

-- Test 6: Check if check_expert_consultation_access function exists
SELECT has_function(
    'public',
    'check_expert_consultation_access',
    'check_expert_consultation_access function should exist'
);

-- Test 7: Check if get_experts_pending_verification function exists
SELECT has_function(
    'public',
    'get_experts_pending_verification',
    'get_experts_pending_verification function should exist'
);

-- Test 8: Check if get_expert_verification_details function exists
SELECT has_function(
    'public',
    'get_expert_verification_details',
    'get_expert_verification_details function should exist'
);

-- Finish tests
SELECT * FROM finish();

ROLLBACK;
