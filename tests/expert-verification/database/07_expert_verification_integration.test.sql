-- Expert Verification Integration Tests
-- Tests end-to-end integration workflows
-- File: tests/expert-verification/database/07_expert_verification_integration.test.sql

BEGIN;

-- Load pgTAP extension
SELECT plan(30);

-- Setup test data
INSERT INTO public.profiles (id, email, first_name, last_name, role, account_activated)
VALUES 
    ('********-1111-1111-1111-************', '<EMAIL>', 'Test', 'Expert1', 'expert', false),
    ('********-3333-3333-3333-********3333', '<EMAIL>', 'Test', 'Admin', 'admin', true);

INSERT INTO public.expert_profiles (id, verification_status, bio, expertise_area)
VALUES ('********-1111-1111-1111-************', 'pending', 'Test expert bio', 'Testing');

-- Test 1-5: Complete Expert Registration and Verification Workflow
-- Step 1: Expert registers (profile created with pending status)
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'pending'::expert_verification_status,
    'New expert should start with pending status'
);

SELECT is(
    (SELECT account_activated FROM public.profiles WHERE id = '********-1111-1111-1111-************'),
    false,
    'New expert account should not be activated'
);

-- Step 2: Expert uploads documents
SELECT lives_ok(
    $$SELECT public.upload_expert_verification_document(
        'qualification',
        'https://example.com/qualification.pdf',
        'University Degree',
        2048000,
        'application/pdf'
    )$$,
    'Expert should be able to upload qualification document'
);

SELECT lives_ok(
    $$SELECT public.upload_expert_verification_document(
        'certification',
        'https://example.com/certification.pdf',
        'Professional Certification',
        1024000,
        'application/pdf'
    )$$,
    'Expert should be able to upload certification document'
);

-- Verify documents were uploaded
SELECT is(
    (SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************')::integer,
    2,
    'Expert should have uploaded 2 documents'
);

-- Test 6-10: Admin Review Process
-- Step 3: Admin starts review process
SELECT lives_ok(
    $$SELECT public.update_expert_verification_status(
        '********-1111-1111-1111-************'::uuid,
        'under_review'::expert_verification_status,
        'Starting document review process',
        NULL
    )$$,
    'Admin should be able to start review process'
);

-- Verify status change and history
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'under_review'::expert_verification_status,
    'Expert status should be updated to under_review'
);

SELECT ok(
    (SELECT COUNT(*) FROM public.expert_verification_history 
     WHERE expert_id = '********-1111-1111-1111-************' AND status = 'under_review') = 1,
    'History entry should be created for status change'
);

-- Step 4: Admin requests additional documents
SELECT lives_ok(
    $$SELECT public.update_expert_verification_status(
        '********-1111-1111-1111-************'::uuid,
        'resubmission_required'::expert_verification_status,
        'Need additional experience documentation',
        ARRAY['Missing work experience certificate', 'Need portfolio examples']
    )$$,
    'Admin should be able to request resubmission'
);

-- Verify resubmission status
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'resubmission_required'::expert_verification_status,
    'Expert status should be updated to resubmission_required'
);

-- Step 5: Expert resubmits additional documents
SELECT lives_ok(
    $$SELECT public.upload_expert_verification_document(
        'experience',
        'https://example.com/experience.pdf',
        'Work Experience Certificate',
        1536000,
        'application/pdf'
    )$$,
    'Expert should be able to upload additional documents'
);

-- Test 11-15: Final Approval Process
-- Step 6: Admin reviews resubmitted documents
SELECT lives_ok(
    $$SELECT public.update_expert_verification_status(
        '********-1111-1111-1111-************'::uuid,
        'under_review'::expert_verification_status,
        'Reviewing resubmitted documents',
        NULL
    )$$,
    'Admin should be able to restart review after resubmission'
);

-- Step 7: Admin approves expert
SELECT lives_ok(
    $$SELECT public.update_expert_verification_status(
        '********-1111-1111-1111-************'::uuid,
        'approved'::expert_verification_status,
        'All requirements met. Expert approved.',
        NULL
    )$$,
    'Admin should be able to approve expert'
);

-- Verify final approval
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************'),
    'approved'::expert_verification_status,
    'Expert status should be approved'
);

SELECT is(
    (SELECT account_activated FROM public.profiles WHERE id = '********-1111-1111-1111-************'),
    true,
    'Expert account should be activated after approval'
);

-- Verify consultation access
SELECT is(
    public.check_expert_consultation_access('********-1111-1111-1111-************'),
    true,
    'Approved expert should have consultation access'
);

-- Test 16-20: Rejection and Suspension Workflows
-- Create another expert for rejection testing
INSERT INTO public.profiles (id, email, first_name, last_name, role, account_activated)
VALUES ('*************-2222-2222-************', '<EMAIL>', 'Test', 'Expert2', 'expert', false);

INSERT INTO public.expert_profiles (id, verification_status, bio, expertise_area)
VALUES ('*************-2222-2222-************', 'pending', 'Test expert bio 2', 'Testing');

-- Test rejection workflow
SELECT lives_ok(
    $$SELECT public.update_expert_verification_status(
        '*************-2222-2222-************'::uuid,
        'rejected'::expert_verification_status,
        'Insufficient qualifications',
        ARRAY['Qualifications do not meet requirements', 'Experience level too low']
    )$$,
    'Admin should be able to reject expert'
);

-- Verify rejection
SELECT is(
    (SELECT verification_status FROM public.expert_profiles WHERE id = '*************-2222-2222-************'),
    'rejected'::expert_verification_status,
    'Expert status should be rejected'
);

SELECT is(
    (SELECT account_activated FROM public.profiles WHERE id = '*************-2222-2222-************'),
    false,
    'Rejected expert account should remain deactivated'
);

-- Verify no consultation access
SELECT is(
    public.check_expert_consultation_access('*************-2222-2222-************'),
    false,
    'Rejected expert should not have consultation access'
);

-- Test suspension workflow
SELECT lives_ok(
    $$SELECT public.update_expert_verification_status(
        '********-1111-1111-1111-************'::uuid,
        'suspended'::expert_verification_status,
        'Temporary suspension due to quality issues',
        NULL
    )$$,
    'Admin should be able to suspend expert'
);

-- Verify suspension blocks access
SELECT is(
    public.check_expert_consultation_access('********-1111-1111-1111-************'),
    false,
    'Suspended expert should not have consultation access'
);

-- Test 21-25: Data Integrity and Constraint Validation
-- Test complete verification history tracking
SELECT ok(
    (SELECT COUNT(*) FROM public.expert_verification_history WHERE expert_id = '********-1111-1111-1111-************') >= 5,
    'Complete verification workflow should be tracked in history'
);

-- Test document count validation
SELECT is(
    (SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************')::integer,
    3,
    'Expert should have all uploaded documents'
);

-- Test foreign key integrity
SELECT throws_ok(
    $$DELETE FROM public.profiles WHERE id = '********-1111-1111-1111-************'$$,
    '23503',
    NULL,
    'Should not be able to delete profile with dependent expert_profile'
);

-- Test cascade deletion
DELETE FROM public.expert_profiles WHERE id = '********-1111-1111-1111-************';
DELETE FROM public.profiles WHERE id = '********-1111-1111-1111-************';

SELECT is(
    (SELECT COUNT(*) FROM public.expert_documents WHERE expert_id = '********-1111-1111-1111-************')::integer,
    0,
    'Documents should be deleted when expert profile is deleted'
);

SELECT is(
    (SELECT COUNT(*) FROM public.expert_verification_history WHERE expert_id = '********-1111-1111-1111-************')::integer,
    0,
    'History should be deleted when expert profile is deleted'
);

-- Test 26-30: Performance and Edge Cases
SELECT ok(true, 'Performance test placeholder 26');
SELECT ok(true, 'Edge case test placeholder 27');
SELECT ok(true, 'Concurrency test placeholder 28');
SELECT ok(true, 'Data validation test placeholder 29');
SELECT ok(true, 'Integration test placeholder 30');

-- Finish tests
SELECT * FROM finish();

ROLLBACK;
