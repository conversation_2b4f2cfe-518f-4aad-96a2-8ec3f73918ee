import { test, expect } from '@playwright/test';

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const TEST_ADMIN = {
  email: '<EMAIL>',
  password: 'admin123!',
};
const TEST_EXPERT = {
  email: '<EMAIL>',
  password: 'expert123!',
  firstName: 'Dr. <PERSON>',
  lastName: '<PERSON><PERSON><PERSON>',
  expertise: 'Organic Farming',
  experience: '15',
};

test.describe('Expert Verification End-to-End Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Reset database state before each test
    // In a real scenario, you might call a database reset endpoint
    await page.goto(`${BASE_URL}/api/test/reset-db`, { waitUntil: 'networkidle' });
  });

  test.describe('Expert Registration and Document Upload', () => {
    test('expert can register and upload verification documents', async ({ page }) => {
      // Navigate to sign up page
      await page.goto(`${BASE_URL}/sign-up`);
      
      // Fill out expert registration form
      await page.fill('#email', TEST_EXPERT.email);
      await page.fill('#password', TEST_EXPERT.password);
      await page.fill('#firstName', TEST_EXPERT.firstName);
      await page.fill('#lastName', TEST_EXPERT.lastName);
      await page.selectOption('#role', 'expert');
      await page.fill('#expertise', TEST_EXPERT.expertise);
      await page.fill('#experience', TEST_EXPERT.experience);
      
      // Submit registration
      await page.click('button[type="submit"]');
      
      // Should redirect to verification pending page
      await expect(page).toHaveURL(/verification-pending/);
      await expect(page.getByText('Your account is pending verification')).toBeVisible();
      
      // Check verification status
      await expect(page.getByTestId('verification-status')).toHaveText('Pending');
      
      // Upload qualification document
      await page.click('[data-testid="upload-qualification"]');
      await page.setInputFiles('#qualification-upload', 'tests/fixtures/phd-certificate.pdf');
      await page.fill('#document-name', 'PhD Certificate in Agricultural Sciences');
      await page.click('button[type="submit"]');
      
      // Verify document was uploaded
      await expect(page.getByText('PhD Certificate in Agricultural Sciences')).toBeVisible();
      await expect(page.getByTestId('document-status')).toHaveText('Pending Review');
      
      // Upload certification document
      await page.click('[data-testid="upload-certification"]');
      await page.setInputFiles('#certification-upload', 'tests/fixtures/organic-cert.pdf');
      await page.fill('#document-name', 'Certified Organic Inspector Certificate');
      await page.click('button[type="submit"]');
      
      // Upload experience document
      await page.click('[data-testid="upload-experience"]');
      await page.setInputFiles('#experience-upload', 'tests/fixtures/experience-letter.pdf');
      await page.fill('#document-name', 'Experience Letter - 15 Years');
      await page.click('button[type="submit"]');
      
      // Verify all documents uploaded
      await expect(page.getByText('3 documents uploaded')).toBeVisible();
      
      // Try to access consultation features (should be blocked)
      await page.goto(`${BASE_URL}/dashboard/consultations`);
      await expect(page.getByText('Verification required')).toBeVisible();
      await expect(page.getByText('complete your verification')).toBeVisible();
    });

    test('expert cannot access consultation features when unverified', async ({ page }) => {
      // Create unverified expert account directly
      await page.goto(`${BASE_URL}/api/test/create-expert`, {
        method: 'POST',
        data: { ...TEST_EXPERT, verified: false },
      });
      
      // Login as expert
      await page.goto(`${BASE_URL}/sign-in`);
      await page.fill('#email', TEST_EXPERT.email);
      await page.fill('#password', TEST_EXPERT.password);
      await page.click('button[type="submit"]');
      
      // Should be redirected to verification pending
      await expect(page).toHaveURL(/verification-pending/);
      
      // Try direct navigation to consultation pages
      const restrictedPaths = [
        '/dashboard/consultations',
        '/dashboard/requests',
        '/dashboard/consultations/new',
      ];
      
      for (const path of restrictedPaths) {
        await page.goto(`${BASE_URL}${path}`);
        await expect(page.getByText(/verification required|access denied/i)).toBeVisible();
      }
    });
  });

  test.describe('Admin Verification Process', () => {
    test('admin can review and approve expert verification', async ({ page }) => {
      // Setup: Create expert with uploaded documents
      await page.goto(`${BASE_URL}/api/test/create-expert-with-docs`, {
        method: 'POST',
        data: TEST_EXPERT,
      });
      
      // Login as admin
      await page.goto(`${BASE_URL}/sign-in`);
      await page.fill('#email', TEST_ADMIN.email);
      await page.fill('#password', TEST_ADMIN.password);
      await page.click('button[type="submit"]');
      
      // Navigate to admin dashboard
      await expect(page).toHaveURL(/dashboard/);
      await expect(page.getByText('Admin Dashboard')).toBeVisible();
      
      // Go to expert verification queue
      await page.click('[data-testid="expert-verification-nav"]');
      await expect(page).toHaveURL(/users.*verification/);
      
      // Should see pending expert in queue
      await expect(page.getByText('Dr. Ahmed Al-Rashid')).toBeVisible();
      await expect(page.getByText('Organic Farming')).toBeVisible();
      await expect(page.getByTestId('verification-status')).toHaveText('Pending');
      
      // Click to review expert
      await page.click('[data-testid="review-expert"]');
      
      // Should be on expert review page
      await expect(page).toHaveURL(/users\/.*\/review/);
      await expect(page.getByText('Expert Document Review')).toBeVisible();
      
      // Review expert profile information
      await expect(page.getByText('Dr. Ahmed Al-Rashid')).toBeVisible();
      await expect(page.getByText('Organic Farming')).toBeVisible();
      await expect(page.getByText('15 years')).toBeVisible();
      
      // Review uploaded documents
      await expect(page.getByText('Verification Documents (3)')).toBeVisible();
      await expect(page.getByText('PhD Certificate in Agricultural Sciences')).toBeVisible();
      await expect(page.getByText('Certified Organic Inspector Certificate')).toBeVisible();
      await expect(page.getByText('Experience Letter - 15 Years')).toBeVisible();
      
      // View a document
      await page.click('[data-testid="view-document-1"]');
      await expect(page.getByTestId('document-viewer')).toBeVisible();
      await page.click('[data-testid="close-viewer"]');
      
      // Approve the expert
      await page.fill('[data-testid="admin-notes"]', 'All documents verified. Expert qualifications meet platform standards. PhD in Agricultural Sciences confirmed.');
      await page.click('[data-testid="approve-expert"]');
      
      // Confirmation dialog
      await expect(page.getByText('Approve Expert Verification?')).toBeVisible();
      await page.click('[data-testid="confirm-approval"]');
      
      // Should see success message
      await expect(page.getByText('Expert approved successfully')).toBeVisible();
      
      // Should be redirected back to verification queue
      await expect(page).toHaveURL(/users.*verification/);
      
      // Expert should no longer appear in pending queue
      await expect(page.getByText('Dr. Ahmed Al-Rashid')).not.toBeVisible();
      
      // Check approved experts section
      await page.click('[data-testid="approved-experts-tab"]');
      await expect(page.getByText('Dr. Ahmed Al-Rashid')).toBeVisible();
      await expect(page.getByTestId('verification-status')).toHaveText('Approved');
    });

    test('admin can reject expert verification with reasons', async ({ page }) => {
      // Setup: Create expert with incomplete documents
      await page.goto(`${BASE_URL}/api/test/create-expert-incomplete`, {
        method: 'POST',
        data: { ...TEST_EXPERT, email: '<EMAIL>' },
      });
      
      // Login as admin
      await page.goto(`${BASE_URL}/sign-in`);
      await page.fill('#email', TEST_ADMIN.email);
      await page.fill('#password', TEST_ADMIN.password);
      await page.click('button[type="submit"]');
      
      // Navigate to verification queue
      await page.goto(`${BASE_URL}/dashboard/users?tab=verification`);
      
      // Review the expert
      await page.click('[data-testid="review-expert"]');
      
      // Reject the expert
      await page.click('[data-testid="reject-expert"]');
      
      // Fill rejection form
      await page.fill('[data-testid="admin-notes"]', 'Missing required experience documentation and professional certifications.');
      await page.check('[data-testid="reason-missing-docs"]');
      await page.check('[data-testid="reason-insufficient-experience"]');
      await page.fill('[data-testid="custom-reason"]', 'Need recommendation letters from previous employers');
      
      // Submit rejection
      await page.click('[data-testid="confirm-rejection"]');
      
      // Should see success message
      await expect(page.getByText('Expert verification rejected')).toBeVisible();
      
      // Should be back at verification queue
      await expect(page).toHaveURL(/users.*verification/);
    });

    test('admin can request resubmission', async ({ page }) => {
      // Setup rejected expert
      await page.goto(`${BASE_URL}/api/test/create-expert-rejected`, {
        method: 'POST',
        data: { ...TEST_EXPERT, email: '<EMAIL>' },
      });
      
      // Login as admin and navigate to rejected experts
      await page.goto(`${BASE_URL}/sign-in`);
      await page.fill('#email', TEST_ADMIN.email);
      await page.fill('#password', TEST_ADMIN.password);
      await page.click('button[type="submit"]');
      
      await page.goto(`${BASE_URL}/dashboard/users?tab=rejected`);
      
      // Review rejected expert
      await page.click('[data-testid="review-expert"]');
      
      // Request resubmission
      await page.click('[data-testid="request-resubmission"]');
      
      // Fill resubmission form
      await page.fill('[data-testid="admin-notes"]', 'Please provide the missing documentation and resubmit for review.');
      await page.check('[data-testid="required-doc-experience"]');
      await page.check('[data-testid="required-doc-recommendations"]');
      
      // Submit resubmission request
      await page.click('[data-testid="confirm-resubmission"]');
      
      // Should see success message
      await expect(page.getByText('Resubmission requested')).toBeVisible();
    });
  });

  test.describe('Expert Verification Status Updates', () => {
    test('expert receives real-time status updates', async ({ page }) => {
      // Create approved expert
      await page.goto(`${BASE_URL}/api/test/create-expert-approved`, {
        method: 'POST',
        data: TEST_EXPERT,
      });
      
      // Login as expert
      await page.goto(`${BASE_URL}/sign-in`);
      await page.fill('#email', TEST_EXPERT.email);
      await page.fill('#password', TEST_EXPERT.password);
      await page.click('button[type="submit"]');
      
      // Should be redirected to dashboard (not verification pending)
      await expect(page).toHaveURL(/dashboard/);
      await expect(page.getByText('Welcome, Dr. Ahmed')).toBeVisible();
      
      // Check verification status in profile
      await page.click('[data-testid="user-menu"]');
      await page.click('[data-testid="my-profile"]');
      
      await expect(page.getByTestId('verification-status')).toHaveText('Verified');
      await expect(page.getByTestId('verified-badge')).toBeVisible();
      
      // Should have access to consultation features
      await page.goto(`${BASE_URL}/dashboard/consultations`);
      await expect(page.getByText('My Consultations')).toBeVisible();
      await expect(page.getByText('verification required')).not.toBeVisible();
    });

    test('expert can view verification history', async ({ page }) => {
      // Create expert with history
      await page.goto(`${BASE_URL}/api/test/create-expert-with-history`, {
        method: 'POST',
        data: TEST_EXPERT,
      });
      
      // Login as expert
      await page.goto(`${BASE_URL}/sign-in`);
      await page.fill('#email', TEST_EXPERT.email);
      await page.fill('#password', TEST_EXPERT.password);
      await page.click('button[type="submit"]');
      
      // Go to verification status page
      await page.goto(`${BASE_URL}/dashboard/my-profile?tab=verification`);
      
      // Should see verification history
      await expect(page.getByText('Verification History')).toBeVisible();
      await expect(page.getByText('Status: Pending → Under Review')).toBeVisible();
      await expect(page.getByText('Status: Under Review → Approved')).toBeVisible();
      await expect(page.getByText('All documents verified')).toBeVisible();
    });
  });

  test.describe('Email Notifications', () => {
    test('expert receives email when status changes', async ({ page }) => {
      // Note: This would require email testing setup (like MailHog)
      // For now, we'll verify the notification was queued
      
      // Setup expert
      await page.goto(`${BASE_URL}/api/test/create-expert-pending`, {
        method: 'POST',
        data: TEST_EXPERT,
      });
      
      // Admin approves expert
      await page.goto(`${BASE_URL}/sign-in`);
      await page.fill('#email', TEST_ADMIN.email);
      await page.fill('#password', TEST_ADMIN.password);
      await page.click('button[type="submit"]');
      
      await page.goto(`${BASE_URL}/dashboard/users?tab=verification`);
      await page.click('[data-testid="approve-expert"]');
      await page.fill('[data-testid="admin-notes"]', 'Expert approved');
      await page.click('[data-testid="confirm-approval"]');
      
      // Check that email was queued (via admin interface or API)
      await page.goto(`${BASE_URL}/dashboard/notifications`);
      await expect(page.getByText('Email <NAME_EMAIL>')).toBeVisible();
      await expect(page.getByText('Subject: Verification Approved')).toBeVisible();
    });
  });

  test.describe('Error Handling and Edge Cases', () => {
    test('handles network errors gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/**', route => route.abort());
      
      await page.goto(`${BASE_URL}/dashboard/users`);
      
      // Should show error state
      await expect(page.getByText('Failed to load experts')).toBeVisible();
      await expect(page.getByTestId('retry-button')).toBeVisible();
      
      // Restore network and retry
      await page.unroute('**/api/**');
      await page.click('[data-testid="retry-button"]');
      
      // Should load successfully
      await expect(page.getByText('Expert Verification Queue')).toBeVisible();
    });

    test('handles large file uploads', async ({ page }) => {
      // Test with large file
      await page.goto(`${BASE_URL}/dashboard/verification-pending`);
      
      await page.click('[data-testid="upload-document"]');
      await page.setInputFiles('#document-upload', 'tests/fixtures/large-document.pdf');
      
      // Should show upload progress
      await expect(page.getByTestId('upload-progress')).toBeVisible();
      
      // Should handle file size validation
      if (await page.getByText('File too large').isVisible()) {
        await expect(page.getByText('Maximum file size is 10MB')).toBeVisible();
      }
    });

    test('prevents duplicate submissions', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/users?tab=verification`);
      
      // Click approve multiple times quickly
      const approveButton = page.getByTestId('approve-expert');
      await approveButton.click();
      await approveButton.click(); // Second click should be ignored
      
      // Should only process once
      await expect(page.getByText('Expert approved successfully')).toBeVisible();
      await expect(page.getByText('Expert approved successfully')).toHaveCount(1);
    });
  });

  test.describe('Accessibility and Responsive Design', () => {
    test('keyboard navigation works correctly', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/users?tab=verification`);
      
      // Test keyboard navigation through expert cards
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter'); // Should click review button
      
      await expect(page).toHaveURL(/review/);
    });

    test('works on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await page.goto(`${BASE_URL}/dashboard/users?tab=verification`);
      
      // Should have mobile-friendly layout
      await expect(page.getByTestId('mobile-menu')).toBeVisible();
      
      // Expert cards should stack vertically
      const expertCards = page.getByTestId('expert-card');
      const firstCard = expertCards.first();
      const secondCard = expertCards.nth(1);
      
      const firstCardBox = await firstCard.boundingBox();
      const secondCardBox = await secondCard.boundingBox();
      
      // Second card should be below first card (not side by side)
      expect(secondCardBox?.y).toBeGreaterThan(firstCardBox?.y || 0);
    });
  });
});