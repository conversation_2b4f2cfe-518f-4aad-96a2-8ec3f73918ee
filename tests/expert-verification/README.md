# Expert Verification Tests

This directory contains all tests related to the Expert Verification feature, organized by testing layer.

## Structure

```
expert-verification/
├── database/           # Database-level tests (Supabase/PostgreSQL)
├── backend/           # Backend API tests
├── e2e/              # End-to-end tests
├── frontend/          # Frontend component tests
└── README.md         # This file
```

## Test Categories

### Database Tests
- **Setup Tests**: Database schema and initial data setup
- **CRUD Tests**: Create, read, update, delete operations for expert documents
- **Status Tests**: Verification status management and workflows
- **History Tests**: Audit trail and verification history tracking
- **Function Tests**: Database functions and triggers
- **RLS Tests**: Row Level Security policies
- **Integration Tests**: Full workflow integration tests

### Backend Tests
- **API Tests**: REST API endpoints for expert verification
- **Authentication Tests**: Role-based access control
- **Business Logic Tests**: Core verification algorithms

### E2E Tests
- **User Journey Tests**: Complete expert verification flows
- **Cross-browser Tests**: Compatibility across different browsers
- **Mobile Tests**: Responsive design verification

### Frontend Tests
- **Component Tests**: Individual React component testing
- **Integration Tests**: Component interaction testing
- **User Interaction Tests**: UI behavior and user experience

## Running Tests

### Database Tests
```bash
cd tests/expert-verification/database
# Run individual test files or all tests
```

### Backend Tests
```bash
cd tests/expert-verification/backend
npm test expert-verification.api.test.ts
```

### E2E Tests
```bash
cd tests/expert-verification/e2e
npx playwright test expert-verification-flow.spec.ts
```

### Frontend Tests
```bash
cd tests/expert-verification/frontend
npm test ExpertVerificationQueue.test.tsx
```

## Adding New Tests

When adding new tests for expert verification features:

1. **Identify the appropriate layer** (database, backend, e2e, frontend)
2. **Follow existing naming conventions** in each directory
3. **Update this README** if adding new subdirectories or test categories
4. **Ensure tests are isolated** and can run independently
5. **Add proper documentation** for test setup and teardown

## Test Data

- Test fixtures are located in `../fixtures/`
- Use consistent test data across all layers
- Ensure test data cleanup in teardown processes

## 📊 Expert Verification Test Coverage

### Database Layer
- ✅ Schema validation and function existence
- ✅ Document CRUD operations
- ✅ Verification status management and workflows
- ✅ Audit trail and verification history tracking
- ✅ Database functions and triggers
- ✅ Row Level Security policies
- ✅ End-to-end workflow integration tests

### Backend Layer
- ✅ API endpoints for expert verification
- ✅ Authentication and role-based access control
- ✅ Business logic validation
- ✅ Database constraint testing
- ✅ RLS policy validation
- ✅ Error handling verification

### Frontend Layer
- ✅ Admin verification queue component
- ✅ Component rendering and state management
- ✅ User interaction testing
- ✅ Accessibility validation
- ✅ Error handling scenarios

### E2E Layer
- ✅ Complete expert verification workflow
- ✅ Cross-browser compatibility testing
- ✅ Mobile responsiveness verification
- ✅ Real user scenario validation

## 🐛 Expert Verification Debugging

### Database Tests
```bash
# Check expert verification function definitions
\df public.*expert*

# Inspect expert documents table structure
\d public.expert_documents

# Check verification status table
\d public.expert_verification_status
```

### Backend Tests
```bash
# Debug specific expert verification test
node --inspect-brk node_modules/vitest/vitest.mjs run tests/expert-verification/backend/expert-verification.api.test.ts
```

### E2E Tests
```bash
# Debug expert verification flow
npm run test:e2e -- --debug tests/expert-verification/e2e/expert-verification-flow.spec.ts

# Generate trace for expert verification tests
npm run test:e2e -- --trace=on tests/expert-verification/e2e/expert-verification-flow.spec.ts
```

## 📚 Additional Resources

For expert verification specific issues, refer to:
- Database schema documentation
- API endpoint documentation
- `.database/guides/expert_verification_migration_guide.md`