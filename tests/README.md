# AgriDustria Testing Guide

This guide covers the complete testing setup for the AgriDustria web portal, including database, backend, frontend, and end-to-end testing strategies and best practices.

## 📋 Testing Overview

Our testing strategy follows a multi-layered approach to ensure comprehensive coverage:

1. **Database Testing** - SQL/pgTAP tests for database functions and constraints
2. **Backend Testing** - API and application logic testing with Vitest
3. **Frontend Testing** - Component and UI testing with React Testing Library
4. **End-to-End Testing** - Full workflow testing with <PERSON><PERSON>

## 🗂️ Test Organization

Tests are organized by feature and testing layer:

```
tests/
├── expert-verification/     # Feature-specific tests (see expert-verification/README.md)
│   ├── database/          # Database-level tests
│   ├── backend/          # API tests
│   ├── e2e/             # End-to-end tests
│   └── frontend/        # Component tests
├── e2e/                  # General E2E tests
│   ├── global-setup.ts   # Test environment setup
│   └── global-teardown.ts # Test cleanup
├── fixtures/             # Shared test data and fixtures
│   └── README.md        # Fixtures documentation
└── README.md            # This general testing guide
```

## 🛠️ Setup Instructions

### Prerequisites

- Node.js 18+
- Supabase CLI installed globally
- Local Supabase instance running

### 1. Install Dependencies

```bash
npm install -D vitest @vitest/ui jsdom @testing-library/react @testing-library/jest-dom @testing-library/user-event @supabase/supabase-js msw playwright @playwright/test
```

### 2. Environment Setup

Ensure your `.env.local` has:

```env
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Database Setup

```bash
# Start Supabase
supabase start

# Apply database migrations
supabase db push

# Install pgTAP for database testing (if not already installed)
supabase db push --file supabase/migrations/enable_pgtap.sql
```

## 🧪 Running Tests

### Database Tests

```bash
# Run all database tests
./supabase/tests/run_tests.sh

# Run specific test file
supabase test db --file="tests/expert-verification/database/01_expert_verification_setup.test.sql"

# Run with verbose output
supabase test db --verbose
```

### Backend/API Tests

```bash
# Run all backend tests
npm run test:backend

# Run with coverage
npm run test:backend -- --coverage

# Run specific test file
npm run test tests/expert-verification/backend/expert-verification.api.test.ts

# Run in watch mode
npm run test:backend -- --watch
```

### Frontend Component Tests

```bash
# Run all frontend tests
npm run test:frontend

# Run with UI
npm run test:frontend:ui

# Run specific component tests
npm run test tests/expert-verification/frontend/ExpertVerificationQueue.test.tsx

# Run in watch mode
npm run test:frontend -- --watch
```

### End-to-End Tests

```bash
# Run all E2E tests
npm run test:e2e

# Run specific browser
npm run test:e2e -- --project=chromium

# Run specific test file
npm run test:e2e tests/expert-verification/e2e/expert-verification-flow.spec.ts

# Run with UI mode
npm run test:e2e -- --ui

# Debug mode
npm run test:e2e -- --debug
```

### All Tests

```bash
# Run complete test suite
npm run test:all

# Run tests in CI mode
npm run test:ci
```

## 📚 Best Practices

### Database Testing

1. **Always use transactions with ROLLBACK** for test isolation
2. **Test both positive and negative cases** thoroughly
3. **Verify constraints and triggers** work as expected
4. **Test RLS policies** from different user perspectives
5. **Include performance considerations** in your tests

### Backend Testing

1. **Mock external dependencies** to isolate unit tests
2. **Test error conditions** and edge cases
3. **Verify authentication/authorization** logic
4. **Use realistic test data** that matches production scenarios
5. **Clean up test data** after each test

### Frontend Testing

1. **Test user interactions, not implementation details**
2. **Use semantic queries** (getByRole, getByText) for better accessibility
3. **Mock API calls consistently** with MSW (Mock Service Worker)
4. **Test error states** and loading conditions
5. **Verify accessibility** compliance

### E2E Testing

1. **Focus on critical user journeys** that impact business value
2. **Use data attributes** for reliable test selectors
3. **Wait for network idle** when testing dynamic content
4. **Test across different browsers** and viewport sizes
5. **Include mobile testing** for responsive design validation

## 🐛 Debugging Tests

### Database Tests

```bash
# Enable verbose output
supabase test db --file="test.sql" --verbose

# Check function definitions
\df

# Inspect table structure
\d table_name
```

### Backend Tests

```bash
# Run with debugging
npm run test:backend -- --reporter=verbose

# Debug specific test
node --inspect-brk node_modules/vitest/vitest.mjs run test-file.ts
```

### Frontend Tests

```bash
# Run with debugging
npm run test:frontend -- --reporter=verbose

# Debug in browser
npm run test:frontend:ui
```

### E2E Tests

```bash
# Run with headed browser for visual debugging
npm run test:e2e -- --headed

# Debug specific test
npm run test:e2e -- --debug test-file.spec.ts

# Generate trace for analysis
npm run test:e2e -- --trace=on
```

## 📈 Test Performance

### Optimization Tips

1. **Database Tests**
   - Use transactions with ROLLBACK for isolation
   - Minimize test data creation
   - Use appropriate indexes for large datasets

2. **Backend Tests**
   - Mock external dependencies to reduce setup time
   - Use service role for database access when appropriate
   - Clean up test data efficiently

3. **Frontend Tests**
   - Mock API calls with MSW to avoid network delays
   - Use shallow rendering when testing in isolation
   - Clean up DOM after tests to prevent memory leaks

4. **E2E Tests**
   - Run tests in parallel when possible
   - Use test data setup/teardown for consistent state
   - Optimize page load waits and timeouts

## 🚀 Continuous Integration

### GitHub Actions Example

```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup Supabase
        run: |
          npm install -g supabase
          supabase start

      - name: Run database tests
        run: ./supabase/tests/run_tests.sh

      - name: Run backend tests
        run: npm run test:backend

      - name: Run frontend tests
        run: npm run test:frontend

      - name: Install Playwright
        run: npx playwright install

      - name: Run E2E tests
        run: npm run test:e2e

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: test-results/
```

## 🔧 Troubleshooting

### Common Issues

1. **Database connection errors**
   ```bash
   # Check Supabase status
   supabase status

   # Restart if needed
   supabase stop && supabase start
   ```

2. **Test data conflicts**
   ```bash
   # Reset database
   supabase db reset
   ```

3. **Port conflicts**
   ```bash
   # Check what's using port 3000
   lsof -i :3000
   ```

4. **Permission errors**
   ```bash
   # Make test runner executable
   chmod +x supabase/tests/run_tests.sh
   ```

## 📞 Support

If you encounter issues:

1. **Check the test logs** for detailed error messages
2. **Verify your environment setup** matches prerequisites
3. **Ensure all dependencies are installed** correctly
4. **Check Supabase is running** properly
5. **Review feature-specific documentation** for detailed guidance

For feature-specific testing guides, see:
- `tests/expert-verification/README.md` - Expert verification testing
- `tests/fixtures/README.md` - Test data and fixtures

---

**Remember:** Good tests are an investment in code quality and developer productivity! 🎯