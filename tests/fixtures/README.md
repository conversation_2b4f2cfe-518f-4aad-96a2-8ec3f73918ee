# Test Fixtures

This directory contains test files and mock data used in E2E and integration tests.

## Files

- `phd-certificate.pdf` - Mock PhD certificate for qualification document testing
- `organic-cert.pdf` - Mock organic inspector certificate 
- `experience-letter.pdf` - Mock experience documentation
- `large-document.pdf` - Large file for testing file size limits
- `invalid-format.txt` - Invalid file format for error testing

## Usage

These files are referenced in E2E tests for document upload functionality:

```typescript
await page.setInputFiles('#qualification-upload', 'tests/fixtures/phd-certificate.pdf');
```

## Creating Mock Files

To create test PDF files, you can use:

```bash
# Create small PDF files for testing
echo "Mock PhD Certificate Document" | pandoc -o tests/fixtures/phd-certificate.pdf
echo "Mock Organic Inspector Certificate" | pandoc -o tests/fixtures/organic-cert.pdf
echo "Mock Experience Letter Document" | pandoc -o tests/fixtures/experience-letter.pdf

# Create large file for size testing (10MB+)
dd if=/dev/zero of=tests/fixtures/large-document.pdf bs=1M count=12

# Create invalid format file
echo "This is not a PDF" > tests/fixtures/invalid-format.txt
```

Note: The actual PDF creation requires `pandoc` to be installed. For testing purposes, you can create dummy files with the correct extensions.