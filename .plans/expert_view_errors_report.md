Comprehensive Issue Report: Expert Profile Page View Problems

📋 Executive Summary

This report documents a critical issue with the expert profile page
(/dashboard/users/[id]) where the interface is displaying an incorrect
simple card view instead of the expected expert tabs interface. The
issue involves both frontend rendering logic and database role
management inconsistencies.

🎯 Main Goals

    1. Fix all compilation and runtime errors preventing the application
    from running
    2. Display the correct expert view with tabs interface for expert
    users
    3. Show real database data instead of placeholder values
    4. Ensure proper role detection and data consistency across the system
    5. Maintain approve/reject functionality for admin users

📊 Current Status

    • ❌ Compilation Error: Persistent "Unexpected token div. Expected
    jsx identifier" syntax error
    • ❌ Wrong View Displayed: Simple card layout instead of expert tabs
    interface
    • ❌ Database Inconsistency: Role detection failing due to missing
    records
    • ⚠️ Data Display: Showing placeholder text instead of actual expert
    data

🔍 Issue Timeline & Actions Taken

Phase 1: Initial Problem Identification
Issue: Expert profile page showing incorrect simple card view instead
of expert tabs interface.

Expected View:
[Overview] [Documents] [History] [Consultations]
- Professional Information
- Education & Qualifications
- Approve/Reject buttons

Actual View:
Personal Information Card:
- Phone: +*****************
- Language: en
- Joined: Jun 26, 2025
Account Status Card:
- Role: Farmer
- Status: Active

Phase 2: Frontend Investigation & Fixes

Action 2.1: Enhanced Database Query
    • File: src/app/dashboard/users/[id]/page.tsx (lines 130-141)
    • Change: Added comprehensive field selection for expert_profiles
    • Code:
.select(`
    *,
    expert_profiles(bio, years_of_experience, education, expert_type,
expertise_area, qualifications, certifications, current_position,
organization, languages_spoken, professional_memberships,
awards_honors, verification_status, verified_at, verified_by),
    client_profiles(client_type, farm_name, farm_size_hectares)
`)

Action 2.2: Added Qualifications Field
    • File: src/app/dashboard/users/[id]/page.tsx (lines 620-630)
    • Change: Added qualifications display in Education section
    • Code:
<div className="flex justify-between items-center py-3 border-b 
border-gray-100 dark:border-gray-800 text-left rtl:text-right">
    <span className="font-medium text-gray-700 dark:text-gray-300">{t
("userDetailPage.qualificationsLabel")}:</span>
    <span className="text-gray-900 dark:text-gray-100">
    {user.expert_profiles?.qualifications ||
t("userDetailPage.notSpecified")}
    </span>
</div>

Action 2.3: Fixed Array Response Handling
    • File: src/app/dashboard/users/[id]/page.tsx (lines 152-156)
    • Issue: Supabase returning expert_profiles as array instead of
    object
    • Fix: Added array-to-object conversion
    • Code:
if (Array.isArray(data.expert_profiles)) {
    data.expert_profiles = data.expert_profiles.length > 0 ?
data.expert_profiles[0] : null;
}

Action 2.4: Removed Competing View
    • File: src/app/dashboard/users/[id]/page.tsx (lines 756-802)
    • Change: Removed simple card view that was competing with expert
    tabs
    • Reason: Two views existed - expert tabs and simple cards, causing
    wrong view to display

Phase 3: Database Investigation

Action 3.1: Identified Role Management Inconsistency
Root Cause Discovery: Migration 20251004_remove_role_column.sql
created inconsistency:
    • profiles.role column still exists with role = 'expert'
    • user_roles table missing corresponding records
    • expert_profiles table missing expert data
    • Application logic checks !!user.expert_profiles for expert
    detection

Action 3.2: Created Database Fix Migration
    • File: supabase/migrations/verification_fixes/20251005_fix_expert_rol
    e_data.sql
    • Purpose: Sync role data across all tables
    • Actions:
    1. Ensure required roles exist in roles table
    2. Create user_roles entries from profiles.role
    3. Create missing expert_profiles records
    4. Create missing client_profiles records
    5. Populate expert data for specific user
    6. Data integrity verification

Phase 4: Syntax Error Resolution Attempts

Action 4.1: Removed Debug Code
    • File: src/app/dashboard/users/[id]/page.tsx
    • Change: Removed temporary forceExpertView debugging variables
    • Result: Syntax error persisted

Action 4.2: Fixed Empty Lines
    • File: src/app/dashboard/users/[id]/page.tsx (lines 407-408)
    • Change: Removed extra empty lines between code and JSX return
    • Result: Syntax error still persistent

🗂️ File Context & Database Schema

Key Files Involved

Frontend Files:
    1. `src/app/dashboard/users/[id]/page.tsx` - Main expert profile page
    2. `src/utils/get-user-role.ts` - Role detection utility
    3. `src/app/actions.ts` - Authentication and role management
    4. `src/middleware.ts` - Request middleware with role checking

Database Migration Files:
    1. `supabase/migrations/20251002_public_schema.sql` - Main schema
    2. `supabase/migrations/verification_fixes/20251004_remove_role_column.
    sql` - Problematic migration
    3. `supabase/migrations/verification_fixes/20251005_fix_expert_role_dat
    a.sql` - Proposed fix

Database Schema Context

profiles Table (Line 2394-2415):
CREATE TABLE "public"."profiles" (
    "id" uuid NOT NULL,
    "email" character varying(255) NOT NULL,
    "first_name" text NOT NULL,
    "last_name" text NOT NULL,
    "phone_number" character varying(20),
    "role" "public"."user_role" DEFAULT 'farmer'::user_role  -- Still
exists!
);

expert_profiles Table (Line 2336+):
CREATE TABLE "public"."expert_profiles" (
    "id" uuid NOT NULL,
    "bio" text,
    "years_of_experience" integer,
    "verification_status" expert_verification_status DEFAULT
'pending'
);

user_roles Table:
CREATE TABLE "public"."user_roles" (
    "user_id" uuid REFERENCES profiles(id),
    "role_id" uuid REFERENCES roles(id)
);

🔧 Role Detection Logic Analysis

Current Detection Methods (Priority Order):
    1. User metadata: session.user.user_metadata?.role
    2. user_roles table: Join with roles table
    3. expert_profiles existence: SELECT id FROM expert_profiles WHERE id =
        user_id
    4. farmer_profiles existence: Fallback check
    5. Default: 'expert'

Expert View Condition:
const isExpert = !!user.expert_profiles;  // Line 398

Problem: If expert_profiles record doesn't exist, isExpert = false →
shows simple card view

🚨 Current Persistent Issues

1. Compilation Error
Error: × Unexpected token `div`. Expected jsx identifier
╭─[/src/app/dashboard/users/[id]/page.tsx:410:1]
410 │     <div className="p-6" dir={locale === 'ar' ? 'rtl' :
'ltr'}>

2. Database Data Mismatch
    • User has profiles.role = 'expert'
    • Missing record in user_roles table
    • Missing record in expert_profiles table
    • Frontend checks expert_profiles existence → fails → shows wrong
    view

3. View Logic Issue
{isExpert ? (
    <Tabs>...</Tabs>  // Expert tabs interface
) : (
    <SimpleCards>...</SimpleCards>  // Wrong view being shown
)}

📋 Recommended Action Plan

Immediate Actions (Priority 1)
    1. Fix Syntax Error: Investigate and resolve JSX compilation issue
    2. Apply Database Migration: Run 20251005_fix_expert_role_data.sql
    3. Test Role Detection: Verify isExpert evaluates correctly

Verification Steps (Priority 2)
    1. Database Verification:
    SELECT p.email, p.role, ep.id as expert_profile_exists
    FROM profiles p
    LEFT JOIN expert_profiles ep ON ep.id = p.id
    WHERE p.email = '<EMAIL>';

    2. Frontend Testing:
    • Check console logs for role detection
    • Verify expert tabs interface displays
    • Test approve/reject button functionality

Long-term Improvements (Priority 3)
    1. Consolidate Role Logic: Use single source of truth for roles
    2. Add Error Boundaries: Better error handling for missing data
    3. Improve Type Safety: Better TypeScript definitions for user data

📊 Success Criteria

Technical Success:
    • ✅ No compilation errors
    • ✅ Expert tabs interface displays correctly
    • ✅ Real database data shows in all fields
    • ✅ Approve/reject buttons functional
    • ✅ Role detection works consistently

User Experience Success:
    • ✅ Expert sees professional interface with tabs
    • ✅ Admin can approve/reject experts
    • ✅ All expert information displays correctly
    • ✅ Document management works
    • ✅ Consultation access available for approved experts

🔍 Next Steps

    1. Resolve syntax error preventing compilation
    2. Apply database migration to fix role inconsistencies
    3. Test complete user flow from login to expert profile view
    4. Verify data integrity across all role-related tables
    5. Document final solution for future reference

This comprehensive approach addresses both the immediate technical
issues and the underlying architectural problems that caused this
situation.