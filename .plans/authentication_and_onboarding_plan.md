# Plan: Unified Authentication & Role-Specific Verification

**Objective:** Implement a robust authentication and onboarding workflow that directs users to the correct experience based on their role, ensuring experts undergo mandatory verification while admins gain immediate access.

### 1. High-Level Workflow

```mermaid
graph TD
    A[User attempts login] --> B{Authenticate via Supabase};
    B --> C{Fetch User Role};
    C -->|Role is Admin| D[Grant Full Access & Redirect to Dashboard];
    C -->|Role is Expert| E{Check Verification Status};
    E -->|Status is 'approved'| D;
    E -->|Status is 'pending', 'rejected', etc.| F[Lock Features & Show Verification UI];
```

### 2. Implementation Steps

#### 2.1. Middleware Enhancement (`src/middleware.ts`)

The middleware will be the primary mechanism for enforcing role-based access and verification status.

**Logic:**

1.  On every request to a protected route (e.g., `/dashboard/*`), get the current user and their profile from the database.
2.  **Admin Check:**
    *   If the user's role is `admin`, allow the request to proceed without any further checks.
3.  **Expert Check:**
    *   If the user's role is `agriculture_expert` or `industrial_expert`:
        *   Check the `account_activated` status on their `profiles` record.
        *   If `account_activated` is `false`, and the user is trying to access a restricted page (like `/dashboard/consultations`), redirect them to a dedicated `/dashboard/verification-pending` page.
        *   Allow access to non-critical pages like the main dashboard home (`/dashboard`) and their profile (`/dashboard/my-profile`).
4.  **Default Behavior:** All other authenticated roles (e.g., `farmer`, `factory_worker`) can proceed.

#### 2.2. Login & Redirection Logic

**File:** `src/app/(auth)/sign-in/page.tsx` (and associated server actions)

1.  After a user successfully logs in, retrieve their role from the `profiles` table.
2.  **Conditional Redirect:**
    *   If the role is `admin`, redirect directly to `/dashboard`.
    *   If the role is `agriculture_expert` or `industrial_expert`, check their `account_activated` status.
        *   If `true`, redirect to `/dashboard`.
        *   If `false`, redirect to `/dashboard/verification-pending`.

#### 2.3. Create the Verification Pending Page

**File:** `src/app/dashboard/verification-pending/page.tsx`

1.  This page will serve as the landing area for unverified experts.
2.  It should display a clear message indicating that their account is under review or requires action.
3.  This page will host the verification form/component outlined in the `expert_verification_plan.md`.

#### 2.4. Disable Verification for Admins

1.  Review all components that currently display a verification prompt or status.
2.  Wrap these components in a conditional check that ensures they are only rendered if the user's role is **not** `admin`.

**Example:**
```tsx
// In a layout or dashboard component
const { user } = useUser(); // Custom hook to get user profile

// ...

{user && user.role !== 'admin' && <VerificationStatusBanner />}