# Plan: Expert Verification Workflow Implementation

**Objective:** Implement the complete end-to-end expert verification workflow, covering both the admin's review interface and the expert's submission and status-checking experience.

### 1. Admin Perspective (`/dashboard/users/[id]`)

This view is the control center for managing expert verification.

#### 1.1. Data Fetching and Hydration

1.  **Server-Side Data Fetching:**
    *   On page load, use a server-side function (`getServerSideProps` or a server component) to fetch all necessary data for the given expert `[id]`.
    *   **Primary Function:** Call the `get_expert_verification_details(p_expert_id)` PostgreSQL function. This function is designed to aggregate all required information in a single call.
    *   **Data to be Returned:** The function will return a JSON object containing:
        *   Core profile details from `profiles`.
        *   Expert-specific data from `expert_profiles` (bio, experience, `verification_status`, `expert_type`).
        *   An array of all documents from `expert_documents`.
        *   A chronological list of all status changes from `expert_verification_history`.
2.  **State Management:**
    *   Use a client-side state management solution (like React Context or a simple `useState`) to hold the fetched expert data, allowing for dynamic UI updates without re-fetching.

#### 1.2. Action Implementation (Approve/Reject)

1.  **"Approve" Button:**
    *   **onClick Handler:** Trigger an API call to a new route handler (e.g., `/api/users/[id]/verify`).
    *   **API Route Handler:** This backend route will securely call the `update_expert_verification_status` database function with the following parameters:
        *   `p_expert_id`: The expert's UUID.
        *   `p_new_status`: `'approved'`.
        *   `p_admin_notes`: An optional note from the admin.
    *   **UI Feedback:** On a successful API response, display a success toast and update the client-side state to reflect the new `approved` status, which should automatically update the UI (e.g., show a green "Approved" badge).
2.  **"Reject" Button:**
    *   **onClick Handler:** Open a `RejectionModal` component.
    *   **Rejection Modal:** This modal will contain:
        *   A `textarea` for the admin to provide mandatory `rejection_reasons`.
        *   A `textarea` for optional `admin_notes`.
    *   **Submission:** On form submission, make an API call to the same `/api/users/[id]/verify` route.
    *   **API Route Handler:** The backend will call the `update_expert_verification_status` function with:
        *   `p_expert_id`: The expert's UUID.
        *   `p_new_status`: `'rejected'`.
        *   `p_rejection_reasons`: The array of reasons from the form.
        *   `p_admin_notes`: The optional notes.
    *   **UI Feedback:** On success, close the modal, show a confirmation toast, and update the client-side state to `rejected`.

### 2. Expert Perspective (Post-Login Flow)

This workflow ensures unverified experts are guided through the process and blocked from restricted features.

#### 2.1. Persistent Verification Component

1.  **Component:** Create a new component, `VerificationStatusBanner.tsx`.
2.  **Logic:**
    *   This component will fetch the expert's own verification status using the `get_my_verification_status()` database function.
    *   It will display different content based on the returned `verification_status`:
        *   **'pending'**: "Your application is under review by our team."
        *   **'rejected'**: "Your application was not approved. Please review the feedback and resubmit." (Should also display rejection reasons).
        *   **'resubmission_required'**: "Please provide additional information or documents as requested."
3.  **Display:** This banner should be prominently displayed on the expert's main dashboard page (`/dashboard`).

#### 2.2. Application Locking (Middleware)

**File:** `src/middleware.ts`

1.  As defined in the `authentication_and_onboarding_plan.md`, the middleware will check the `account_activated` flag for any user with an expert role.
2.  **Route Protection:**
    *   **Allowed Routes:** `/dashboard`, `/dashboard/my-profile`, `/dashboard/verification-pending`.
    *   **Blocked Routes:** `/dashboard/consultations`, `/dashboard/requests`, and any other feature-specific pages.
    *   If an unverified expert tries to access a blocked route, they should be immediately redirected to `/dashboard/verification-pending`.

#### 2.3. Multi-Step Verification Form

**Location:** This form will be the main content of the `/dashboard/verification-pending` page.

1.  **Form Structure:** Use a multi-step form UI (e.g., using tabs or a stepper component) to collect all required information, matching the fields listed in the prompt.
2.  **Data Submission:**
    *   **Profile Data:** On submission, update the `expert_profiles` table with the bio, experience, education, etc.
    *   **Document Uploads:**
        *   Use Supabase Storage for file uploads.
        *   For each uploaded file, call the `upload_expert_verification_document` database function to create a corresponding record in `expert_documents`.
3.  **Final Submission:**
    *   Once all steps are complete, a final "Submit for Review" button will set the `expert_profiles.verification_status` to `pending`.

### 3. Data Display on `/dashboard/users/[id]`

The page will be structured with tabs to organize the vast amount of information.

*   **Basic Information Tab:** Display all fields from `profiles` and `expert_profiles`.
*   **User History Tab:** Fetch and display data from `expert_verification_history`.
*   **Documents Tab:**
    *   Render a list of documents from the `expert_documents` array.
    *   Implement a `DocumentViewer` component that can display PDFs or images in a modal or inline viewer.
*   **Consultations Tab:**
    *   Fetch and display a list of all consultations assigned to this expert from the `consultation_requests` table.