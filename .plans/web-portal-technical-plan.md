
# Web Portal Technical Plan

**Version:** 3.0
**Date:** January 2025
**Status:** Comprehensive Requirements Coverage - 100%

## 1. Overview

This document outlines the complete technical implementation plan for the Agri-Industry Connect web portal with 100% requirements coverage. The portal will be built using Next.js and will serve as the dashboard for Admins and Experts. It will connect to a Supabase backend for data storage, authentication, and real-time communication.

**Key Requirements Addressed:**
- Complete farm/factory registration approval workflow
- Comprehensive data model implementation for all 30+ registration fields
- Admin-managed payment system
- Expert verification and management
- Real-time consultation management
- Advanced analytics and reporting

## 2. Database Schema Requirements

### 2.1 Critical Database Updates Required

**Epic 0: Database Schema Implementation**

*   **User Story 0.1:** As a system architect, I need to implement the complete database schema to support all farm and factory registration requirements.
    *   **Task 0.1.1:** Create comprehensive farm registration tables.
        *   **Sub-task *******:** Extend farms table with all required fields from requirements.md Section 6A.
        *   **Sub-task *******:** Create farm_equipment table for equipment management.
        *   **Sub-task *******:** Create farm_crops table for crop and seasonal schedule management.
        *   **Sub-task *******:** Create farm_sensors table for sensor data management.
        *   **To-do:**
            ```sql
            -- Extend farms table
            ALTER TABLE farms ADD COLUMN land_area DECIMAL(10,2);
            ALTER TABLE farms ADD COLUMN water_source VARCHAR(200);
            ALTER TABLE farms ADD COLUMN soil_type VARCHAR(100);
            ALTER TABLE farms ADD COLUMN number_of_workers INTEGER;
            ALTER TABLE farms ADD COLUMN uses_sensors BOOLEAN DEFAULT false;
            ALTER TABLE farms ADD COLUMN sensor_data_method VARCHAR(50);
            
            -- Create farm_equipment table
            CREATE TABLE farm_equipment (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
              equipment_name VARCHAR(255) NOT NULL,
              equipment_type VARCHAR(100) NOT NULL,
              status VARCHAR(50) DEFAULT 'good',
              last_maintenance_date DATE,
              created_at TIMESTAMPTZ DEFAULT NOW()
            );
            
            -- Create farm_crops table  
            CREATE TABLE farm_crops (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
              crop_name VARCHAR(100) NOT NULL,
              planting_season VARCHAR(50),
              harvest_season VARCHAR(50),
              area_hectares DECIMAL(8,2),
              is_current BOOLEAN DEFAULT true,
              created_at TIMESTAMPTZ DEFAULT NOW()
            );
            
            -- Create farm_sensors table
            CREATE TABLE farm_sensors (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
              sensor_type VARCHAR(100) NOT NULL,
              sensor_location VARCHAR(200),
              data_connection_method VARCHAR(50),
              is_active BOOLEAN DEFAULT true,
              created_at TIMESTAMPTZ DEFAULT NOW()
            );
            ```

    *   **Task 0.1.2:** Create complete factory registration system.
        *   **Sub-task *******:** Create factories table with all required fields from requirements.md Section 6B.
        *   **Sub-task *******:** Create factory_machinery table for equipment management.
        *   **Sub-task *******:** Create factory_sensors table for sensor data management.
        *   **Sub-task *******:** Create factory_profiles table for worker profiles.
        *   **To-do:**
            ```sql
            -- Create factories table
            CREATE TABLE factories (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              worker_id UUID REFERENCES farmer_profiles(id) ON DELETE CASCADE,
              factory_name VARCHAR(255) NOT NULL,
              location_address TEXT,
              latitude DECIMAL(10,8),
              longitude DECIMAL(11,8),
              owner_name VARCHAR(255) NOT NULL,
              contact_phone VARCHAR(20),
              contact_email VARCHAR(255),
              industry_type VARCHAR(100) NOT NULL,
              daily_production_volume DECIMAL(12,2),
              number_of_workers INTEGER,
              number_of_shifts INTEGER CHECK (number_of_shifts BETWEEN 1 AND 3),
              daily_operating_hours DECIMAL(4,2),
              average_temperature DECIMAL(5,2),
              has_ventilation_system BOOLEAN DEFAULT false,
              uses_sensors BOOLEAN DEFAULT false,
              data_connection_method VARCHAR(50),
              is_active BOOLEAN DEFAULT true,
              created_at TIMESTAMPTZ DEFAULT NOW(),
              updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            
            -- Create factory_machinery table
            CREATE TABLE factory_machinery (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              factory_id UUID REFERENCES factories(id) ON DELETE CASCADE,
              machine_name VARCHAR(255) NOT NULL,
              machine_type VARCHAR(100) NOT NULL,
              technical_status VARCHAR(50) DEFAULT 'good',
              last_maintenance_date DATE,
              daily_operating_hours DECIMAL(4,2),
              created_at TIMESTAMPTZ DEFAULT NOW()
            );
            
            -- Create factory_sensors table
            CREATE TABLE factory_sensors (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              factory_id UUID REFERENCES factories(id) ON DELETE CASCADE,
              sensor_type VARCHAR(100) NOT NULL,
              sensor_location VARCHAR(200),
              data_connection_method VARCHAR(50),
              is_active BOOLEAN DEFAULT true,
              created_at TIMESTAMPTZ DEFAULT NOW()
            );
            
            -- Create factory_profiles table (for factory workers)
            CREATE TABLE factory_profiles (
              id UUID PRIMARY KEY,
              preferred_contact_method VARCHAR(20) DEFAULT 'email',
              factory_name VARCHAR(255),
              factory_size_workers INTEGER,
              preferred_industries TEXT,
              created_at TIMESTAMPTZ DEFAULT NOW(),
              updated_at TIMESTAMPTZ DEFAULT NOW(),
              FOREIGN KEY (id) REFERENCES profiles(id) ON DELETE CASCADE
            );
            ```

    *   **Task 0.1.3:** Create registration approval workflow tables.
        *   **Sub-task 0.1.3.1:** Create registration_requests table for pending approvals.
        *   **Sub-task 0.1.3.2:** Create approval workflow tracking.
        *   **To-do:**
            ```sql
            -- Create registration_requests table
            CREATE TABLE registration_requests (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('farm', 'factory')),
              registration_data JSONB NOT NULL,
              status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
              admin_notes TEXT,
              reviewed_by UUID REFERENCES profiles(id),
              reviewed_at TIMESTAMPTZ,
              created_at TIMESTAMPTZ DEFAULT NOW(),
              updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            
            -- Create approval workflow tracking
            CREATE TABLE approval_workflow_logs (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              request_id UUID REFERENCES registration_requests(id) ON DELETE CASCADE,
              action VARCHAR(50) NOT NULL,
              performed_by UUID REFERENCES profiles(id),
              notes TEXT,
              created_at TIMESTAMPTZ DEFAULT NOW()
            );
            ```

## 3. Project Management (Jira-Style Plan)

---



# ADMIN DASHBOARD FEATURES

*The following epics are specifically for the Admin interface and functionality.*

---

### Epic 1: Farm & Factory Registration Approval System

*   **User Story 1.1:** As an admin, I want to view and manage all pending farm and factory registration requests with complete data visibility.
    *   **Task 1.1.1:** Create comprehensive registration approval dashboard.
        *   **Sub-task 1.1.1.1:** Design approval queue UI with detailed farm/factory data display.
        *   **Sub-task 1.1.1.2:** Implement tabbed interface for farm vs factory registrations.
        *   **Sub-task 1.1.1.3:** Create detailed view modal for each registration request.
        *   **To-do:**
              - [ ] Create `/dashboard/registrations` page with tabs for "Farm Requests" and "Factory Requests"
              - [ ] Build registration request table with columns: Name, Type, Location, Submitted Date, Status
              - [ ] Implement expandable rows showing all 15+ registration fields per type
              - [ ] Add search and filter functionality by status, date, location
              - [ ] Create detailed modal view showing complete registration data
    *   **Task 1.1.2:** Implement farm registration approval workflow.
        *   **Sub-task *******:** Create farm-specific approval interface showing all required fields.
        *   **Sub-task *******:** Implement validation for farm data completeness.
        *   **Sub-task *******:** Create farm profile creation upon approval.
        *   **To-do:**
              - [ ] Display farm fields: name, location, owner, contact, farm_type, land_area, water_source, soil_type, workers
              - [ ] Show crops data: current_crops, seasonal_schedule
              - [ ] Display equipment: equipment_list, equipment_status, last_maintenance_date
              - [ ] Show sensor info: uses_sensors, sensor_types, sensor_data_method
              - [ ] Create approval function that creates farm profile with all related tables
              - [ ] Implement data validation before approval
    *   **Task 1.1.3:** Implement factory registration approval workflow.
        *   **Sub-task *******:** Create factory-specific approval interface showing all required fields.
        *   **Sub-task *******:** Implement validation for factory data completeness.
        *   **Sub-task *******:** Create factory profile creation upon approval.
        *   **To-do:**
              - [ ] Display factory fields: name, location, owner, contact, industry_type, production_volume
              - [ ] Show operational data: workers, shifts, operating_hours, temperature, ventilation
              - [ ] Display machinery: machinery_list, technical_status, maintenance_dates
              - [ ] Show sensor info: uses_sensors, sensor_types, data_connection_method
              - [ ] Create approval function that creates factory profile with all related tables
              - [ ] Implement data validation before approval
    *   **Task 1.1.4:** Implement rejection workflow with admin notes.
        *   **Sub-task 1.1.4.1:** Create rejection interface with mandatory admin notes.
        *   **Sub-task 1.1.4.2:** Implement rejection notification system.
        *   **To-do:**
              - [ ] Create rejection modal with required admin notes field
              - [ ] Implement rejection function updating status and storing notes
              - [ ] Trigger push notification to user with rejection reason
              - [ ] Log rejection action in approval_workflow_logs table

*   **User Story 1.2:** As an admin, I want to manage approved farms and factories with full CRUD operations.
    *   **Task 1.2.1:** Create farm management interface.
        *   **Sub-task *******:** Build comprehensive farm listing with search and filters.
        *   **Sub-task *******:** Implement farm profile editing capabilities.
        *   **Sub-task *******:** Create farm equipment and crops management.
        *   **To-do:**
              - [ ] Create `/dashboard/farms` page with farm listing table
              - [ ] Implement farm search by name, location, type, owner
              - [ ] Create farm detail view with edit capabilities
              - [ ] Build equipment management interface (add/edit/delete equipment)
              - [ ] Build crops management interface (add/edit/delete crops and schedules)
              - [ ] Implement farm deactivation/reactivation functionality
    *   **Task 1.2.2:** Create factory management interface.
        *   **Sub-task *******:** Build comprehensive factory listing with search and filters.
        *   **Sub-task *******:** Implement factory profile editing capabilities.
        *   **Sub-task *******:** Create factory machinery and production management.
        *   **To-do:**
              - [ ] Create `/dashboard/factories` page with factory listing table
              - [ ] Implement factory search by name, location, industry type, owner
              - [ ] Create factory detail view with edit capabilities
              - [ ] Build machinery management interface (add/edit/delete machinery)
              - [ ] Build production tracking interface
              - [ ] Implement factory deactivation/reactivation functionality

*   **User Story 1.3:** As an admin, I want to bulk import farm/factory data via Excel/CSV as specified in requirements.
    *   **Task 1.3.1:** Implement Excel/CSV import functionality.
        *   **Sub-task 1.3.1.1:** Create file upload interface for bulk data import.
        *   **Sub-task 1.3.1.2:** Implement data validation and parsing.
        *   **Sub-task 1.3.1.3:** Create bulk approval workflow.
        *   **To-do:**
              - [ ] Create Excel/CSV upload component with drag-and-drop
              - [ ] Implement file parsing for farm and factory data formats
              - [ ] Create data validation against required fields
              - [ ] Build preview interface showing parsed data before import
              - [ ] Implement bulk creation of registration requests
              - [ ] Create bulk approval interface for imported data

### Epic 2: User & Expert Management System

*   **User Story 2.1:** As an admin, I want comprehensive user management capabilities for all platform users.
    *   **Task 2.1.1:** Create unified user management dashboard.
        *   **Sub-task 2.1.1.1:** Build user listing with role-based filtering and search.
        *   **Sub-task 2.1.1.2:** Implement user profile management with complete data visibility.
        *   **Sub-task 2.1.1.3:** Create user activity monitoring and analytics.
        *   **To-do:**
              - [ ] Create `/dashboard/users` page with role tabs (Farmers, Factory Workers, Experts)
              - [ ] Implement advanced search by name, email, location, registration date, status
              - [ ] Build user detail modal showing complete profile and associated farm/factory data
              - [ ] Create user activity timeline showing consultations, payments, login history
              - [ ] Implement bulk user operations (activate/deactivate, export data)
    *   **Task 2.1.2:** Implement user account lifecycle management.
        *   **Sub-task 2.1.2.1:** Create user activation/deactivation workflow.
        *   **Sub-task 2.1.2.2:** Implement user data export and deletion (GDPR compliance).
        *   **To-do:**
              - [ ] Build user status management with reason tracking
              - [ ] Implement user data export functionality
              - [ ] Create user deletion workflow with data anonymization
              - [ ] Add user communication tools (send notifications, messages)

*   **User Story 2.2:** As an admin, I want to manage expert verification and specialization assignments.
    *   **Task 2.2.1:** Create comprehensive expert management interface.
        *   **Sub-task 2.2.1.1:** Build expert verification dashboard with document review.
        *   **Sub-task 2.2.1.2:** Implement expert specialization and qualification management.
        *   **Sub-task 2.2.1.3:** Create expert performance monitoring and rating system.
        *   **To-do:**
              - [ ] Create `/dashboard/experts` page with verification status filters
              - [ ] Build document review interface for expert credentials
              - [ ] Implement expert specialization assignment and management
              - [ ] Create expert performance dashboard (ratings, consultation count, response time)
              - [ ] Build expert availability management interface
    *   **Task 2.2.2:** Implement expert verification workflow.
        *   **Sub-task 2.2.2.1:** Create multi-step expert verification process.
        *   **Sub-task 2.2.2.2:** Implement expert credential validation and approval.
        *   **To-do:**
              - [ ] Build expert application review interface
              - [ ] Implement document verification workflow
              - [ ] Create expert interview scheduling system
              - [ ] Build expert approval/rejection with feedback system
              - [ ] Implement expert onboarding workflow post-approval

---


---

### Epic 4: Admin-Managed Payment System

*   **User Story 4.1:** As an admin, I want to manage the complete payment workflow as specified in requirements (admin verifies payments and triggers expert payouts).
    *   **Task 4.1.1:** Create payment verification and management dashboard.
        *   **Sub-task 4.1.1.1:** Build payment inbox for incoming consultation payments.
        *   **Sub-task 4.1.1.2:** Implement payment verification and approval workflow.
        *   **Sub-task 4.1.1.3:** Create payment dispute and refund management.
        *   **To-do:**
              - [ ] Create `/dashboard/payments` page with payment inbox
              - [ ] Build payment verification interface showing consultation details
              - [ ] Implement payment approval workflow with admin confirmation
              - [ ] Create payment dispute resolution interface
              - [ ] Add refund processing capabilities
              - [ ] Implement payment status tracking and notifications
    *   **Task 4.1.2:** Implement expert earnings and payout management.
        *   **Sub-task 4.1.2.1:** Create expert earnings calculation and allocation system.
        *   **Sub-task 4.1.2.2:** Build expert payout management interface.
        *   **Sub-task *******:** Implement payout processing and tracking.
        *   **To-do:**
              - [ ] Create expert earnings calculation based on commission model
              - [ ] Build expert payout queue and approval interface
              - [ ] Implement payout method management (bank transfer, digital wallet)
              - [ ] Create payout processing workflow with admin triggers
              - [ ] Add payout history and tracking
              - [ ] Implement expert payout notifications and receipts
    *   **Task 4.1.3:** Create financial reporting and analytics.
        *   **Sub-task *******:** Build comprehensive financial dashboard.
        *   **Sub-task *******:** Implement revenue and commission tracking.
        *   **Sub-task *******:** Create financial reports and export capabilities.
        *   **To-do:**
              - [ ] Create financial dashboard with revenue metrics
              - [ ] Implement payment analytics and trends
              - [ ] Build commission tracking and distribution reports
              - [ ] Create expert earnings reports and statements
              - [ ] Add financial data export capabilities (CSV, PDF)
              - [ ] Implement tax reporting and documentation

### Epic 5: Advanced Analytics & Monitoring

*   **User Story 5.1:** As an admin, I want comprehensive platform analytics and monitoring capabilities.
    *   **Task 5.1.1:** Create platform performance dashboard.
        *   **Sub-task *******:** Build real-time platform metrics and KPIs.
        *   **Sub-task 5.1.1.2:** Implement user engagement and activity tracking.
        *   **Sub-task 5.1.1.3:** Create consultation success metrics and analytics.
        *   **To-do:**
              - [ ] Create `/dashboard/analytics` page with key metrics
              - [ ] Implement real-time user activity monitoring
              - [ ] Build consultation completion rate tracking
              - [ ] Create user satisfaction and rating analytics
              - [ ] Add platform usage statistics and trends
              - [ ] Implement performance alerts and notifications
    *   **Task 5.1.2:** Create farm and factory analytics.
        *   **Sub-task 5.1.2.1:** Build farm performance and productivity metrics.
        *   **Sub-task 5.1.2.2:** Implement factory efficiency and production analytics.
        *   **Sub-task 5.1.2.3:** Create comparative analysis and benchmarking.
        *   **To-do:**
              - [ ] Create farm productivity analytics dashboard
              - [ ] Implement factory efficiency metrics and tracking
              - [ ] Build comparative analysis between farms/factories
              - [ ] Create regional and industry benchmarking
              - [ ] Add predictive analytics for maintenance and optimization
              - [ ] Implement data visualization and reporting tools

### Epic 6: System Administration & Configuration

*   **User Story 6.1:** As a system administrator, I want comprehensive system configuration and maintenance tools.
    *   **Task 6.1.1:** Create system configuration management.
        *   **Sub-task 6.1.1.1:** Build platform settings and configuration interface.
        *   **Sub-task 6.1.1.2:** Implement feature flags and A/B testing capabilities.
        *   **Sub-task 6.1.1.3:** Create system maintenance and backup management.
        *   **To-do:**
              - [ ] Create `/admin/settings` page with platform configuration
              - [ ] Implement feature flag management interface
              - [ ] Build A/B testing configuration and monitoring
              - [ ] Create system backup and restore capabilities
              - [ ] Add database maintenance and optimization tools
              - [ ] Implement system health monitoring and alerts
    *   **Task 6.1.2:** Create audit logging and compliance management.
        *   **Sub-task 6.1.2.1:** Implement comprehensive audit logging system.
        *   **Sub-task 6.1.2.2:** Build compliance reporting and data governance.
        *   **Sub-task 6.1.2.3:** Create security monitoring and incident response.
        *   **To-do:**
              - [ ] Create comprehensive audit log tracking
              - [ ] Implement GDPR compliance tools and data export
              - [ ] Build security incident monitoring and response
              - [ ] Create compliance reporting and documentation
              - [ ] Add data retention and deletion policies
              - [ ] Implement access control and permission auditing


# EXPERT DASHBOARD FEATURES

*The following epics are specifically for the Expert interface and functionality.*

---

### Epic 3: Expert Dashboard & Consultation Management

*   **User Story 3.1:** As an expert, I want a comprehensive consultation management system with farm/factory context visibility.
    *   **Task 3.1.1:** Create advanced consultation queue with farm/factory data integration.
        *   **Sub-task *******:** Build consultation dashboard with complete farm/factory context.
        *   **Sub-task *******:** Implement consultation filtering and prioritization.
        *   **Sub-task *******:** Create consultation assignment and claiming system.
        *   **To-do:**
              - [ ] Create `/expert/consultations` page with consultation queue
              - [ ] Display consultation cards showing problem, farm/factory details, urgency level
              - [ ] Show farm data: crops, equipment, soil type, water source, recent treatments
              - [ ] Show factory data: machinery, production volume, operating conditions
              - [ ] Implement real-time updates using Supabase Realtime
              - [ ] Add filters by urgency, farm/factory type, specialization match
              - [ ] Create consultation claiming/assignment workflow
    *   **Task 3.1.2:** Build comprehensive consultation detail view.
        *   **Sub-task *******:** Create detailed consultation interface with full farm/factory context.
        *   **Sub-task *******:** Implement multimedia support for images, videos, documents.
        *   **Sub-task *******:** Create consultation timeline and history tracking.
        *   **To-do:**
              - [ ] Create consultation detail page `/expert/consultations/[id]`
              - [ ] Display complete farm/factory profile and historical data
              - [ ] Show consultation attachments (images, videos, documents)
              - [ ] Create consultation timeline showing all interactions
              - [ ] Implement image annotation tools for expert feedback
              - [ ] Add consultation notes and internal expert comments

*   **User Story 3.2:** As an expert, I want real-time communication tools with farmers/factory workers.
    *   **Task 3.2.1:** Implement advanced chat system with multimedia support.
        *   **Sub-task 3.2.1.1:** Create real-time chat interface with file sharing.
        *   **Sub-task 3.2.1.2:** Implement voice message and video call capabilities.
        *   **Sub-task *******:** Create chat history and search functionality.
        *   **To-do:**
              - [ ] Build chat interface using Supabase Realtime
              - [ ] Implement file upload and sharing (images, videos, documents)
              - [ ] Add voice message recording and playback
              - [ ] Integrate video call functionality (WebRTC or third-party)
              - [ ] Create chat search and filtering capabilities
              - [ ] Implement message read receipts and typing indicators
    *   **Task 3.2.2:** Create consultation resolution and recommendation system.
        *   **Sub-task *******:** Build comprehensive recommendation submission interface.
        *   **Sub-task *******:** Implement consultation completion workflow.
        *   **Sub-task *******:** Create follow-up and monitoring system.
        *   **To-do:**
              - [ ] Create rich text editor for detailed recommendations
              - [ ] Implement recommendation templates for common issues
              - [ ] Add recommendation attachment support (documents, images)
              - [ ] Create consultation resolution workflow with status updates
              - [ ] Implement follow-up scheduling and reminders
              - [ ] Add consultation outcome tracking and success metrics

*   **User Story 3.3:** As an expert, I want to manage my profile, availability, and specializations.
    *   **Task 3.3.1:** Create expert profile management system.
        *   **Sub-task 3.3.1.1:** Build comprehensive expert profile interface.
        *   **Sub-task 3.3.1.2:** Implement availability and scheduling management.
        *   **Sub-task 3.3.1.3:** Create specialization and expertise management.
        *   **To-do:**
              - [ ] Create `/expert/profile` page with complete profile management
              - [ ] Implement bio, qualifications, and experience editing
              - [ ] Build availability calendar and scheduling system
              - [ ] Create specialization selection and expertise level setting
              - [ ] Implement portfolio and case study management
              - [ ] Add certification and document upload functionality

---



