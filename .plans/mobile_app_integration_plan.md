# Plan: Mobile App Authentication & Database Integration

**Objective:** This document provides a technical guide for the mobile app development team to adapt the client application to the recent database schema enhancements.

### 1. Authentication Flow for `farmer` and `factory_worker`

The authentication process remains largely unchanged. The mobile app should continue using Supabase Auth for user sign-up and sign-in.

1.  **Sign-up:** When a new user registers, the mobile app should capture whether they are a `farmer` or `factory_worker`. This role information must be passed in the `data` property of the `supabase.auth.signUp()` call.
2.  **Sign-in:** Standard email/password or social login flows will work as before.
3.  **Session Management:** The app should continue to rely on Supabase for session management and JWT handling.

### 2. Schema Change Impact Analysis & Required Actions

This section details the critical database changes and the corresponding updates required in the mobile application.

#### 2.1. **Table Rename: `farmer_profiles` is now `client_profiles`**

**Impact:** High. All direct references to `farmer_profiles` in the mobile app's code will fail.

**Required Actions:**

1.  **Global Search-and-Replace:** Perform a project-wide search for the string `farmer_profiles` and replace all occurrences with `client_profiles`.
2.  **API Calls:** Update all Supabase API queries.
    *   **Old:** `supabase.from('farmer_profiles').select('*')`
    *   **New:** `supabase.from('client_profiles').select('*')`
3.  **Data Models/Types:** Update any local data models, TypeScript interfaces, or type definitions that represent the user profile.
    *   **Old:** `interface FarmerProfile { ... }`
    *   **New:** `interface ClientProfile { ... }`
4.  **Local Storage/State Management:** If user profile data is cached or stored locally (e.g., in AsyncStorage, Redux, Zustand), ensure that the keys and object structures are updated to reflect the `client_profiles` change.

#### 2.2. **New Column: `client_type` in `client_profiles`**

**Impact:** High. The registration process must be updated to populate this new mandatory field.

**Required Actions:**

1.  **Update Registration UI:** The sign-up or initial profile setup screen must include a clear choice for the user to select their type: "Farmer" or "Factory Worker".
2.  **Update API Payload:** The data payload sent to the database when creating a `client_profiles` record **must** include the `client_type` field.
    *   **Example Payload:**
        ```javascript
        const { data, error } = await supabase
          .from('client_profiles')
          .insert([
            { 
              id: user.id, 
              client_type: 'FARMER', // or 'FACTORY_WORKER'
              // ... other profile data
            },
          ]);
        ```
3.  **Update Data Models:** Add the `client_type` field to the local `ClientProfile` interface/type.
    ```typescript
    interface ClientProfile {
      id: string;
      client_type: 'FARMER' | 'FACTORY_WORKER';
      // ... other fields
    }
    ```

### 3. Account Setup Process (`account_setup_progress`)

**Objective:** To track the user's progress through a multi-step profile completion form after their initial registration.

**Workflow:**

1.  **Check Setup Status:** After login, the app should query the `get_account_setup_progress(user_id)` function to determine if the user has completed their profile. This function returns the `setup_completed` boolean and the `current_step`.
2.  **Force Profile Completion:** If `setup_completed` is `false`, the app must navigate the user to the profile setup screen and restrict access to other parts of the app until the process is finished.
3.  **Implement Multi-Step Form:** Create a multi-step form that matches the steps defined in the `registration_data` JSON structure (e.g., Basic Info, Location, Contact Info, etc.).
4.  **Save Progress:** After each step is completed, the app must call the `update_account_setup_step` database function. This function saves the data for that step and marks it as complete.
    *   **Function Signature:** `update_account_setup_step(user_id, step_number, step_name, step_data)`
    *   **Example Call:**
        ```javascript
        await supabase.rpc('update_account_setup_step', {
          user_id: user.id,
          step_number: 1,
          step_name: 'farm_basic_info',
          step_data: { farm_name: 'My Farm', ... }
        });
        ```
5.  **Final Submission:** After the final step, a "Complete Profile" button should make a final call to update the `profiles` table, setting `account_setup_completed` to `true`. This will unlock the rest of the application for the user.