-- Seed script for unverified expert user
-- This script creates an expert user that is unverified and needs to go through the verification process

-- Generate a new UUID for the unverified expert
-- You can replace this with your own UUID if needed
-- Current UUID: bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da

-- 1. Insert into auth.users table
INSERT INTO "auth"."users" (
    "instance_id", 
    "id", 
    "aud", 
    "role", 
    "email", 
    "encrypted_password", 
    "email_confirmed_at", 
    "invited_at", 
    "confirmation_token", 
    "confirmation_sent_at", 
    "recovery_token", 
    "recovery_sent_at", 
    "email_change_token_new", 
    "email_change", 
    "email_change_sent_at", 
    "last_sign_in_at", 
    "raw_app_meta_data", 
    "raw_user_meta_data", 
    "is_super_admin", 
    "created_at", 
    "updated_at", 
    "phone", 
    "phone_confirmed_at", 
    "phone_change", 
    "phone_change_token", 
    "phone_change_sent_at", 
    "confirmed_at", 
    "email_change_token_current", 
    "email_change_confirm_status", 
    "banned_until", 
    "reauthentication_token", 
    "reauthentication_sent_at", 
    "is_sso_user", 
    "deleted_at", 
    "is_anonymous"
) VALUES (
    '00000000-0000-0000-0000-000000000000', 
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da', 
    'authenticated', 
    'authenticated', 
    '<EMAIL>', 
    '$2a$10$caqcs6QuAmeWqUT479zz6eob6MPDJ49Roj3fKVs2By7nGDeUi8Boi', -- Password: 'password123'
    '2025-01-01 12:00:00.000000+00', 
    null, 
    '', 
    null, 
    '', 
    null, 
    '', 
    '', 
    null, 
    '2025-01-01 12:30:00.000000+00', 
    '{"provider": "email", "providers": ["email"]}', 
    '{"sub": "bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da", "role": "expert", "email": "<EMAIL>", "is_admin": false, "last_name": "Expert", "first_name": "Unverified", "email_verified": true, "phone_verified": false}', 
    null, 
    '2025-01-01 12:00:00.000000+00', 
    '2025-01-01 12:30:00.000000+00', 
    null, 
    null, 
    '', 
    '', 
    null, 
    '2025-01-01 12:00:00.000000+00', 
    '', 
    '0', 
    null, 
    '', 
    null, 
    'false', 
    null, 
    'false'
);

-- 2. Insert into profiles table (account_activated set to false for unverified status)
INSERT INTO public.profiles (
    id, 
    email, 
    first_name, 
    last_name, 
    phone_number,
    email_verified,
    password_set,
    is_active,
    account_activated,
    created_at,
    updated_at
) VALUES (
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    '<EMAIL>',
    'Unverified',
    'Expert',
    '+**********',
    true,
    true,
    true,
    false, -- Key: account_activated = false for unverified expert
    '2025-01-01 12:00:00.000000+00',
    '2025-01-01 12:00:00.000000+00'
);

-- 3. Assign expert role to the user
INSERT INTO public.user_roles (user_id, role_id)
SELECT 
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    r.id
FROM public.roles r 
WHERE r.name = 'agriculture_expert'; -- Change to 'industrial_expert' if needed

-- 4. Create expert profile with pending verification status
INSERT INTO public.expert_profiles (
    id,
    expert_type,
    verification_status,
    bio,
    years_of_experience,
    education,
    expertise_area,
    certifications,
    current_position,
    organization,
    languages_spoken,
    professional_memberships,
    awards_honors,
    is_available,
    created_at,
    updated_at
) VALUES (
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    'AGRICULTURE', -- Change to 'INDUSTRIAL' if needed
    'pending', -- Unverified status
    'Experienced agricultural expert seeking verification on the platform.',
    5,
    'Bachelor of Agricultural Science, Master of Crop Science',
    'Crop Management, Soil Analysis, Sustainable Farming',
    'Certified Crop Advisor (CCA), Organic Farming Certification',
    'Senior Agricultural Consultant',
    'AgriTech Solutions Inc.',
    'English, Spanish',
    'American Society of Agronomy, Soil Science Society',
    'Excellence in Sustainable Farming Award 2023',
    true,
    '2025-01-01 12:00:00.000000+00',
    '2025-01-01 12:00:00.000000+00'
);

-- 5. Add some sample documents for the verification process
INSERT INTO public.expert_documents (
    expert_id,
    document_type,
    document_name,
    document_url,
    file_size,
    file_type,
    verification_status,
    upload_date
) VALUES 
(
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    'ID',
    'driver_license.pdf',
    'https://example.com/documents/driver_license.pdf',
    245760, -- 240KB
    'application/pdf',
    'pending',
    '2025-01-01 12:15:00.000000+00'
),
(
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    'QUALIFICATION',
    'agricultural_degree.pdf',
    'https://example.com/documents/agricultural_degree.pdf',
    512000, -- 500KB
    'application/pdf',
    'pending',
    '2025-01-01 12:16:00.000000+00'
),
(
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    'CERTIFICATION',
    'cca_certificate.pdf',
    'https://example.com/documents/cca_certificate.pdf',
    180000, -- 175KB
    'application/pdf',
    'pending',
    '2025-01-01 12:17:00.000000+00'
);

-- 6. Add initial verification history entry
INSERT INTO public.expert_verification_history (
    expert_id,
    previous_status,
    new_status,
    admin_notes,
    created_at
) VALUES (
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    null, -- No previous status for initial entry
    'pending',
    'Initial verification application submitted',
    '2025-01-01 12:20:00.000000+00'
);

-- Verification: Check that the user was created correctly
SELECT 
    'User verification check' as check_type,
    u.email,
    u.raw_user_meta_data->>'role' as user_role,
    p.account_activated,
    ep.verification_status,
    ep.expert_type
FROM auth.users u
JOIN public.profiles p ON u.id = p.id
JOIN public.expert_profiles ep ON u.id = ep.id
WHERE u.id = 'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da';

-- Show documents for verification
SELECT 
    'Documents check' as check_type,
    document_type,
    document_name,
    verification_status,
    upload_date
FROM public.expert_documents
WHERE expert_id = 'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da'
ORDER BY upload_date;

-- Show user role assignment
SELECT 
    'Role assignment check' as check_type,
    p.first_name,
    p.last_name,
    r.name as role_name,
    r.is_admin_role
FROM public.profiles p
JOIN public.user_roles ur ON p.id = ur.user_id
JOIN public.roles r ON ur.role_id = r.id
WHERE p.id = 'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da';

/*
NOTES:
- This user can log in with email: <EMAIL> and password: password123
- The user will be redirected to the verification pending page due to account_activated = false
- The expert profile has verification_status = 'pending'
- Sample documents are included for the verification process
- The user has the 'agriculture_expert' role assigned
- Change expert_type to 'INDUSTRIAL' and role to 'industrial_expert' if you need an industrial expert instead
*/