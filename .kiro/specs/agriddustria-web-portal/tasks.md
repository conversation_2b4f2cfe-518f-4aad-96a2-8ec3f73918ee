# Implementation Plan

- [ ] 1. Enhance existing authentication and role-based access control
  - [ ] 1.1 Update authentication system to use latest database schema
    - Modify existing DashboardLayout to properly handle role-based access from database profiles
    - Update getUserRole utility to use the latest database schema with proper role detection
    - Enhance existing role-based sidebar navigation to restrict admin features from experts
    - Add middleware protection for admin-only routes
    - Ensure smooth user profile creation upon signup, integrating with the latest database schema.
    - Develop database triggers/functions to ensure error handling and smooth application functionality for newly authenticated users' profile creation upon signup.
    - _Requirements: 1.1, 1.2, 1.3, 10.1, 10.2, 10.3_

  - [ ] 1.2 Manual testing for authentication and role-based access
    - Test admin login: verify admin dashboard access → check admin-only navigation items → test admin features
    - Test expert login: verify expert dashboard access → check expert-only navigation → verify admin features blocked
    - Test role-based route protection: expert tries admin route → verify access denied → check redirect
    - Test session management: login → wait for timeout → verify auto-logout → test token refresh
    - Test password reset flow: request reset → check email → reset password → verify login works
    - Test edge cases: invalid credentials, expired sessions, concurrent logins, role changes
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 10.1, 10.2, 10.3_

  - [ ] 1.3 Refine New Expert Signup Process
  - Enhance the existing logic to ensure new expert registrations are correctly processed, data is stored, roles are assigned, and profiles seamlessly enter the verification workflow.

- [x] 2. Build asset registration approval system (new feature)
  - [x] 2.1 Create database functions for asset registration management
    - Create SQL file with functions for fetching registration requests with pagination and filtering
    - Add function for updating registration status with proper audit logging
    - Create function for extracting asset data from registration JSON for display
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 2.2, 2.3, 2.4_

  - [x] 2.2 Create new asset registration management page for admin
    - Build new page at `/dashboard/management` to display pending asset registration requests
    - Create table component to show registration requests from `registration_requests` table
    - Add filtering by entity_type (FARM/FACTORY) and status
    - Implement search functionality for registration requests
    - _Requirements: 2.2_

  - [x] 2.3 Create asset registration details modal/page
    - Build detailed view component for individual registration requests
    - Parse and display structured registration data from JSON `registration_data` field
    - Show farm/factory information with proper Arabic/English formatting
    - Add image gallery for uploaded asset photos from registration data
    - _Requirements: 2.2, 2.6_

  - [x] 2.4 Implement registration approval/rejection functionality
    - Create approval action buttons using existing `approve_registration_request` function
    - Build rejection form with admin notes field
    - Implement API calls to update registration status and trigger asset creation
    - Add success/error notifications using existing toast system
    - _Requirements: 2.3, 2.4, 2.5, 2.8_

  - [ ] 2.5 Manual testing for asset registration system
    - Test asset registration list display with different filter combinations
    - Test registration details view with various JSON data structures
    - Test approval workflow: approve registration → verify asset creation → check user notification
    - Test rejection workflow: reject registration → verify admin notes → check user notification
    - Test edge cases: malformed JSON data, missing required fields, concurrent approvals
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.8_

- [ ] 3. Implement and Refine Expert Verification Feature
  - [x] 3.1 Refine Expert View (`dashboard/verification-pending`)
    - In the form at `dashboard/verification-pending`, consolidate all document uploads into the 'الوثائق' (Documents) section.
    - Remove document upload sections ('ارفع صورة واضحة لهويتك الوطنية' and 'ارفع الشهادات ذات الصلة') from 'المعلومات الأساسية' and 'الخبرة'.
    - Add clear instructions for required and optional documents, emphasizing clear, authenticated versions.
    - _References: @/.plans/expert_verification_plan.md 

  - [x] 3.2 Implement Backend and Database Enhancements
    - **Resolve Form Submission and Document Upload Errors:**
      - Ensure the form includes proper validation for all fields.
      - Fix drag-and-drop functionality for document uploads.
      - Ensure 'pick a document' works without page reloads and supports multiple files.
      - Verify all form fields and documents are correctly submitted and stored.
    - **Implement Backend and Database Enhancements:**
      - **Fix Form Submission Error:** Add the missing `awards_honors` column to the `expert_profiles` table to resolve `Error: Could not find the 'awards_honors' column of 'expert_profiles' in the schema cache (PGRST204)`.
      - **Optimize Database Schema:** Review `@/supabase/migrations/20251002_public_schema.sql` and `@/.database/db_enhancement/README.md` to ensure all expert-entered data is saved.
      - **Set Up Document Storage:** Configure a Supabase storage bucket for expert verification documents and create a guide for its setup.
    - **Organize SQL Migrations:**
      - Create a new folder at `@/supabase/migrations/verification_fixes`.
      - Place all SQL database edits in this new folder.
      - Include a `README.md` guide in the folder detailing all necessary database modifications (e.g., running scripts, setting up buckets).

  - [x] 3.3 Enhance Admin View for User Management
    - **Context from Recent Modifications:**
      - ✅ Added new fields to `expert_profiles`: `expert_type`, `certifications`, `current_position`, `organization`, `languages_spoken`, `professional_memberships`, `awards_honors`
      - ✅ Expert type differentiation enforced with NOT NULL constraint
      - ✅ Document storage moved to `expert_documents` table
      - ✅ Verification form captures comprehensive expert data
      - ✅ Access control system implemented for pending/approved experts

    - **Update User List (`/dashboard/users`):**
      - **Current State**: Already has tabs for experts/farmers, but missing user type differentiation
      - **Add User Type Column**: Implement `getUserTypeDisplay()` function to show:
        - 'Agriculture Expert' / 'Industrial Expert' (from `expert_profiles.expert_type`)
        - 'Agriculture Client' / 'Industrial Client' (from `client_profiles.client_type`)
      - **Database Query Updates**:
        ```sql
        -- For experts query
        SELECT id, first_name, last_name, email, created_at, role,
               expert_profiles(verification_status, expert_type, bio, years_of_experience, education, expertise_area)
        FROM profiles WHERE role = 'expert'

        -- For clients query
        SELECT id, first_name, last_name, email, created_at, role,
               client_profiles(client_type, farm_name, farm_size_hectares)
        FROM profiles WHERE role IN ('farmer', 'client')
        ```
      - **Files to Modify**: `src/app/dashboard/users/page.tsx`

    - **Refine Expert Details Page (`/users/[id]`):**
      - **Current State**: Shows basic expert info (bio, experience, education) but missing new verification fields
      - **Add All Verification Form Fields**:
        - `expert_type`: Expert Type (Agriculture/Industrial)
        - `expertise_area`: Area of Expertise
        - `certifications`: Professional Certifications
        - `current_position`: Current Position
        - `organization`: Organization/Company
        - `languages_spoken`: Languages Spoken
        - `professional_memberships`: Professional Memberships
        - `awards_honors`: Awards & Honors
      - **Database Query Update**:
        ```sql
        SELECT profiles.*,
               expert_profiles.bio, expert_profiles.years_of_experience, expert_profiles.education,
               expert_profiles.expert_type, expert_profiles.expertise_area, expert_profiles.certifications,
               expert_profiles.current_position, expert_profiles.organization, expert_profiles.languages_spoken,
               expert_profiles.professional_memberships, expert_profiles.awards_honors,
               expert_profiles.verification_status, expert_profiles.verified_at, expert_profiles.verified_by
        FROM profiles
        LEFT JOIN expert_profiles ON profiles.id = expert_profiles.id
        WHERE profiles.id = $1
        ```
      - **Document Display Integration**: Update documents tab to use `expert_documents` table instead of `document_urls`
      - **Files to Modify**: `src/app/dashboard/users/[id]/page.tsx`
      - **Dependencies**: Apply `supabase/migrations/verification_fixes/20251004_add_awards_and_setup_storage.sql` first

  - [ ] 3.4 Fix Expert Approval/Rejection Flows and Email Notifications
    - **Context from Current Issues:**
      - ❌ Approve button failing with error: `Could not find the function public.approve_expert_verification(p_admin_notes, p_expert_id) in the schema cache`
      - ❌ Reject button failing with error: `Could not find the function public.reject_expert_verification(p_admin_notes, p_expert_id, p_rejection_reasons) in the schema cache`
      - ❌ Rejection popup for reason input needs to be removed temporarily
      - ❌ Missing translations for user type column (`userTypes.industrialExpert` etc.)
      - ❌ No email notification system for approval/rejection results

    - **Fix Database Functions (Priority 1):**
      - **Problem**: Missing or incorrectly named database functions
      - **Investigation Steps**:
        1. Check existing functions in `supabase/migrations/20251005_public_schema.sql`
        2. Verify function signatures match frontend calls
        3. Ensure functions are properly created and accessible
      - **Expected Functions**:
        ```sql
        -- Should exist in database
        public.approve_expert_verification(p_expert_id uuid, p_admin_notes text DEFAULT NULL)
        public.reject_expert_verification(p_expert_id uuid, p_admin_notes text DEFAULT NULL, p_rejection_reasons text[] DEFAULT NULL)
        ```
      - **Files to Check/Fix**:
        - `supabase/migrations/20251005_public_schema.sql` 
        - `supabase/migrations/verification_fixes/` directory
      - **Action**: Create missing functions or fix parameter naming mismatch

    - **Fix Frontend Function Calls:**
      - **Current Implementation**: `src/app/dashboard/users/[id]/page.tsx`
      - **Fix Function Parameters**: Ensure frontend calls match database function signatures
      - **Remove Rejection Popup**: Temporarily disable rejection reason input modal
      - **Error Handling**: Add proper error messages and loading states
      - **Code Updates**:
        ```typescript
        // Fix approve function call
        const handleApprove = async () => {
          const { error } = await supabase.rpc('approve_expert_verification', {
            p_expert_id: user.id,
            p_admin_notes: adminNotes || null
          });
        };

        // Fix reject function call (simplified without popup)
        const handleReject = async () => {
          const { error } = await supabase.rpc('reject_expert_verification', {
            p_expert_id: user.id,
            p_admin_notes: adminNotes || null
          });
        };
        ```

    - **Add Missing Translations:**
      - **Update Locale Files**: `src/locales/en.json` and `src/locales/ar.json`
      - **Add User Type Translations**:
        ```json
        // en.json
        "userTypes": {
          "industrialExpert": "Industrial Expert",
          "agricultureExpert": "Agriculture Expert",
          "farmer": "Farmer",
          "industrialClient": "Industrial Client"
        }

        // ar.json  
        "userTypes": {
          "industrialExpert": "خبير صناعي",
          "agricultureExpert": "خبير زراعي", 
          "farmer": "مزارع",
          "industrialClient": "عميل صناعي"
        }
        ```
      - **Files to Update**: 
        - `src/locales/en.json`
        - `src/locales/ar.json`
        - `src/app/dashboard/users/page.tsx` (implement translation usage)

    - **Plan Email Notification System:**
      - **Goal**: Send verification result emails to experts using Resend service
      - **Implementation Plan**:
        1. **Setup Resend SDK**: Add resend package and API key configuration
        2. **Create Email Templates**: Simple approval/rejection email templates
        3. **Create Email API Route**: `/api/send-verification-email`
        4. **Integration**: Call email API after successful approve/reject
      - **Email Template Structure**:
        ```typescript
        // components/email-templates/verification-result.tsx
        interface VerificationEmailProps {
          expertName: string;
          status: 'approved' | 'rejected';
          adminNotes?: string;
        }
        ```
      - **API Route Structure**:
        ```typescript
        // app/api/send-verification-email/route.ts
        export async function POST(request: Request) {
          const { expertEmail, expertName, status, adminNotes } = await request.json();
          
          const { data, error } = await resend.emails.send({
            from: 'AgriDustria <<EMAIL>>',
            to: [expertEmail],
            subject: `Expert Verification ${status === 'approved' ? 'Approved' : 'Rejected'}`,
            react: VerificationResultTemplate({ expertName, status, adminNotes }),
          });
        }
        ```
      - **Required Environment Variables**: `RESEND_API_KEY`
      - **Dependencies**: 
        - Install resend package: `npm install resend`
        - Domain verification on Resend platform
      - **Documentation References**: 
        - `@/.guides/resend_api_docs.md`
        - `@/.guides/resend_NextJs.md`

    - **Implementation Order:**
      1. **Database Function Fix** (Immediate - blocks current functionality)
      2. **Frontend Function Calls Update** (Immediate)
      3. **Remove Rejection Popup** (Immediate)
      4. **Add Missing Translations** (High Priority)
      5. **Email System Setup** (Medium Priority - can be implemented after core flows work)

    - **Testing Requirements:**
      - Test approve flow with database function
      - Test reject flow with database function  
      - Verify translations appear correctly
      - Test email sending (when implemented)
      - Ensure proper error handling and user feedback

  - [ ] 3.5 Enhance Client Management and UI Features
    - **Context from Current State:**
      - ✅ Basic client listing exists at `/dashboard/users?tab=farmers`
      - ❌ Missing translations for table columns and values
      - ❌ Client detail pages lack RTL support and proper structure
      - ❌ Missing profile image display functionality
      - ❌ Assets tab needs translation and RTL support

    - **Implement Client Table Translations:**
      - **Target**: `/dashboard/users?tab=farmers` table
      - **Translation Requirements**:
        ```json
        // Add to locale files
        "usersPage": {
          "clientType": "Client Type",
          "viewDetails": "View Details",
          "userType": "User Type",
          "name": "Name", 
          "email": "Email",
          "registrationDate": "Registration Date",
          "status": "Status"
        }
        ```
      - **Value Translations**: Use existing `userTypes` translations for records
      - **Files to Update**:
        - `src/locales/en.json` and `src/locales/ar.json`
        - `src/app/dashboard/users/page.tsx`

    - **Enhance Client Detail Page RTL Support:**
      - **Target**: `/dashboard/users/[id]` for client users
      - **RTL Implementation**: Follow patterns from `@/.guides/rtl-implementation-guide.md`
      - **Key Components to Update**:
        ```tsx
        // Personal details cards with RTL support
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-left rtl:text-right">
              <User className="h-5 w-5" />
              <span>{t("personalDetails")}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="text-left rtl:text-right">
                <label className="text-sm font-medium text-muted-foreground">
                  {t("fieldLabel")}
                </label>
                <p className="text-sm">{fieldValue}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        ```
      - **Files to Update**: `src/app/dashboard/users/[id]/page.tsx`

    - **Implement Profile Image Display:**
      - **Data Source**: `public.profiles.avatar_url` field
      - **Implementation Structure**:
        ```tsx
        // Add to client detail page
        <div className="flex items-center gap-3 text-left rtl:text-right">
          <Avatar className="h-12 w-12">
            <AvatarImage src={user.avatar_url} alt={`${user.first_name} ${user.last_name}`} />
            <AvatarFallback>
              {user.first_name?.[0]}{user.last_name?.[0]}
            </AvatarFallback>
          </Avatar>
          <div className="text-left rtl:text-right">
            <p className="font-medium">{user.first_name} {user.last_name}</p>
            <p className="text-sm text-muted-foreground">{user.email}</p>
          </div>
        </div>
        ```
      - **Database Query Update**: Ensure `avatar_url` is included in user fetch query
      - **Files to Update**: `src/app/dashboard/users/[id]/page.tsx`

    - **Add Assets Tab Translation and RTL Support:**
      - **Current State**: Assets tab exists but lacks translations and RTL
      - **Translation Keys Needed**:
        ```json
        "assetsTab": {
          "title": "Linked Assets",
          "assetName": "Asset Name",
          "assetType": "Asset Type", 
          "location": "Location",
          "condition": "Condition",
          "noAssets": "No assets found",
          "addAsset": "Add Asset"
        }
        ```
      - **RTL Implementation**:
        ```tsx
        <TabsContent value="assets">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-left rtl:text-right">
                <Package className="h-5 w-5" />
                <span>{t("assetsTab.title")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {assets.map(asset => (
                  <div key={asset.id} className="border rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left rtl:text-right">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          {t("assetsTab.assetName")}
                        </label>
                        <p className="text-sm">{asset.name}</p>
                      </div>
                      {/* Additional asset fields with RTL support */}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        ```
      - **Files to Update**: 
        - `src/components/admin/linked-assets-tab.tsx` (if exists)
        - `src/app/dashboard/users/[id]/page.tsx`

    - **Enhanced Client Details Structure:**
      - **Complete Tab Implementation**:
        ```typescript
        const ClientDetailsPage = ({ clientId }) => {
          return (
            <div dir={isRTL ? 'rtl' : 'ltr'}>
              <Tabs>
                <TabsList>
                  <TabsTrigger value="profile">{t("tabs.profile")}</TabsTrigger>
                  <TabsTrigger value="assets">{t("tabs.assets")}</TabsTrigger>
                  <TabsTrigger value="consultations">{t("tabs.consultations")}</TabsTrigger>
                </TabsList>

                <TabsContent value="profile">
                  <ClientProfileTab clientId={clientId} />
                </TabsContent>

                <TabsContent value="assets">
                  <LinkedAssetsTab clientId={clientId} />
                </TabsContent>

                <TabsContent value="consultations">
                  <ClientConsultationsTab clientId={clientId} />
                </TabsContent>
              </Tabs>
            </div>
          );
        };
        ```

    - **Implementation Priority:**
      1. **Client Table Translations** (High Priority)
      2. **Profile Image Display** (High Priority)
      3. **RTL Support for Client Detail Cards** (High Priority)
      4. **Assets Tab Translation and RTL** (Medium Priority)
      5. **Complete Tab Structure Enhancement** (Medium Priority)

    - **Technical Considerations:**
      - **RTL Guidelines**: Follow `@/.guides/rtl-implementation-guide.md` patterns exactly
      - **Performance**: Optimize image loading with proper fallbacks
      - **Accessibility**: Ensure proper ARIA labels for RTL content
      - **Testing**: Test language switching and RTL layout integrity

  - [ ] 3.6 Automated Testing for Verification and User Management
    - Update and create database tests for the refined verification workflow (`tests/expert-verification/database/verification-workflow.test.sql`).
    - Develop backend API tests for the updated endpoints (`tests/expert-verification/backend/verification-api.test.ts`).
    - Write frontend component tests for the new UI elements (`tests/expert-verification/frontend/ExpertVerificationForm.test.tsx`).
    - Create E2E tests covering the complete flow from registration to approval/rejection and client view (`tests/e2e/expert-verification-and-user-management.spec.ts`).

- [ ] 4. Enhance existing consultation management system
  - [ ] 4.1 Create database functions for consultation management
    - Create SQL file with functions for consultation assignment and status management
    - Add function for fetching available experts based on expertise and availability
    - Create function for consultation workflow state transitions with proper validation
    - Add function for consultation resolution with recommendation storage
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 4.1, 4.2, 4.5, 4.6_

  - [ ] 4.2 Create admin consultation assignment interface (new feature)
    - Build new admin consultation queue page to replace dummy data in existing requests page
    - Connect to real consultation data from database consultations table
    - Create expert selection dropdown with availability status from profiles table
    - Implement consultation assignment API functionality using new database functions
    - _Requirements: 4.1, 4.2_

  - [ ] 4.3 Enhance existing expert consultation dashboard
    - Modify existing `/dashboard/requests` page to show only assigned consultations for experts
    - Update existing consultation cards to display real data from consultations table
    - Enhance existing status indicators with proper consultation workflow states
    - Add consultation details modal with user and asset context information
    - _Requirements: 4.3, 4.5, 4.8_

  - [ ] 4.4 Enhance existing consultation resolution workflow
    - Modify existing consultation detail pages to include resolution functionality
    - Update existing consultation status update system to handle resolution workflow
    - Add final recommendation text editor to existing consultation interface
    - Enhance existing consultation archive system in consultations page
    - _Requirements: 4.5, 4.6_

  - [ ] 4.5 Manual testing for consultation management system
    - Test admin consultation queue display and expert assignment functionality
    - Test expert consultation dashboard with assigned consultations only
    - Test consultation assignment: assign to expert → verify expert notification → check status update
    - Test consultation resolution: complete consultation → verify recommendation saved → check user notification
    - Test consultation workflow: pending → assigned → in progress → resolved states
    - Test edge cases: expert unavailability, concurrent assignments, invalid status transitions
    - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.8_

- [ ] 5. Build real-time chat system for consultations (new feature)
  - [ ] 5.1 Create database functions for chat system
    - Create SQL file with functions for consultation message management
    - Add function for real-time message insertion with proper validation
    - Create function for message read status tracking and updates
    - Add function for file attachment handling in chat messages
    - Create triggers for real-time message notifications
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 11.1, 11.2, 11.3, 11.5_

  - [ ] 5.2 Create chat interface components for consultation pages
    - Build new chat component to integrate with existing consultation detail pages
    - Create message list component with proper RTL support matching existing UI
    - Build message input component with file upload using existing upload patterns
    - Add typing indicator functionality using Supabase real-time
    - _Requirements: 11.1, 11.4, 11.7, 11.8_

  - [ ] 5.3 Implement real-time messaging functionality
    - Set up Supabase real-time subscriptions for consultation_messages table
    - Create message sending API endpoints following existing API patterns
    - Implement message delivery confirmation using existing notification system
    - Add offline message queuing using existing error handling patterns
    - _Requirements: 11.2, 11.3, 11.6_

  - [ ] 5.4 Add file sharing capabilities to chat
    - Integrate with existing file upload system for image sharing in chat
    - Create file attachment preview using existing image handling components
    - Add image zoom functionality matching existing image gallery patterns
    - Build file validation using existing security measures
    - _Requirements: 11.5_

  - [ ] 5.5 Manual testing for real-time chat system
    - Test chat interface integration with consultation pages
    - Test real-time messaging: send message → verify instant delivery → check read status
    - Test file sharing: upload image → verify preview → test zoom functionality
    - Test typing indicators and online/offline status
    - Test chat in both Arabic and English interfaces with RTL support
    - Test edge cases: network disconnection, large file uploads, concurrent messaging
    - _Requirements: 11.1, 11.2, 11.3, 11.4, 11.5, 11.6, 11.7, 11.8_

- [ ] 6. Enhance existing notification system
  - [ ] 6.1 Create database functions for notification system
    - Create SQL file with functions for notification management and delivery tracking
    - Add function for creating notifications based on system events (triggers)
    - Create function for push notification payload generation for mobile app
    - Add function for email notification queue management
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 12.1, 12.2, 12.4, 12.5_

  - [ ] 6.2 Build on existing toast notification infrastructure
    - Extend existing toast system in `use-toast.ts` for real-time notifications
    - Create notification badge components for sidebar navigation
    - Implement notification persistence using existing local storage patterns
    - Add notification sound and visual indicators to existing UI components
    - _Requirements: 12.1, 12.2, 12.3, 12.5_

  - [ ] 6.3 Build email notification system (new feature)
    - Create email templates for expert verification status updates
    - Implement email sending functionality via Supabase Edge Functions
    - Integrate with existing user management system for email preferences
    - Build email delivery status tracking in existing admin interface
    - _Requirements: 12.4_

  - [ ] 6.4 Implement push notification integration (new feature)
    - Set up push notification service integration for mobile app communication
    - Create notification payload formatting for different event types
    - Implement notification retry mechanism using existing error handling
    - Add notification preferences to existing user profile management
    - _Requirements: 12.6, 12.7, 12.8_

  - [ ] 6.5 Manual testing for notification system
    - Test in-app notifications: trigger event → verify toast display → check persistence
    - Test email notifications: expert verification status change → verify email sent → check delivery status
    - Test push notifications: consultation assignment → verify mobile notification → check payload format
    - Test notification preferences: update settings → verify respected in delivery
    - Test notification badges and counters in sidebar navigation
    - Test edge cases: email delivery failures, push notification service downtime, notification flooding
    - _Requirements: 12.1, 12.2, 12.3, 12.4, 12.5, 12.6, 12.7, 12.8_

- [ ] 7. Enhance existing analytics dashboard
  - [ ] 7.1 Create database functions for analytics data
    - Create SQL file with functions for dashboard analytics and metrics calculation
    - Add function for user registration trends over time with date filtering
    - Create function for consultation metrics and status distribution
    - Add function for expert performance and activity statistics
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 8.1, 8.2, 8.3, 8.4_

  - [ ] 7.2 Replace dummy data in existing admin dashboard
    - Update existing AdminView component to show real metrics from database
    - Modify existing DashboardMetrics component to fetch actual user, expert, and asset counts
    - Enhance existing dashboard cards with real-time data from profiles and assets tables
    - Add active consultations and pending requests counters using existing consultation system
    - _Requirements: 8.1, 8.2, 8.3, 8.4_

  - [ ] 7.3 Enhance existing chart components
    - Update existing DashboardChart component to display real consultation data
    - Add time-series charts for consultation trends using existing chart infrastructure
    - Enhance existing responsive layouts for different screen sizes
    - Integrate with existing RTL support for Arabic interface
    - _Requirements: 8.3, 8.6, 8.7_

  - [ ] 7.4 Manual testing for analytics dashboard
    - Test dashboard metrics display with real data from database
    - Test chart functionality: select date range → verify data updates → check chart rendering
    - Test analytics in both Arabic and English interfaces
    - Test responsive layout on different screen sizes
    - Test data refresh and real-time updates when new data is added
    - Test edge cases: no data periods, large datasets, date range edge cases
    - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.6, 8.7_

- [ ] 8. Development of Database triggers/functions for core features
  - [ ] 8.1 Implement database triggers/functions for error handling
    - Develop triggers/functions to ensure robust error handling for critical operations.
    - Focus on ensuring robust error handling and data integrity across various core features.
    - Ensure smooth application functionality by preventing common database-related issues.
    - _Orientation: This involves writing SQL functions and triggers within the Supabase database to automate error checks and data consistency for various core features, excluding specific user profile creation which is now covered under task 1.1._

- [ ] 9. Develop admin invitation feature
  - [ ] 9.1 Create admin invitation functionality for admin view
    - Design and implement a feature allowing existing administrators to invite new administrators.
    - This includes a UI in the admin dashboard for sending invitations.
    - Implement the logic for sending invitations using the `auth.admin.inviteUserByEmail()` function.
    - _Orientation: This will involve creating new API endpoints, UI components, and integrating with Supabase Auth's invitation capabilities._
  - [ ] 9.2 Handle admin invitation acceptance and role assignment
    - Integrate the admin invitation feature into the application's user management flow.
    - This involves handling the invitation acceptance process and assigning appropriate roles.
    - _Orientation: This task focuses on the backend and frontend logic to support the new user types and invitation flows, ensuring they are seamlessly integrated into the application's existing authentication and authorization systems._

- [ ] 10. Enhance existing security and data protection
  - [ ] 10.1 Strengthen existing input validation
    - Enhance existing form validation in user management and consultation forms
    - Review and strengthen existing API endpoint validation
    - Improve existing file upload validation in avatar and document upload systems
    - Enhance existing XSS prevention measures in user-generated content display
    - _Requirements: 9.3, 9.4_

  - [ ] 10.2 Strengthen existing authentication security
    - Enhance existing session management in AuthProvider component
    - Review existing Supabase Auth configuration for rate limiting
    - Improve existing password reset flow in forgot-password page
    - Add account lockout mechanism to existing authentication system
    - _Requirements: 9.1, 9.5_

  - [ ] 10.3 Review and enhance existing database security
    - Review existing Row Level Security (RLS) policies in database schema
    - Enhance existing audit logging in account_activation_history table
    - Review data encryption for sensitive fields in existing profiles table
    - Improve existing data deletion procedures with proper audit trails
    - _Requirements: 9.2, 9.7_

- [ ] 11. Enhance existing multi-language support
  - [ ] 11.1 Complete existing Arabic/English interface support
    - Complete missing translations in existing i18n system (ar.json, en.json)
    - Enhance existing RTL layout in DashboardLayout and components
    - Improve existing language-specific formatting in date/number displays
    - Enhance existing language preference persistence in LocaleProvider
    - _Requirements: 7.1, 7.2, 7.6_

  - [ ] 11.2 Enhance existing content localization system
    - Extend existing translation system for admin-generated content
    - Create language-specific templates for new notification system
    - Enhance existing language detection in i18n-provider
    - Improve existing fallback mechanisms for missing translations
    - _Requirements: 7.3, 7.4, 7.5_

  - [ ] 11.3 Manual testing for multi-language support
    - Test language switching: switch Arabic ↔ English → verify UI updates → check RTL/LTR layout
    - Test Arabic interface: navigate all pages → verify RTL layout → check text alignment and icons
    - Test English interface: navigate all pages → verify LTR layout → check proper text rendering
    - Test language persistence: switch language → refresh page → verify language maintained
    - Test missing translations: check fallback to English → verify no broken UI elements
    - Test edge cases: mixed content languages, special characters, long text strings
    - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6_

- [ ] 12. Enhance existing error handling and user experience
  - [ ] 12.1 Improve existing error boundaries and error handling
    - Enhance existing ErrorBoundary and ChunkErrorBoundary components
    - Improve existing error pages and user-friendly error messages
    - Enhance existing error logging using existing patterns
    - Add graceful degradation for non-critical features in existing components
    - _Requirements: 9.5_

  - [ ] 12.2 Enhance existing loading states and offline handling
    - Improve existing loading skeletons in dashboard and user management pages
    - Enhance existing offline detection and user notification systems
    - Improve existing retry mechanisms for failed API calls in Supabase client
    - Add optimistic updates to existing user management and consultation systems
    - _Requirements: 11.6_

- [ ] 13. Enhance integration with mobile application APIs
  - [ ] 13.1 Review and enhance existing API endpoints
    - Review existing Supabase API configuration for mobile app communication
    - Enhance existing authentication and authorization for mobile requests
    - Review existing data synchronization patterns for offline mobile functionality
    - Add API versioning support to existing Supabase configuration
    - _Requirements: 13.1, 13.3, 13.7_

  - [ ] 13.2 Enhance existing real-time synchronization
    - Review existing Supabase real-time configuration for web and mobile sync
    - Enhance existing conflict resolution in database functions
    - Integrate push notification delivery with existing notification system
    - Review and optimize existing API performance patterns
    - _Requirements: 13.2, 13.4, 13.5_

- [ ] 14. Testing and quality assurance for existing and new features
  - [ ] 14.1 Write tests for existing and new functionality
    - Create unit tests for existing utility functions in utils directory
    - Build component tests for existing and new UI components
    - Test existing authentication flow and role-based access
    - Add database function testing for existing and new database functions
    - _Requirements: All requirements validation_

  - [ ] 14.2 Implement integration testing for the complete system
    - Test existing and new API endpoint integrations
    - Build real-time functionality testing for new chat system
    - Test existing and enhanced role-based access control
    - Add cross-browser compatibility testing for existing RTL support
    - _Requirements: All requirements validation_

- [ ] 15. Performance optimization and deployment preparation
  - [ ] 15.1 Fix critical web portal loading performance issues
    - Investigate and fix 32.4s initial startup time and 19.5s instrumentation compilation
    - Remove excessive middleware cookie logging that's causing performance degradation
    - Fix Supabase realtime-js critical dependency warning affecting bundle size
    - Optimize middleware.ts to reduce redundant cookie checks (currently checking same cookies 5x per request)
    - Remove or optimize deprecated util._extend usage causing Node.js warnings
    - Implement proper error handling for "user aborted request" retries
    - Add performance monitoring to identify bottlenecks in page load (currently 60797ms for GET /)
    - _Requirements: System performance and scalability_

  - [ ] 15.2 Optimize existing application performance
    - Review existing code splitting and lazy loading in dashboard components
    - Optimize existing database queries and review indexing in schema
    - Enhance existing image optimization in avatar and file upload systems
    - Review existing performance monitoring patterns
    - _Requirements: System performance and scalability_

  - [ ] 15.3 Enhance existing deployment configuration
    - Review existing environment variables and secrets configuration
    - Enhance existing Netlify deployment configuration
    - Review existing database migration patterns and backup procedures
    - Improve existing monitoring and logging infrastructure
    - _Requirements: System reliability and maintenance_

- [ ] 16. Fix RTL (Right-to-Left) language support issues
  - [ ] 16.1 Fix RTL issues on main landing page
    - Audit and fix RTL layout problems on the main landing page
    - Add language toggle buttons for Arabic/English switching on landing page
    - Ensure proper text alignment, icon positioning, and layout flow for Arabic interface
    - Test navigation, buttons, and interactive elements in RTL mode
    - Verify responsive design works correctly in both RTL and LTR modes
    - _Requirements: 7.1, 7.2, 7.6_

  - [ ] 16.2 Complete RTL audit and fixes for main app interface
    - Conduct comprehensive RTL audit of all dashboard pages and components
    - Fix remaining RTL layout issues in sidebar navigation, forms, and data tables
    - Ensure proper RTL support in modals, dropdowns, and overlay components
    - Fix text alignment and spacing issues in Arabic interface
    - Verify icon positioning and directional elements work correctly in RTL
    - Test all interactive elements (buttons, inputs, selects) in Arabic mode
    - _Requirements: 7.1, 7.2, 7.6_

  - [ ] 16.3 Manual testing for RTL language support
    - Test complete user journey in Arabic: landing page → registration → dashboard navigation
    - Verify language toggle functionality works seamlessly throughout the application
    - Test all forms and input fields with Arabic text input and RTL layout
    - Verify data tables, charts, and complex layouts render correctly in RTL
    - Test responsive behavior on different screen sizes in both Arabic and English
    - Check for any text overflow, alignment issues, or broken layouts in RTL mode
    - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6_

- [x] 17. Implement database enhancements
  - [x] 17.1 Apply database migration scripts
      - Execute the `enhancement_migration.sql` script in the Supabase dashboard.
      - Execute the `refactor_schema.sql` script in the Supabase dashboard.
      - Verify that the schema changes have been applied correctly.
    - [x] 17.2 Update frontend to use new database logic
      - Update the expert verification flow to use the `expert_profiles.verification_status` column.
      - Update the asset registration flow to use the new `create_asset_from_request` function.
      - Update the consultation management system to use the new `consultation_participants` table.
      - Update the user management system to use the new `client_profiles` table.