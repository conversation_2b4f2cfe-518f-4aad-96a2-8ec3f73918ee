{"mcpServers": {"supabase": {"command": "uvx", "args": ["mcp-server-supabase", "--read-only", "--project-ref=jrhbvcjwxvyrxrmgjfbu"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}, "TestSprite": {"command": "npx", "args": ["@testsprite/testsprite-mcp@latest"], "env": {"API_KEY": "sk-user-ipqsMlYaQ6r6v_O-Kds0LlfBS33weuR-rFGaNCJSBeR_zLOZ7BLqUf2c-7fkI_DLevPUcFmF-LabmeITsZzEKrjeTQT2zorzxCwD8UqmCJZQfurKSfW_ejD3Y7q_uJmUqas"}}}}