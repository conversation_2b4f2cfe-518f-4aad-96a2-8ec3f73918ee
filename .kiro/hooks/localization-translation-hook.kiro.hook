{"enabled": true, "name": "Localization Translation Monitor", "description": "Monitor changes to localization files (JSON, YAML) and automatically generate translations for Arabic and English target languages while maintaining context and locale-specific conventions", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/locales/*.json", "src/locales/*.yaml", "src/locales/*.yml", "locales/*.json", "locales/*.yaml", "locales/*.yml", "**/*locales*.json", "**/*locales*.yaml", "**/*i18n*.json", "**/*i18n*.yaml"]}, "then": {"type": "askAgent", "prompt": "A localization file has been modified. Please analyze the changes and:\n\n1. Identify any new or modified text content that needs translation\n2. Generate accurate translations for both Arabic and English languages\n3. Ensure translations maintain proper context and meaning\n4. Apply locale-specific conventions (RTL for Arabic, proper formatting)\n5. Preserve any existing translation keys and structure\n6. Output the complete updated translation files for both languages\n\nFocus on maintaining consistency with existing translations and ensuring cultural appropriateness for both Arabic and English audiences."}}