-- Expert Verification Enhancement Migration - FIXED VERSION
-- This file enhances the existing expert verification system with comprehensive management functions
-- Created for task 3.1: Create database functions for expert verification management
-- FIXES: Column type conversion issues with existing verification_status column

-- ==========================================
-- 1. CREATE EXPERT VERIFICATION STATUS ENUM
-- ==========================================

-- Create enum for expert verification status if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'expert_verification_status') THEN
        CREATE TYPE public.expert_verification_status AS ENUM (
            'pending',
            'under_review', 
            'approved',
            'rejected',
            'suspended',
            'resubmission_required'
        );
        RAISE NOTICE 'Created expert_verification_status enum type';
    ELSE
        RAISE NOTICE 'expert_verification_status enum type already exists';
    END IF;
END $$;

-- ==========================================
-- 2. CREATE EXPERT DOCUMENTS TABLE
-- ==========================================

-- Table to store expert verification documents
CREATE TABLE IF NOT EXISTS public.expert_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    expert_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    document_type VARCHAR(50) NOT NULL, -- 'qualification', 'certification', 'experience', 'id_document'
    document_url TEXT NOT NULL, -- URL to stored document
    document_name VARCHAR(255) NOT NULL,
    file_size INTEGER,
    file_type VARCHAR(50),
    upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    verification_status expert_verification_status DEFAULT 'pending',
    admin_notes TEXT,
    reviewed_by UUID REFERENCES public.profiles(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on expert_documents
ALTER TABLE public.expert_documents ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Experts can view own documents" ON public.expert_documents;
DROP POLICY IF EXISTS "Experts can insert own documents" ON public.expert_documents;
DROP POLICY IF EXISTS "Experts can update own pending documents" ON public.expert_documents;
DROP POLICY IF EXISTS "Admins can view all expert documents" ON public.expert_documents;

-- RLS Policies for expert_documents
CREATE POLICY "Experts can view own documents" ON public.expert_documents
    FOR SELECT USING (auth.uid() = expert_id);

CREATE POLICY "Experts can insert own documents" ON public.expert_documents
    FOR INSERT WITH CHECK (auth.uid() = expert_id);

CREATE POLICY "Experts can update own pending documents" ON public.expert_documents
    FOR UPDATE USING (auth.uid() = expert_id AND verification_status = 'pending');

CREATE POLICY "Admins can view all expert documents" ON public.expert_documents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

RAISE NOTICE 'Created expert_documents table with RLS policies';

-- ==========================================
-- 3. CREATE EXPERT VERIFICATION HISTORY TABLE
-- ==========================================

-- Table to track expert verification status changes
CREATE TABLE IF NOT EXISTS public.expert_verification_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    expert_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    previous_status expert_verification_status,
    new_status expert_verification_status NOT NULL,
    changed_by UUID REFERENCES public.profiles(id),
    admin_notes TEXT,
    rejection_reasons TEXT[], -- Array of rejection reasons
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on expert_verification_history
ALTER TABLE public.expert_verification_history ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Experts can view own verification history" ON public.expert_verification_history;
DROP POLICY IF EXISTS "Admins can view all verification history" ON public.expert_verification_history;
DROP POLICY IF EXISTS "Admins can insert verification history" ON public.expert_verification_history;

-- RLS Policies for expert_verification_history
CREATE POLICY "Experts can view own verification history" ON public.expert_verification_history
    FOR SELECT USING (auth.uid() = expert_id);

CREATE POLICY "Admins can view all verification history" ON public.expert_verification_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can insert verification history" ON public.expert_verification_history
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

RAISE NOTICE 'Created expert_verification_history table with RLS policies';

-- ==========================================
-- 4. FIX EXISTING EXPERT_PROFILES TABLE
-- ==========================================

-- Handle the verification_status column conversion properly
DO $$
DECLARE
    column_exists BOOLEAN;
    column_type TEXT;
BEGIN
    -- Check if verification_status column exists and get its type
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'expert_profiles' 
        AND column_name = 'verification_status'
        AND table_schema = 'public'
    ), data_type INTO column_exists, column_type
    FROM information_schema.columns 
    WHERE table_name = 'expert_profiles' 
    AND column_name = 'verification_status'
    AND table_schema = 'public';

    IF column_exists THEN
        RAISE NOTICE 'Found existing verification_status column with type: %', column_type;
        
        -- First, normalize existing data to match enum values (using string values)
        UPDATE public.expert_profiles 
        SET verification_status = CASE 
            WHEN verification_status = 'verified' THEN 'approved'
            WHEN verification_status = 'unverified' THEN 'pending'
            WHEN verification_status IN ('pending', 'approved', 'rejected') THEN verification_status
            WHEN verification_status IS NULL THEN 'pending'
            ELSE 'pending'  -- Default for any other values
        END
        WHERE verification_status IS NOT NULL OR verification_status IS NULL;
        
        RAISE NOTICE 'Normalized existing verification_status data';
        
        -- Drop the default constraint if it exists
        ALTER TABLE public.expert_profiles ALTER COLUMN verification_status DROP DEFAULT;
        
        -- Convert the column type using explicit casting
        ALTER TABLE public.expert_profiles 
        ALTER COLUMN verification_status TYPE expert_verification_status 
        USING verification_status::expert_verification_status;
        
        -- Set the new default
        ALTER TABLE public.expert_profiles 
        ALTER COLUMN verification_status SET DEFAULT 'pending'::expert_verification_status;
        
        RAISE NOTICE 'Converted verification_status column to expert_verification_status enum';
    ELSE
        -- Add the column if it doesn't exist
        ALTER TABLE public.expert_profiles 
        ADD COLUMN verification_status expert_verification_status DEFAULT 'pending';
        
        RAISE NOTICE 'Added verification_status column to expert_profiles';
    END IF;
END $$;

-- Add other verification-related columns to expert_profiles if not exists
DO $$
BEGIN
    -- Add verification documents required flag
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'expert_profiles' 
        AND column_name = 'documents_required'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.expert_profiles 
        ADD COLUMN documents_required BOOLEAN DEFAULT true;
        RAISE NOTICE 'Added documents_required column to expert_profiles';
    END IF;

    -- Add verification completion date
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'expert_profiles' 
        AND column_name = 'verified_at'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.expert_profiles 
        ADD COLUMN verified_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added verified_at column to expert_profiles';
    END IF;

    -- Add verified by admin reference
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'expert_profiles' 
        AND column_name = 'verified_by'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.expert_profiles 
        ADD COLUMN verified_by UUID REFERENCES public.profiles(id);
        RAISE NOTICE 'Added verified_by column to expert_profiles';
    END IF;
END $$;

-- ==========================================
-- 5. EXPERT VERIFICATION MANAGEMENT FUNCTIONS
-- ==========================================

-- Function to update expert verification status
CREATE OR REPLACE FUNCTION public.update_expert_verification_status(
    p_expert_id UUID,
    p_new_status expert_verification_status,
    p_admin_notes TEXT DEFAULT NULL,
    p_rejection_reasons TEXT[] DEFAULT NULL
) RETURNS JSONB
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_current_status expert_verification_status;
    v_admin_id UUID;
    v_result JSONB;
BEGIN
    -- Get current admin user ID
    v_admin_id := auth.uid();
    
    -- Verify admin permissions
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = v_admin_id AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Unauthorized: Only admins can update verification status';
    END IF;

    -- Get current verification status
    SELECT ep.verification_status INTO v_current_status
    FROM public.expert_profiles ep
    WHERE ep.id = p_expert_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Expert profile not found';
    END IF;

    -- Update expert_profiles verification status
    UPDATE public.expert_profiles 
    SET 
        verification_status = p_new_status,
        verified_at = CASE WHEN p_new_status = 'approved' THEN NOW() ELSE NULL END,
        verified_by = CASE WHEN p_new_status = 'approved' THEN v_admin_id ELSE NULL END,
        updated_at = NOW()
    WHERE id = p_expert_id;

    -- Update main profiles table account_activated based on verification status
    UPDATE public.profiles 
    SET 
        account_activated = CASE WHEN p_new_status = 'approved' THEN true ELSE false END,
        updated_at = NOW()
    WHERE id = p_expert_id;

    -- Log status change in history
    INSERT INTO public.expert_verification_history (
        expert_id,
        previous_status,
        new_status,
        changed_by,
        admin_notes,
        rejection_reasons
    ) VALUES (
        p_expert_id,
        v_current_status,
        p_new_status,
        v_admin_id,
        p_admin_notes,
        p_rejection_reasons
    );

    -- Update document statuses if approved
    IF p_new_status = 'approved' THEN
        UPDATE public.expert_documents 
        SET 
            verification_status = 'approved',
            reviewed_by = v_admin_id,
            reviewed_at = NOW()
        WHERE expert_id = p_expert_id AND verification_status = 'pending';
    END IF;

    v_result := jsonb_build_object(
        'success', true,
        'expert_id', p_expert_id,
        'previous_status', v_current_status,
        'new_status', p_new_status,
        'message', 'Expert verification status updated successfully'
    );

    RETURN v_result;
END;
$$;

-- Function to check expert verification eligibility for consultation access
CREATE OR REPLACE FUNCTION public.check_expert_consultation_access(
    p_expert_id UUID
) RETURNS BOOLEAN
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_verification_status expert_verification_status;
    v_account_activated BOOLEAN;
BEGIN
    -- Get expert verification status and account activation
    SELECT 
        ep.verification_status,
        p.account_activated
    INTO 
        v_verification_status,
        v_account_activated
    FROM public.expert_profiles ep
    JOIN public.profiles p ON p.id = ep.id
    WHERE ep.id = p_expert_id;

    -- Return true only if verified and account is activated
    RETURN (v_verification_status = 'approved' AND v_account_activated = true);
END;
$$;

-- Function to get expert verification details
CREATE OR REPLACE FUNCTION public.get_expert_verification_details(
    p_expert_id UUID
) RETURNS JSONB
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_result JSONB;
    v_documents JSONB;
    v_history JSONB;
BEGIN
    -- Build expert verification details
    SELECT jsonb_build_object(
        'expert_id', p.id,
        'full_name', p.first_name || ' ' || p.last_name,
        'email', p.email,
        'verification_status', ep.verification_status,
        'account_activated', p.account_activated,
        'expertise_area', ep.expertise_area,
        'qualifications', ep.qualifications,
        'years_of_experience', ep.years_of_experience,
        'education', ep.education,
        'bio', ep.bio,
        'verified_at', ep.verified_at,
        'verified_by', ep.verified_by,
        'documents_required', ep.documents_required,
        'created_at', ep.created_at,
        'updated_at', ep.updated_at
    ) INTO v_result
    FROM public.profiles p
    JOIN public.expert_profiles ep ON ep.id = p.id
    WHERE p.id = p_expert_id;

    IF v_result IS NULL THEN
        RAISE EXCEPTION 'Expert not found';
    END IF;

    -- Get documents
    SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
            'id', ed.id,
            'document_type', ed.document_type,
            'document_name', ed.document_name,
            'document_url', ed.document_url,
            'file_size', ed.file_size,
            'file_type', ed.file_type,
            'verification_status', ed.verification_status,
            'admin_notes', ed.admin_notes,
            'upload_date', ed.upload_date,
            'reviewed_at', ed.reviewed_at
        )
    ), '[]'::jsonb) INTO v_documents
    FROM public.expert_documents ed
    WHERE ed.expert_id = p_expert_id;

    -- Get verification history
    SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
            'id', evh.id,
            'previous_status', evh.previous_status,
            'new_status', evh.new_status,
            'admin_notes', evh.admin_notes,
            'rejection_reasons', evh.rejection_reasons,
            'created_at', evh.created_at,
            'changed_by', evh.changed_by
        ) ORDER BY evh.created_at DESC
    ), '[]'::jsonb) INTO v_history
    FROM public.expert_verification_history evh
    WHERE evh.expert_id = p_expert_id;

    -- Add documents and history to result
    v_result := v_result || jsonb_build_object(
        'documents', v_documents,
        'verification_history', v_history
    );

    RETURN v_result;
END;
$$;

-- Function to get experts pending verification (for admin queue)
CREATE OR REPLACE FUNCTION public.get_experts_pending_verification()
RETURNS TABLE (
    expert_id UUID,
    full_name TEXT,
    email TEXT,
    verification_status expert_verification_status,
    expertise_area TEXT,
    years_of_experience INTEGER,
    documents_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
    -- Verify admin permissions
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Unauthorized: Only admins can view verification queue';
    END IF;

    RETURN QUERY
    SELECT 
        p.id as expert_id,
        (p.first_name || ' ' || p.last_name) as full_name,
        p.email,
        ep.verification_status,
        ep.expertise_area,
        ep.years_of_experience,
        COALESCE(doc_count.count, 0)::INTEGER as documents_count,
        ep.created_at,
        ep.updated_at
    FROM public.profiles p
    JOIN public.expert_profiles ep ON ep.id = p.id
    LEFT JOIN (
        SELECT 
            expert_id, 
            COUNT(*)::INTEGER as count
        FROM public.expert_documents 
        GROUP BY expert_id
    ) doc_count ON doc_count.expert_id = p.id
    WHERE p.role = 'expert'
    AND ep.verification_status IN ('pending', 'under_review', 'resubmission_required')
    ORDER BY ep.created_at ASC;
END;
$$;

-- ==========================================
-- 6. ACCESS CONTROL TRIGGERS
-- ==========================================

-- Function to block unverified experts from consultation features
CREATE OR REPLACE FUNCTION public.validate_expert_consultation_access()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Only apply to consultation-related operations by experts
    IF NEW.expert_id IS NOT NULL THEN
        -- Check if expert has consultation access
        IF NOT public.check_expert_consultation_access(NEW.expert_id) THEN
            RAISE EXCEPTION 'Access denied: Expert verification required for consultation features';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Note: Consultation access trigger will be applied when consultations table is created
-- CREATE TRIGGER validate_expert_consultation_access_trigger
--     BEFORE INSERT OR UPDATE ON public.consultations
--     FOR EACH ROW
--     EXECUTE FUNCTION public.validate_expert_consultation_access();

-- ==========================================
-- 7. HELPER FUNCTIONS
-- ==========================================

-- Function to upload expert verification document
CREATE OR REPLACE FUNCTION public.upload_expert_verification_document(
    p_document_type VARCHAR(50),
    p_document_url TEXT,
    p_document_name VARCHAR(255),
    p_file_size INTEGER DEFAULT NULL,
    p_file_type VARCHAR(50) DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_expert_id UUID;
    v_document_id UUID;
BEGIN
    -- Get current user ID
    v_expert_id := auth.uid();
    
    -- Verify user is an expert
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = v_expert_id AND role = 'expert'
    ) THEN
        RAISE EXCEPTION 'Unauthorized: Only experts can upload verification documents';
    END IF;

    -- Insert document record
    INSERT INTO public.expert_documents (
        expert_id,
        document_type,
        document_url,
        document_name,
        file_size,
        file_type
    ) VALUES (
        v_expert_id,
        p_document_type,
        p_document_url,
        p_document_name,
        p_file_size,
        p_file_type
    ) RETURNING id INTO v_document_id;

    RETURN v_document_id;
END;
$$;

-- Function to get expert's own verification status
CREATE OR REPLACE FUNCTION public.get_my_verification_status()
RETURNS JSONB
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_expert_id UUID;
    v_result JSONB;
BEGIN
    v_expert_id := auth.uid();
    
    -- Verify user is an expert
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = v_expert_id AND role = 'expert'
    ) THEN
        RAISE EXCEPTION 'Unauthorized: Only experts can check their verification status';
    END IF;

    -- Get verification status and details
    SELECT jsonb_build_object(
        'verification_status', ep.verification_status,
        'account_activated', p.account_activated,
        'documents_required', ep.documents_required,
        'consultation_access', public.check_expert_consultation_access(v_expert_id),
        'verified_at', ep.verified_at,
        'can_upload_documents', (ep.verification_status IN ('pending', 'resubmission_required'))
    ) INTO v_result
    FROM public.profiles p
    JOIN public.expert_profiles ep ON ep.id = p.id
    WHERE p.id = v_expert_id;

    RETURN COALESCE(v_result, '{}'::jsonb);
END;
$$;

-- ==========================================
-- 8. INDEXES FOR PERFORMANCE
-- ==========================================

-- Indexes for expert_documents table
CREATE INDEX IF NOT EXISTS idx_expert_documents_expert_id ON public.expert_documents(expert_id);
CREATE INDEX IF NOT EXISTS idx_expert_documents_verification_status ON public.expert_documents(verification_status);
CREATE INDEX IF NOT EXISTS idx_expert_documents_document_type ON public.expert_documents(document_type);

-- Indexes for expert_verification_history table
CREATE INDEX IF NOT EXISTS idx_expert_verification_history_expert_id ON public.expert_verification_history(expert_id);
CREATE INDEX IF NOT EXISTS idx_expert_verification_history_created_at ON public.expert_verification_history(created_at);

-- Indexes for expert_profiles verification queries
CREATE INDEX IF NOT EXISTS idx_expert_profiles_verification_status ON public.expert_profiles(verification_status);
CREATE INDEX IF NOT EXISTS idx_expert_profiles_verified_at ON public.expert_profiles(verified_at);

-- ==========================================
-- 9. GRANT PERMISSIONS
-- ==========================================

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.update_expert_verification_status(UUID, expert_verification_status, TEXT, TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_expert_consultation_access(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_expert_verification_details(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_experts_pending_verification() TO authenticated;
GRANT EXECUTE ON FUNCTION public.upload_expert_verification_document(VARCHAR, TEXT, VARCHAR, INTEGER, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_my_verification_status() TO authenticated;

-- Grant table permissions
GRANT SELECT, INSERT, UPDATE ON public.expert_documents TO authenticated;
GRANT SELECT, INSERT ON public.expert_verification_history TO authenticated;
GRANT SELECT ON public.expert_profiles TO authenticated;
GRANT UPDATE (verification_status, verified_at, verified_by, documents_required, updated_at) ON public.expert_profiles TO authenticated;

-- ==========================================
-- MIGRATION COMPLETE
-- ==========================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Expert Verification Enhancement Migration completed successfully!';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'ADDED:';
    RAISE NOTICE '✓ expert_verification_status enum type';
    RAISE NOTICE '✓ expert_documents table with RLS policies';
    RAISE NOTICE '✓ expert_verification_history table with RLS policies';
    RAISE NOTICE '✓ Enhanced expert_profiles table with verification fields';
    RAISE NOTICE '✓ Expert verification management functions';
    RAISE NOTICE '✓ Access control functions and triggers';
    RAISE NOTICE '✓ Performance indexes';
    RAISE NOTICE '✓ Proper column type conversion handling';
    RAISE NOTICE '';
    RAISE NOTICE 'NEXT STEPS:';
    RAISE NOTICE '1. Run test script: test_expert_verification.sql';
    RAISE NOTICE '2. Apply consultation access trigger when consultations table is created';
    RAISE NOTICE '3. Configure Supabase Storage for document uploads';
    RAISE NOTICE '4. Implement frontend expert verification interfaces';
    RAISE NOTICE '5. Set up email notifications for status changes';
    RAISE NOTICE '========================================';
END $$;