-- Pre-Migration Data Check Script
-- Run this BEFORE executing the expert verification migration
-- This will show you what data currently exists so you can prepare for the migration

-- ==========================================
-- CURRENT DATA ANALYSIS
-- ==========================================

-- Check current verification_status values
SELECT 
    'Current verification_status values' as check_type,
    verification_status,
    COUNT(*) as count
FROM public.expert_profiles 
WHERE verification_status IS NOT NULL
GROUP BY verification_status
ORDER BY count DESC;

-- Check NULL verification_status
SELECT 
    'NULL verification_status count' as check_type,
    'NULL' as verification_status,
    COUNT(*) as count
FROM public.expert_profiles 
WHERE verification_status IS NULL;

-- Check expert_profiles table structure
SELECT 
    'expert_profiles columns' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'expert_profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if our target tables already exist
SELECT 
    'Existing tables check' as check_type,
    table_name,
    'EXISTS' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('expert_documents', 'expert_verification_history')
UNION ALL
SELECT 
    'Existing tables check' as check_type,
    'expert_documents' as table_name,
    'MISSING' as status
WHERE NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'expert_documents'
)
UNION ALL
SELECT 
    'Existing tables check' as check_type,
    'expert_verification_history' as table_name,
    'MISSING' as status
WHERE NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'expert_verification_history'
);

-- Check if enum type already exists
SELECT 
    'Enum type check' as check_type,
    typname as enum_name,
    'EXISTS' as status
FROM pg_type 
WHERE typname = 'expert_verification_status'
UNION ALL
SELECT 
    'Enum type check' as check_type,
    'expert_verification_status' as enum_name,
    'MISSING' as status
WHERE NOT EXISTS (
    SELECT 1 FROM pg_type WHERE typname = 'expert_verification_status'
);

-- Check existing functions that might conflict
SELECT 
    'Existing functions check' as check_type,
    routine_name,
    'EXISTS' as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%expert_verification%'
OR routine_name LIKE '%expert%verification%';

-- Show sample expert data
SELECT 
    'Sample expert data' as check_type,
    p.id,
    p.email,
    p.role,
    p.account_activated,
    ep.verification_status,
    ep.created_at
FROM public.profiles p
JOIN public.expert_profiles ep ON ep.id = p.id
WHERE p.role = 'expert'
ORDER BY ep.created_at DESC
LIMIT 5;

-- Check for any constraints on verification_status column
SELECT 
    'Column constraints' as check_type,
    tc.constraint_name,
    tc.constraint_type
FROM information_schema.table_constraints tc
JOIN information_schema.constraint_column_usage ccu 
    ON tc.constraint_name = ccu.constraint_name
WHERE tc.table_name = 'expert_profiles' 
AND ccu.column_name = 'verification_status'
AND tc.table_schema = 'public';