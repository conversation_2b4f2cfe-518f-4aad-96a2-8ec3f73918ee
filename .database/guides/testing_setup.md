This guide covers a basic setup for a todo app (e.g., with auth, todos table via Supabase, Next.js frontend/backend). Assume a monorepo structure. We'll use Vitest for unit/integration and Playwright for E2E. Total setup time: ~30-60 mins.

#### Step 1: Project Setup
- Ensure Supabase CLI is installed (`npm i -g supabase`).
- Init local Supabase: `supabase init && supabase start`.
- Install testing deps (in your Next.js root):
  ```
  npm install -D vitest @vitest/ui jsdom @testing-library/react @testing-library/jest-dom @supabase/supabase-js msw
  npm install -D playwright @playwright/test
  ```
- Update `package.json` scripts:
  ```json
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:e2e": "playwright test"
  }
  ```
- Create `vitest.config.ts` (for Vitest):
  ```ts
  import { defineConfig } from 'vitest/config';
  import react from '@vitejs/plugin-react';

  export default defineConfig({
    plugins: [react()],
    test: {
      environment: 'jsdom',  // For React DOM testing
      setupFiles: './vitest.setup.ts',
    },
  });
  ```
- Create `vitest.setup.ts`:
  ```ts
  import '@testing-library/jest-dom/vitest';
  import { server } from './mocks/server';  // For MSW, optional
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());
  ```

#### Step 2: Database Testing (CLI/pgTAP)
- Create `supabase/tests/database/todos_rls.test.sql` (as in previous example).
- Run: `supabase test db`.
- Assert: Check terminal output for PASS/FAIL.

#### Step 3: Backend Testing (API Routes, Application-Level)
- Assume an API route `/api/todos.ts` that uses Supabase client to fetch todos.
- Create `tests/backend/todos.api.test.ts`:
  ```ts
  import { createClient } from '@supabase/supabase-js';
  import { describe, expect, it, vi } from 'vitest';
  import { GET } from '@/app/api/todos/route';  // Your API handler

  const supabase = createClient('http://localhost:54321', 'your-anon-key');

  describe('Todos API', () => {
    it('fetches user todos with RLS', async () => {
      // Mock auth (or use real session)
      vi.stubGlobal('headers', new Headers({ authorization: 'Bearer mock-token' }));

      const req = new Request('http://localhost/api/todos');
      const res = await GET(req);
      const data = await res.json();

      expect(res.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
    });
  });
  ```
- Run: `vitest tests/backend`.
- For pure app-level DB tests (no API): Use the example from before.

#### Step 4: Frontend Testing (Components/Pages)
- Assume a component `TodoList.tsx` that fetches from Supabase.
- Create `tests/frontend/TodoList.test.tsx`:
  ```tsx
  import { render, screen, waitFor } from '@testing-library/react';
  import userEvent from '@testing-library/user-event';
  import TodoList from '@/components/TodoList';
  import { describe, expect, it } from 'vitest';

  describe('TodoList', () => {
    it('renders todos and allows adding', async () => {
      render(<TodoList />);
      await waitFor(() => expect(screen.getByText('Loading...')).not.toBeInTheDocument());

      const input = screen.getByPlaceholderText('New todo');
      await userEvent.type(input, 'Buy milk');
      await userEvent.click(screen.getByText('Add'));

      expect(screen.getByText('Buy milk')).toBeInTheDocument();
    });
  });
  ```
- Run: `vitest tests/frontend`.
- Tip: Mock Supabase fetches with MSW for isolation.

#### Step 5: End-to-End Testing (Full Flow)
- Create `tests/e2e/todo-flow.spec.ts`:
  ```ts
  import { test, expect } from '@playwright/test';

  test('User can create and view todo', async ({ page }) => {
    await page.goto('http://localhost:3000');  // Your app URL

    // Login (assume form)
    await page.fill('#email', '<EMAIL>');
    await page.fill('#password', 'password');
    await page.click('button[type="submit"]');
    await expect(page.getByText('Welcome')).toBeVisible();

    // Add todo
    await page.fill('#todo-input', 'Buy milk');
    await page.click('#add-button');
    await expect(page.getByText('Buy milk')).toBeVisible();

    // Verify in DB (optional, via page evaluate or separate check)
  });
  ```
- Before running, start your app (`npm run dev`) and local Supabase.
- Run: `npm run test:e2e`.
- Config (`playwright.config.ts`): Auto-generated by `npx playwright init`; set `webServer` to start your app if needed.

#### Best Practices for the Guide
- **Isolation**: Use local Supabase for all tests; reset DB with `supabase db reset` between runs.
- **Coverage**: Aim for 80%+; use `vitest --coverage`.
- **CI**: Add to GitHub Actions (similar to DB example).
- **Mocking**: Mock external services (e.g., auth) to avoid flakiness.
- **Start Small**: Begin with 1-2 tests per layer, then expand.
- **Resources**: Supabase docs for RLS testing; Next.js testing guide.

This setup scales—adapt to your project's specifics. If you hit issues, share code snippets!