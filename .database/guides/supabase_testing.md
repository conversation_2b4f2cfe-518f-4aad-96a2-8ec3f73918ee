# Full Guide to Testing Supabase Applications

Supabase, as an open-source alternative to Firebase, provides a suite of tools including PostgreSQL database, authentication, realtime subscriptions, storage, and edge functions. Testing ensures reliability, security (especially with Row Level Security or RLS), and performance across these components. This guide covers best technologies and methods, drawing from official documentation and community practices as of 2025.

A structured approach follows the testing pyramid, emphasizing more unit tests at the base, fewer integration tests in the middle, and minimal end-to-end (E2E) tests at the top for efficiency.



## 1. Database Testing with pgTAP

Database testing focuses on schema, functions, triggers, and RLS policies. The best tool is pgTAP, integrated with the Supabase CLI for unit-style tests.  

### Setup
- Install Supabase CLI (v1.11.4+): `brew install supabase/tap/supabase` (macOS) or equivalent.
- Initialize project: `supabase init`.
- Create tests folder: `mkdir -p supabase/tests/database`.
- Enable pgTAP extension in your database (via Dashboard or SQL: `CREATE EXTENSION IF NOT EXISTS pgtap;`).
- Optional: Install Supabase test helpers from GitHub (`supabase-test-helpers`) for utilities like user creation and authentication simulation.

Name test files as `XX-feature.test.sql` (e.g., `01-rls_policies.test.sql`) for organization.

### Writing Tests
Tests are SQL scripts using pgTAP assertions. Wrap in transactions for isolation:

```sql
BEGIN;
SELECT plan(3);  -- Number of tests

-- Test schema
SELECT has_table('public', 'profiles', 'profiles table exists');
SELECT has_column('public', 'profiles', 'user_id', 'user_id column exists');

-- Test RLS (using helpers)
SELECT tests.rls_enabled('public');  -- Ensure RLS on all tables

SELECT * FROM finish();
ROLLBACK;
```

For authenticated tests:
- Create users: `SELECT tests.create_supabase_user('test_user', '<EMAIL>');`
- Authenticate: `SELECT tests.authenticate_as('test_user');`
- Test operations: Use `throws_ok` for denied actions, `results_eq` for expected queries.

Example for RLS on a `todos` table:
```sql
BEGIN;
SELECT plan(4);

-- Setup data as postgres
INSERT INTO auth.users (id, email) VALUES (uuid_generate_v4(), '<EMAIL>');
INSERT INTO public.todos (user_id, task) VALUES ((SELECT id FROM auth.users WHERE email='<EMAIL>'), 'Task 1');

-- Authenticate as user
SELECT tests.authenticate_as('<EMAIL>');

-- Test select
SELECT is((SELECT count(*) FROM public.todos), 1::bigint, 'User sees own todo');

-- Test unauthorized
SELECT throws_ok('UPDATE public.todos SET task=''Hack'' WHERE user_id != current_user_id()', 'permission denied');

SELECT * FROM finish();
ROLLBACK;
```

Cover CRUD, anonymous/authenticated roles, edge cases like nulls or invalid data.

### Running Tests
- Local: `supabase start` then `supabase test db` (applies migrations, seeds, runs tests).
- Verbose: `supabase test db --verbose`.
- Remote: Link project (`supabase link`) and provide DB URL.

### Integration with CI/CD
Use GitHub Actions for automated runs on PRs:

```yaml
name: Database Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: supabase/setup-cli@v1
      - run: supabase start
      - run: supabase test db
```

## 2. Unit Testing Client-Side Code and Edge Functions

For JavaScript/TypeScript code interacting with Supabase (e.g., via `@supabase/supabase-js`), use Vitest or Jest.  Vitest is recommended for speed and Vite compatibility.

### Setup
- Install: `npm install -D vitest @vitest/ui msw`.
- Configure `vite.config.ts` for testing.
- Mock Supabase client with MSW (Mock Service Worker) to simulate API responses.

Example test for a function fetching todos:
```ts
import { createClient } from '@supabase/supabase-js';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { describe, it, expect } from 'vitest';

const server = setupServer(
  http.get('*/todos', () => HttpResponse.json([{ id: 1, task: 'Test' }]))
);

describe('fetchTodos', () => {
  it('fetches todos', async () => {
    server.listen();
    const supabase = createClient('url', 'key');
    const { data } = await supabase.from('todos').select('*');
    expect(data).toHaveLength(1);
    server.close();
  });
});
```

For Edge Functions (Deno-based): Use Deno's built-in `deno test` for unit tests.

## 3. Integration Testing

Test interactions between components, like API routes with Supabase. Use Vitest/Jest with real or mocked Supabase.

- Mock for isolation: As in unit tests.
- Real integration: Spin up local Supabase and test against it.
- Tools: Combine with testing-library for React components.

For auth: Mock `signIn`/`signUp` in unit tests, but use real local auth for integration to verify flows.

## 4. End-to-End (E2E) Testing

Simulate user interactions in a full environment. Best tools: Playwright (faster, parallel) or Cypress (interactive debugger).    

### Using Playwright with Supawright
Supawright enhances Playwright for Supabase by automating record creation/cleanup, handling foreign keys. 

Setup:
- Install: `npm i -D playwright supawright`.
- Adjust Supabase types: Change `interface Database` to `type Database`.
- Use `withSupawright(test, { schemas: ['public'] })` in tests.

Example:
```ts
import { withSupawright } from 'supawright';

const test = withSupawright(baseTest, { schemas: ['public'] });

test('user signup and todo creation', async ({ page, supawright }) => {
  // Supawright creates user/session automatically
  const user = await supawright.create('auth', 'users', { email: '<EMAIL>' });
  await page.goto('/signup');
  await page.fill('#email', '<EMAIL>');
  await page.click('button[type=submit]');
  // Assert todo page loads with user's data
});
```

Run: `npx playwright test`. Cleanup happens automatically.

For Cypress: Similar, but use plugins like `cypress-supabase` for auth mocking.

## 5. Testing Specific Supabase Features

| Feature | Methods and Tools | Best Practices |
|---------|-------------------|----------------|
| **Authentication** | Mock signIn/signUp in unit/integration; E2E with real flows using Playwright. Test JWT claims, roles. | Cover providers (email, OAuth); test failures (invalid creds). Use local Supabase for realism.  |
| **Realtime** | E2E: Subscribe in browser, trigger DB changes, assert updates. Use Vitest for unit (mock channels). | Test latency, offline scenarios; cover channels, presence. |
| **Storage** | Integration: Upload/download files via Supabase client; E2E: Browser uploads. Mock with MSW. | Test buckets, permissions, file types; handle large files in CI. |
| **Edge Functions** | Deno test for logic; Invoke via Supabase CLI for integration. | Mock dependencies; test HTTP requests/responses. |
| **RLS Policies** | pgTAP: Test CRUD per role. Include in DB tests. | Negative tests (denials); index columns in policies for perf. |

## 6. Best Practices and CI/CD Integration

- **Isolation**: Use transactions in DB tests; unique IDs/prefixes in app tests.
- **Data**: Realistic, edge-case covering; seed via `seed.sql`.
- **Coverage**: Aim for 80%+; test security (RLS, auth).
- **Performance**: Keep tests fast; parallelize in Playwright.
- **CI/CD**: Run all tests in pipelines (GitHub Actions, GitLab CI). Fail builds on test failures; use matrix for environments.
- **Monitoring**: Integrate with tools like Sentry for production errors.

For advanced setups, explore community repos like MakerKit for Next.js examples. Regularly update tools and review Supabase changelogs for new testing features.