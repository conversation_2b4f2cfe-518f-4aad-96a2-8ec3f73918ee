# Expert Verification Migration Guide

## Overview

This guide helps you successfully apply the expert verification enhancement migration to your AgriDustria database. It addresses common issues and provides step-by-step instructions.

## Common Issues and Solutions

### Issue 1: Column Type Conversion Error

**Error Message:**
```
ERROR: 42804: default for column "verification_status" cannot be cast automatically to type expert_verification_status
```

**Cause:** The existing `verification_status` column in `expert_profiles` table has a default value that can't be automatically cast to the new enum type.

**Solution:** Use the fixed migration script that properly handles the column type conversion.

### Issue 2: Existing Data Conflicts

**Error Message:**
```
ERROR: invalid input value for enum expert_verification_status: "some_existing_value"
```

**Cause:** Existing data in the `verification_status` column doesn't match the new enum values.

**Solution:** The migration script includes data normalization to handle existing values.

## Pre-Migration Checklist

1. **Backup your database** before running the migration
2. **Check existing data** in `expert_profiles.verification_status` column:
   ```sql
   SELECT DISTINCT verification_status FROM public.expert_profiles;
   ```
3. **Verify admin permissions** - ensure you can execute DDL statements
4. **Check for active connections** that might be using the expert_profiles table

## Migration Steps

### Step 1: Apply the Fixed Migration

Execute the fixed migration script `expert_verification_functions_fixed.sql` in your Supabase SQL Editor.

### Step 2: Verify the Migration

Check that the new structures were created:

```sql
-- Check new enum type
SELECT enumlabel FROM pg_enum WHERE enumtypid = 'expert_verification_status'::regtype;

-- Check new tables
\d public.expert_documents
\d public.expert_verification_history

-- Check updated expert_profiles table
\d public.expert_profiles
```

### Step 3: Test the Functions

Run the test script to ensure everything works:

```sql
-- Execute the test script
\i test_expert_verification.sql
```

## Data Migration Notes

### Existing Verification Status Values

The migration handles these existing values:

- `'pending'` → `'pending'` (no change)
- `'approved'` → `'approved'` (no change)
- `'rejected'` → `'rejected'` (no change)
- `'verified'` → `'approved'` (mapped)
- `'unverified'` → `'pending'` (mapped)
- `NULL` → `'pending'` (default)
- Any other value → `'pending'` (safe default)

### Default Value Handling

The migration properly handles the default value by:

1. Dropping the existing default
2. Converting the column type
3. Setting the new default value

## Post-Migration Verification

### 1. Check Function Permissions

```sql
-- Check function permissions
SELECT routine_name, routine_type, security_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%expert%verification%';
```

### 2. Verify RLS Policies

```sql
-- Check RLS policies
SELECT tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('expert_documents', 'expert_verification_history');
```

### 3. Test Basic Functionality

```sql
-- Test getting pending experts (should work for admins)
SELECT * FROM public.get_experts_pending_verification() LIMIT 5;

-- Test enum values
SELECT unnest(enum_range(NULL::expert_verification_status));
```

## Troubleshooting

### Issue: Function Permission Denied

**Error:** `permission denied for function public.get_experts_pending_verification`

**Solution:**
```sql
GRANT EXECUTE ON FUNCTION public.get_experts_pending_verification() TO authenticated;
```

### Issue: RLS Policy Blocking Access

**Error:** `permission denied for table expert_documents`

**Solution:** Check your user role and ensure RLS policies are correctly applied:
```sql
-- Check your current role
SELECT current_user, session_user;

-- Check RLS is enabled
SELECT tablename, rowsecurity FROM pg_tables 
WHERE schemaname = 'public' AND tablename = 'expert_documents';
```

### Issue: Enum Type Already Exists

**Error:** `type "expert_verification_status" already exists`

**Solution:** The migration script uses `IF NOT EXISTS` to handle this safely. If you get this error, the script should continue without issues.

## Rollback Instructions

If you need to rollback the migration:

```sql
-- Drop new tables (this will lose data!)
DROP TABLE IF EXISTS public.expert_verification_history CASCADE;
DROP TABLE IF EXISTS public.expert_documents CASCADE;

-- Revert expert_profiles changes
ALTER TABLE public.expert_profiles 
DROP COLUMN IF EXISTS documents_required,
DROP COLUMN IF EXISTS verified_at,
DROP COLUMN IF EXISTS verified_by;

-- Revert verification_status to varchar (if needed)
ALTER TABLE public.expert_profiles 
ALTER COLUMN verification_status TYPE varchar(20);

-- Drop functions
DROP FUNCTION IF EXISTS public.update_expert_verification_status(UUID, expert_verification_status, TEXT, TEXT[]);
DROP FUNCTION IF EXISTS public.check_expert_consultation_access(UUID);
DROP FUNCTION IF EXISTS public.get_expert_verification_details(UUID);
DROP FUNCTION IF EXISTS public.get_experts_pending_verification();
DROP FUNCTION IF EXISTS public.upload_expert_verification_document(VARCHAR, TEXT, VARCHAR, INTEGER, VARCHAR);
DROP FUNCTION IF EXISTS public.get_my_verification_status();
DROP FUNCTION IF EXISTS public.validate_expert_consultation_access();

-- Drop enum type
DROP TYPE IF EXISTS public.expert_verification_status CASCADE;
```

## Performance Monitoring

After migration, monitor these queries:

```sql
-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read 
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('expert_documents', 'expert_verification_history', 'expert_profiles');

-- Check table sizes
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('expert_documents', 'expert_verification_history', 'expert_profiles');
```

## Support

If you encounter issues not covered in this guide:

1. Check the Supabase logs for detailed error messages
2. Verify your database user has the necessary permissions
3. Ensure you're running the migration in the correct database/schema
4. Review the migration script for any environment-specific adjustments needed

## Next Steps

After successful migration:

1. **Configure Supabase Storage** for document uploads
2. **Set up email notifications** for verification status changes
3. **Implement frontend interfaces** for expert verification
4. **Configure monitoring** for the verification workflow

Remember to test thoroughly in a development environment before applying to production!