# Admin Invitation Guide

## 1. Overview

This guide explains how to implement an admin-to-admin invitation system using Supabase's built-in `auth.admin.inviteUserByEmail()` function.

## 2. Prerequisites

- A Supabase project with authentication enabled.
- The `invite` provider is enabled in your Supabase project's authentication settings.

## 3. Implementation Steps

### 3.1. Database

No database changes are required for this feature, as it leverages Supabase's built-in authentication system.

### 3.2. Backend (Supabase Edge Function)

1.  **Create a new Supabase Edge Function**: Create a new Edge Function in your Supabase project called `invite-admin`.
2.  **Implement the Invitation Logic**: In the `invite-admin` function, call the `auth.admin.inviteUserByEmail()` function with the email address of the user to be invited.

    ```typescript
    // supabase/functions/invite-admin/index.ts
    import { serve } from "https://deno.land/std@0.131.0/http/server.ts"
    import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

    serve(async (req) => {
      const { email } = await req.json()
      const supabaseAdmin = createClient(
        Deno.env.get("SUPABASE_URL") ?? "",
        Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
      )

      const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email)

      if (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          headers: { "Content-Type": "application/json" },
          status: 400,
        })
      }

      return new Response(JSON.stringify(data), {
        headers: { "Content-Type": "application/json" },
      })
    })
    ```

### 3.3. Frontend

1.  **Create an Admin Management Page**: Create a new page in the dashboard at `/dashboard/admins` where admins can view a list of existing admins and invite new ones.
2.  **Create an Invitation Form**: Add a form to the admin management page that allows admins to enter the email address of the user they want to invite.
3.  **Call the Supabase Edge Function**: When the form is submitted, call the `invite-admin` Supabase Edge Function.

    ```typescript
    // src/app/dashboard/admins/page.tsx
    'use client'

    import { useState } from 'react'
    import { createClient } from '@/supabase/client'

    export default function AdminPage() {
      const [email, setEmail] = useState('')
      const supabase = createClient()

      const handleInvite = async () => {
        const { error } = await supabase.functions.invoke('invite-admin', {
          body: { email },
        })

        if (error) {
          alert('Error inviting admin: ' + error.message)
        } else {
          alert('Admin invited successfully!')
        }
      }

      return (
        <div>
          <h1>Admin Management</h1>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter email to invite"
          />
          <button onClick={handleInvite}>Invite Admin</button>
        </div>
      )
    }
    ```

### 3.4. Email Template

1.  **Customize the Invitation Email**: Customize the invitation email template in your Supabase project's authentication settings to match your application's branding.

## 4. Security Considerations

- **Restrict Access to the Admin Management Page**: Ensure that only admins can access the admin management page.
- **Validate the Email Address**: Validate the email address on both the frontend and the backend to prevent invalid email addresses from being submitted.