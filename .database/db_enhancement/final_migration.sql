-- This script unifies the expert verification status by removing the redundant
-- `verification_status` column from the `profiles` table and making the
-- `expert_profiles.verification_status` column the single source of truth.

-- 1. Remove the `verification_status` column from the `profiles` table
ALTER TABLE public.profiles
DROP COLUMN IF EXISTS verification_status;

-- 2. Refactor User Profiles
-- Rename the farmer_profiles table to client_profiles
ALTER TABLE IF EXISTS public.farmer_profiles RENAME TO client_profiles;

-- Add a new client_type ENUM column to this table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'client_type') THEN
        CREATE TYPE public.client_type AS ENUM ('FARMER', 'FACTORY_WORKER');
    END IF;
END$$;
ALTER TABLE public.client_profiles ADD COLUMN IF NOT EXISTS client_type public.client_type;

-- 3. Refactor Expert Profiles
-- Add a new expert_type ENUM column to the existing expert_profiles table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'expert_type') THEN
        CREATE TYPE public.expert_type AS ENUM ('AGRICULTURE', 'INDUSTRIAL');
    END IF;
END$$;
ALTER TABLE public.expert_profiles ADD COLUMN IF NOT EXISTS expert_type public.expert_type;

-- 4. Add new roles to the user_role enum
DO $$
BEGIN
    ALTER TYPE public.user_role ADD VALUE IF NOT EXISTS 'agriculture_expert';
    ALTER TYPE public.user_role ADD VALUE IF NOT EXISTS 'industrial_expert';
END$$;

-- 5. Remove the consultation_packages table and its references completely
ALTER TABLE public.consultations DROP CONSTRAINT IF EXISTS consultations_package_id_fkey;
ALTER TABLE public.consultation_requests DROP CONSTRAINT IF EXISTS consultation_requests_package_id_fkey;
ALTER TABLE public.consultations DROP COLUMN IF EXISTS package_id;
ALTER TABLE public.consultation_requests DROP COLUMN IF EXISTS package_id;
DROP TABLE IF EXISTS public.consultation_packages;