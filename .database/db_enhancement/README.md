# Database Enhancement

This directory contains a series of scripts and documentation related to the enhancement of the AgriDustria database.

## Files

- `db_enhancement_report.md`: A comprehensive architectural report detailing all proposed changes.
- `final_migration.sql`: A single, idempotent migration script to apply all changes.
- `DB_core_onboarding.md`: Authoritative developer documentation for the new database logic.
- `admin_invitation_guide.md`: A guide on how to implement the admin invitation feature using Supabase functionalities.
- `authenticated_users.sql`: This file contains `INSERT` statements for `auth.users` and must be used as the foundation for all user-related data.
- `seed.sql`: This script seeds the database with test data.

## Order of Execution

1.  **`final_migration.sql`**: This script should be run first to apply the schema changes.
2.  **`authenticated_users.sql`**: This script should be run second to insert the pre-authenticated users.
3.  **`seed.sql`**: This script should be run last to seed the database with test data.