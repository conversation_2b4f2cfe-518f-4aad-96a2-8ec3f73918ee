# Database Enhancement Report

## 1. Introduction

This report outlines a series of proposed enhancements to the AgriDustria database schema. The primary goals of these changes are to improve data integrity, optimize performance, and streamline development by refactoring key areas of the database architecture.

## 2. Analysis of Existing Schema

### 2.1. Redundant and Inconsistent Status Management

A critical issue in the current schema is the presence of multiple, inconsistent columns for tracking user and asset statuses. This redundancy creates confusion and increases the risk of data integrity violations.

- **Expert Verification Status**: The `profiles` table contains a `verification_status` column of type `text`, while the `expert_profiles` table has a more specific `verification_status` column of type `expert_verification_status` (ENUM). This duplication is problematic.

- **Asset Status**: The `assets` table has a `status` column of type `asset_status` (ENUM), which is good. However, the `registration_requests` table also has a `status` column of type `RegistrationStatus` (ENUM). While these are for different purposes, their relationship and the flow of data between them could be clearer.

### 2.2. Inter-Role Relationships

The relationships between `experts`, `farmers`, and `factory_workers` are not explicitly defined in a way that efficiently supports features like consultations and chat. The current schema relies on foreign keys in the `consultation_requests` table, which may not be the most scalable solution for many-to-many interactions.

### 2.3. Asset Registration Workflow

The process for creating an asset from an approved registration request is handled by a trigger (`on_registration_approved`). While this is a good approach, the logic within the trigger could be more robust to handle potential failures and ensure data consistency.

### 2.4. Indexing Strategy

The current indexing strategy is a good start, but there are opportunities for improvement. Some foreign keys are not indexed, and there are no indexes on columns that are frequently used in `WHERE` clauses for filtering and searching.

## 3. Proposed Enhancements

### 3.1. Unify Status Management and Optimize Onboarding

To address the redundancy and inconsistency in status management, the following changes are proposed:

- **Expert Verification**:
    - The `verification_status` column will be **removed** from the `profiles` table.
    - The `expert_profiles.verification_status` column will be the **single source of truth** for an expert's verification status.
    - The `profiles.account_activated` column will be used to control system access for all roles, including experts. This will be set to `true` for an expert only when their `expert_profiles.verification_status` is `approved`.

- **Asset Registration**:
    - The `asset_status` and `RegistrationStatus` ENUMs will be reviewed and potentially merged into a single, more generic `status` ENUM that can be used across multiple tables.

### 3.2. Expert Onboarding and Verification Flow

The expert onboarding and verification flow will be optimized as follows:

1.  **Expert Signup**: A new user signs up with the `expert` role. The `handle_new_user` trigger creates a new record in the `profiles` table with `account_activated` set to `false`.
2.  **Verification Submission**: The expert completes their profile and submits their verification documents. The `expert_profiles.verification_status` is set to `pending`.
3.  **Admin Review**: An admin reviews the expert's profile and documents.
4.  **Approval/Rejection**: The admin approves or rejects the expert. The `update_expert_verification_status` function is called to update the `expert_profiles.verification_status` and `profiles.account_activated` columns accordingly.

### 3.3. Admin Invitation Flow

A guide will be created to document the implementation of an admin-to-admin invitation system using Supabase's built-in functionality. The guide will cover the following:

- **Enabling the `invite` provider** in the Supabase project settings.
- **Using the `auth.admin.inviteUserByEmail()`** function to send an invitation email.
- **Handling the invitation** on the frontend.
- **Creating a new admin management page** with an invitation feature.

### 3.4. Optimize Inter-Role Relationships

To better model the many-to-many relationships between users, the following is proposed:

- A new join table, `consultation_participants`, will be created to link users to consultations. This will provide a more flexible and scalable way to manage consultation participants.

### 3.3. Improve Asset Registration Workflow

The asset registration workflow will be enhanced as follows:

- The `handle_registration_approval` trigger will be refactored to be more resilient and to provide better error logging.
- A new function, `create_asset_from_request`, will be created to encapsulate the logic for creating an asset from a registration request.

### 3.4. Enhance Indexing Strategy

The following indexing strategy will be implemented:

- Indexes will be added to all foreign key columns.
- Indexes will be added to columns that are frequently used in `WHERE` clauses, such as `profiles.email` and `assets.name`.

### 3.5. Implement Row-Level Security (RLS)

A baseline set of RLS policies will be implemented to enforce data access rules at the database level.

## 4. Implementation Plan

The proposed changes will be implemented in the following order:

1.  Create a new migration script to apply the schema changes.
2.  Update the existing database functions and triggers to work with the new schema.
3.  Create new database functions and triggers to support the enhanced workflows.
4.  Update the RLS policies to reflect the new schema.
5.  Create the `DB_core_onboarding.md` documentation.
6.  Create the guide for the admin invitation feature.
7.  Update the `tasks.md` file with new development tasks.