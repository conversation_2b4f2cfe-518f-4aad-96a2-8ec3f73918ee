-- Test Users Profiles Seeding <PERSON>ript
-- This script creates proper profiles for the two test users in testing_users_I_use.sql
-- It is idempotent and can be run multiple times safely.

-- Skip auth.users insertion as they should already exist
-- The users are created through the authentication system

-- Create profiles for the test users
-- First, safely delete existing expert profiles that reference these users
DELETE FROM public.expert_profiles WHERE id IN (
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    'c6645c34-ea98-4852-b56b-ca4de7d1c01c'
);

-- Delete existing profiles to ensure clean state
DELETE FROM public.profiles WHERE id IN (
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    'c6645c34-ea98-4852-b56b-ca4de7d1c01c'
);

-- Insert profiles for both test users
INSERT INTO public.profiles (
    id,
    email,
    first_name,
    last_name,
    role,
    account_activated,
    created_at,
    updated_at
) VALUES
-- Admin user profile
(
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da',
    '<EMAIL>',
    'Arrogant',
    'Alligator',
    'admin',
    true,
    '2025-06-03 23:18:47.043738+00',
    '2025-10-02 22:58:30.41259+00'
),
-- Expert user profile
(
    'c6645c34-ea98-4852-b56b-ca4de7d1c01c',
    '<EMAIL>',
    'Psychological',
    'Duck',
    'expert',
    true,
    '2025-06-26 12:37:33.455614+00',
    '2025-10-02 23:05:08.880062+00'
);

-- Create expert profile for the expert user (unverified status)
INSERT INTO public.expert_profiles (
    id,
    bio,
    years_of_experience,
    education,
    verification_status,
    expertise_area,
    qualifications,
    created_at,
    updated_at
) VALUES (
    'c6645c34-ea98-4852-b56b-ca4de7d1c01c',
    'Agricultural expert specializing in sustainable farming practices and crop optimization.',
    5,
    'Master of Science in Agricultural Engineering',
    'pending', -- Unverified status as requested
    'Sustainable Agriculture, Crop Management, Soil Science',
    'MSc Agricultural Engineering, Certified Crop Advisor, Sustainable Agriculture Certificate',
    '2025-06-26 12:37:33.455614+00',
    '2025-10-02 23:05:08.880062+00'
);

-- Add some sample documents for the expert (to make the profile more realistic)
-- Delete existing documents first
DELETE FROM public.expert_documents WHERE expert_id = 'c6645c34-ea98-4852-b56b-ca4de7d1c01c';

INSERT INTO public.expert_documents (
    expert_id,
    document_type,
    document_url,
    document_name,
    upload_date,
    verification_status
) VALUES 
(
    'c6645c34-ea98-4852-b56b-ca4de7d1c01c',
    'degree_certificate',
    'https://example.com/documents/msc_certificate.pdf',
    'MSc_Agricultural_Engineering_Certificate.pdf',
    '2025-06-26 12:40:00+00',
    'pending'
),
(
    'c6645c34-ea98-4852-b56b-ca4de7d1c01c',
    'professional_license',
    'https://example.com/documents/crop_advisor_license.pdf',
    'Certified_Crop_Advisor_License.pdf',
    '2025-06-26 12:41:00+00',
    'pending'
),
(
    'c6645c34-ea98-4852-b56b-ca4de7d1c01c',
    'identity_document',
    'https://example.com/documents/national_id.pdf',
    'National_ID_Document.pdf',
    '2025-06-26 12:42:00+00',
    'pending'
);

-- Add initial verification history entry for the expert
-- Delete existing history first
DELETE FROM public.expert_verification_history WHERE expert_id = 'c6645c34-ea98-4852-b56b-ca4de7d1c01c';

INSERT INTO public.expert_verification_history (
    expert_id,
    previous_status,
    new_status,
    changed_by,
    admin_notes,
    created_at
) VALUES (
    'c6645c34-ea98-4852-b56b-ca4de7d1c01c',
    null, -- Initial status
    'pending',
    'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da', -- Changed by admin user
    'Expert profile created and submitted for verification',
    '2025-06-26 12:37:33.455614+00'
);

-- Verify the data was inserted correctly
SELECT 
    'Profiles created:' as message,
    COUNT(*) as count
FROM public.profiles 
WHERE id IN ('bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da', 'c6645c34-ea98-4852-b56b-ca4de7d1c01c');

SELECT 
    'Expert profiles created:' as message,
    COUNT(*) as count
FROM public.expert_profiles 
WHERE id = 'c6645c34-ea98-4852-b56b-ca4de7d1c01c';

SELECT 
    'Expert documents created:' as message,
    COUNT(*) as count
FROM public.expert_documents 
WHERE expert_id = 'c6645c34-ea98-4852-b56b-ca4de7d1c01c';

-- Display the created profiles for verification
SELECT
    p.id,
    p.email,
    p.first_name,
    p.last_name,
    p.role,
    p.account_activated,
    CASE
        WHEN ep.id IS NOT NULL THEN ep.verification_status::text
        ELSE 'N/A (not an expert)'
    END as expert_verification_status
FROM public.profiles p
LEFT JOIN public.expert_profiles ep ON p.id = ep.id
WHERE p.id IN ('bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da', 'c6645c34-ea98-4852-b56b-ca4de7d1c01c')
ORDER BY p.role DESC;
