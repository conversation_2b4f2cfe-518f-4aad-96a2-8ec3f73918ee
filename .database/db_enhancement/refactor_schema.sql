-- This script refactors the schema to differentiate between agricultural and industrial users and experts.

-- 1. Refactor User Profiles
-- Rename the farmer_profiles table to client_profiles
ALTER TABLE IF EXISTS public.farmer_profiles RENAME TO client_profiles;

-- Add a new client_type ENUM column to this table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'client_type') THEN
        CREATE TYPE public.client_type AS ENUM ('FARMER', 'FACTORY_WORKER');
    END IF;
END$$;
ALTER TABLE public.client_profiles ADD COLUMN IF NOT EXISTS client_type public.client_type;

-- 2. Refactor Expert Profiles
-- Add a new expert_type ENUM column to the existing expert_profiles table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'expert_type') THEN
        CREATE TYPE public.expert_type AS ENUM ('AGRICULTURE', 'INDUSTRIAL');
    END IF;
END$$;
ALTER TABLE public.expert_profiles ADD COLUMN IF NOT EXISTS expert_type public.expert_type;