-- This script seeds the database with test data.
-- It is idempotent and can be run multiple times.

-- 1. Truncate all relevant tables
TRUNCATE TABLE public.profiles, public.user_roles, public.roles, public.expert_profiles, public.expert_documents, public.expert_verification_history, public.client_profiles, public.assets, public.registration_requests, public.consultation_requests, public.consultations, public.messages RESTART IDENTITY CASCADE;

-- 2. Roles Initialization
INSERT INTO public.roles (name, description, is_admin_role) VALUES
('admin', 'Administrator with full access', true),
('agriculture_expert', 'Expert in the field of agriculture', false),
('industrial_expert', 'Expert in the field of industry', false),
('farmer', 'User who owns a farm', false),
('factory_worker', 'User who works in a factory', false);

-- 3. User & Profile Seeding
-- This assumes users from authenticated_users.sql are in auth.users
INSERT INTO public.profiles (id, email, first_name, last_name, phone_number) VALUES
('125d4d70-5b03-442a-895d-b4ae0ba11252', 'ahmad<PERSON><PERSON><PERSON>@gmail.com', 'Super', 'Admin', '111111111'),
('04522a96-c132-47c8-9102-d0bcac64292b', '<EMAIL>', 'Agri', 'Expert', '222222222'),
('0b1c1f2f-7ab2-4b03-af7b-f7244e688352', '<EMAIL>', 'Industro', 'Expert', '333333333'),
('13cd7e57-3fcc-4e38-b050-b3fbbbdb5573', '<EMAIL>', 'Farma', 'Jones', '444444444'),
('1a6b38ed-10c1-496d-8439-ec80e8de0572', '<EMAIL>', 'Factoria', 'Smith', '555555555'),
('1aad4c12-3f89-414b-b8eb-90e635d879de', '<EMAIL>', 'Pending', 'Expert', '666666666'),
('1b69f50e-03c5-4160-b39f-2a131c662a0e', '<EMAIL>', 'Rejected', 'Expert', '777777777');

-- User Roles
INSERT INTO public.user_roles (user_id, role_id)
SELECT p.id, r.id
FROM public.profiles p
JOIN public.roles r ON
    CASE
        WHEN p.email = '<EMAIL>' THEN r.name = 'admin'
        WHEN p.email = '<EMAIL>' THEN r.name = 'agriculture_expert'
        WHEN p.email = '<EMAIL>' THEN r.name = 'industrial_expert'
        WHEN p.email = '<EMAIL>' THEN r.name = 'farmer'
        WHEN p.email = '<EMAIL>' THEN r.name = 'factory_worker'
        WHEN p.email = '<EMAIL>' THEN r.name = 'agriculture_expert'
        WHEN p.email = '<EMAIL>' THEN r.name = 'industrial_expert'
    END;

-- 4. Scenario-Based Data Simulation

-- Expert Lifecycle
INSERT INTO public.expert_profiles (id, expert_type, verification_status)
SELECT p.id,
    CASE
        WHEN r.name = 'agriculture_expert' THEN 'AGRICULTURE'::public.expert_type
        WHEN r.name = 'industrial_expert' THEN 'INDUSTRIAL'::public.expert_type
    END,
    CASE
        WHEN p.email = '<EMAIL>' THEN 'pending'::public.expert_verification_status
        WHEN p.email = '<EMAIL>' THEN 'approved'::public.expert_verification_status
		WHEN p.email = '<EMAIL>' THEN 'approved'::public.expert_verification_status
        WHEN p.email = '<EMAIL>' THEN 'rejected'::public.expert_verification_status
        ELSE 'pending'::public.expert_verification_status
    END
FROM public.profiles p
JOIN public.user_roles ur ON p.id = ur.user_id
JOIN public.roles r ON ur.role_id = r.id
WHERE r.name LIKE '%expert%';

-- Add a document for the pending expert
INSERT INTO public.expert_documents (expert_id, document_type, document_url, document_name)
SELECT id, 'ID', 'http://example.com/id.pdf', 'ID.pdf'
FROM public.profiles WHERE email = '<EMAIL>';

-- Add verification history for approved and rejected experts
WITH admin_user AS (
    SELECT id FROM public.profiles WHERE email = '<EMAIL>'
)
INSERT INTO public.expert_verification_history (expert_id, new_status, changed_by)
SELECT p.id, 'approved'::public.expert_verification_status, admin_user.id
FROM public.profiles p, admin_user
WHERE p.email in ('<EMAIL>', '<EMAIL>')
UNION ALL
SELECT p.id, 'rejected'::public.expert_verification_status, admin_user.id
FROM public.profiles p, admin_user
WHERE p.email = '<EMAIL>';

-- Farmer/Factory Worker Lifecycle
INSERT INTO public.client_profiles (id, client_type)
SELECT p.id,
    CASE
        WHEN r.name = 'farmer' THEN 'FARMER'::public.client_type
        WHEN r.name = 'factory_worker' THEN 'FACTORY_WORKER'::public.client_type
    END
FROM public.profiles p
JOIN public.user_roles ur ON p.id = ur.user_id
JOIN public.roles r ON ur.role_id = r.id
WHERE r.name IN ('farmer', 'factory_worker');

-- Asset Registration Lifecycle
WITH farmer_user AS (
    SELECT id FROM public.profiles WHERE email = '<EMAIL>'
),
factory_user AS (
    SELECT id FROM public.profiles WHERE email = '<EMAIL>'
),
admin_user AS (
    SELECT id FROM public.profiles WHERE email = '<EMAIL>'
)
INSERT INTO public.registration_requests (user_id, entity_type, registration_data, status, reviewed_by)
SELECT farmer_user.id, 'FARM'::public."EntityType", '{"name":"Pending Farm"}'::jsonb, 'PENDING'::public."RegistrationStatus", NULL FROM farmer_user
UNION ALL
SELECT factory_user.id, 'FACTORY'::public."EntityType", '{"name":"Rejected Factory"}'::jsonb, 'REJECTED'::public."RegistrationStatus", admin_user.id FROM factory_user, admin_user
UNION ALL
SELECT farmer_user.id, 'FARM'::public."EntityType", '{"name":"Approved Farm"}'::jsonb, 'APPROVED'::public."RegistrationStatus", admin_user.id FROM farmer_user, admin_user;

-- Asset for the approved request
INSERT INTO public.assets (owner_id, name, asset_type)
SELECT p.id, 'Approved Farm', 'farm'::public.asset_type
FROM public.profiles p
WHERE p.email = '<EMAIL>';

-- Consultation Lifecycle
WITH farm_asset AS (
    SELECT id, owner_id FROM public.assets WHERE name = 'Approved Farm'
),
agri_expert AS (
    SELECT id FROM public.profiles WHERE email = '<EMAIL>'
),
farm_consultation_request AS (
    INSERT INTO public.consultation_requests (requester_id, expert_id, asset_id, problem_description)
    SELECT farm_asset.owner_id, agri_expert.id, farm_asset.id, 'My crops are dying.'
    FROM farm_asset, agri_expert
    RETURNING id, requester_id, expert_id
)
INSERT INTO public.messages (consultation_id, sender_id, content)
SELECT id, requester_id, 'Hello, I need help with my farm.' FROM farm_consultation_request
UNION ALL
SELECT id, expert_id, 'Hi, I am here to help. What is the problem?' FROM farm_consultation_request;

-- Add a second consultation for the factory
WITH factory_user AS (
    SELECT id FROM public.profiles WHERE email = '<EMAIL>'
),
industrial_expert AS (
    SELECT id FROM public.profiles WHERE email = '<EMAIL>'
),
factory_asset AS (
    INSERT INTO public.assets(owner_id, name, asset_type)
    SELECT factory_user.id, 'My Factory', 'factory'::public.asset_type FROM factory_user
    RETURNING id, owner_id
),
factory_consultation_request AS (
    INSERT INTO public.consultation_requests (requester_id, expert_id, asset_id, problem_description)
    SELECT factory_asset.owner_id, industrial_expert.id, factory_asset.id, 'My machine is broken.'
    FROM factory_asset, industrial_expert
    RETURNING id, requester_id, expert_id
)
INSERT INTO public.messages (consultation_id, sender_id, content)
SELECT id, requester_id, 'Hello, my machine is broken.' FROM factory_consultation_request
UNION ALL
SELECT id, expert_id, 'I can help with that.' FROM factory_consultation_request;