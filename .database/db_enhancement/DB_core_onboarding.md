# DB Core Onboarding and Management

## 1. Authentication and User Management

### 1.1. Overview

The authentication and user management system is the backbone of the AgriDustria platform. It is responsible for authenticating users, managing their profiles, and controlling their access to the system's features.

### 1.2. Core Tables

- `auth.users`: This is the core authentication table provided by Supabase. It stores the user's login credentials and basic metadata.
- `public.profiles`: This table extends the `auth.users` table and stores the user's profile information, such as their name, email, and role.
- `public.user_roles`: This table links users to their roles.
- `public.roles`: This table defines the available roles in the system.
- `public.account_activation_history`: This table tracks the activation status of user accounts.
- `public.account_setup_progress`: This table tracks the progress of a user's account setup, particularly for farmers who need to fill out a detailed form on the mobile app.

### 1.3. Relationships

```mermaid
erDiagram
    "auth.users" ||--o{ "public.profiles" : "has one"
    "public.profiles" ||--o{ "public.user_roles" : "has many"
    "public.user_roles" }o--|| "public.roles" : "belongs to"
    "public.profiles" ||--o{ "public.account_activation_history" : "has many"
    "public.profiles" ||--o{ "public.account_setup_progress" : "has many"
```

### 1.4. Triggers and Functions

- `handle_new_user()`: This trigger is fired when a new user is created in the `auth.users` table. It creates a corresponding record in the `public.profiles` table.
- `sync_profile_role()`: This trigger is fired when a user's role is changed in the `public.user_roles` table. It updates the `role` column in the `public.profiles` table.

## 2. Expert Onboarding and Verification

### 2.1. Overview

The expert onboarding and verification process is a state-driven workflow that ensures only qualified and approved experts can provide consultation services on the platform. The process is enforced at the database level to ensure data integrity and security.

### 2.2. State Diagram

```mermaid
graph TD
    A[Expert Signup] --> B{Pending Verification};
    B --> C{Admin Review};
    C --> D[Approved];
    C --> E[Rejected];
    D --> F[Active];
    E --> B;
```

### 2.3. Tables Involved

- `public.profiles`: Stores the expert's core profile information.
- `public.expert_profiles`: Stores specialized information for the expert, including their verification status.
- `public.expert_documents`: Stores the documents submitted by the expert for verification.
- `public.expert_verification_history`: Tracks the expert's verification status and timestamps.

### 2.4. Workflow

1.  **Expert Signup**: A new user signs up with the `expert` role. The `handle_new_user` trigger creates a new record in the `profiles` table with `account_activated` set to `false`.
2.  **Verification Submission**: The expert completes their profile and submits their verification documents. The `expert_profiles.verification_status` is set to `pending`.
3.  **Admin Review**: An admin reviews the expert's profile and documents.
4.  **Approval/Rejection**: The admin approves or rejects the expert. The `update_expert_verification_status` function is called to update the `expert_profiles.verification_status` and `profiles.account_activated` columns accordingly.

### 2.5. Expert Profile Management

The `expert_profiles` table is a critical component of the expert onboarding and verification process. It stores a wealth of information about each expert, which is used by admins to make informed decisions about their verification status.

#### Key Columns

- `id`: A foreign key that references the `id` column in the `profiles` table.
- `bio`: A text field that allows experts to provide a brief biography.
- `years_of_experience`: An integer that represents the expert's years of experience in their field.
- `education`: A text field that allows experts to provide details about their educational background.
- `verification_status`: An `expert_verification_status` ENUM that represents the expert's current verification status.
- `is_available`: A boolean that indicates whether the expert is currently available for consultations.
- `average_rating`: A numeric value that represents the expert's average rating from user reviews.
- `total_reviews`: An integer that represents the total number of reviews the expert has received.
- `expertise_area`: A text field that allows experts to specify their area of expertise.
- `qualifications`: A text field that allows experts to list their qualifications.
- `documents_required`: A boolean that indicates whether the expert is required to submit verification documents.
- `verified_at`: A timestamp that indicates when the expert was verified.
- `verified_by`: A UUID that references the admin who verified the expert.

## 3. Asset Registration

### 3.1. Overview

The asset registration workflow is an automated process that ensures data consistency when a new asset is registered on the platform. The process is triggered when an admin approves a registration request.

### 3.2. Tables Involved

- `public.registration_requests`: Stores the asset registration requests submitted by users.
- `public.assets`: Stores the approved and registered assets.
- `public.client_profiles`: Stores specialized information for farmers and factory workers, which is populated from the mobile app.

### 3.3. Relationships

```mermaid
erDiagram
    "public.profiles" ||--o{ "public.client_profiles" : "has one"
    "public.client_profiles" ||--o{ "public.assets" : "has many"
    "public.assets" ||--o{ "public.registration_requests" : "has many"
```

### 3.4. Workflow

1.  **User Submits Request**: A user submits an asset registration request via the mobile app. A new record is created in the `registration_requests` table with a `status` of `PENDING`.
2.  **Admin Approves Request**: An admin approves the registration request.
3.  **Trigger Execution**: The `on_registration_approved` trigger is executed.
4.  **Asset Creation**: The `handle_registration_approval` function is called, which in turn calls the `create_asset_from_request` function to create a new asset in the `assets` table.

## 4. Consultations Management

### 4.1. Overview

The consultation management system is designed to facilitate the process of requesting and providing consultation services. The system is designed to be flexible and scalable to support a large number of users and consultations.

### 4.2. Tables Involved

- `public.consultation_requests`: Stores the consultation requests submitted by users.
- `public.consultations`: Stores the details of the consultations.
- `public.messages`: Stores the chat messages for consultations.

### 4.3. Relationships

```mermaid
erDiagram
    "public.consultation_requests" ||--o{ "public.consultations" : "has many"
    "public.consultations" }o--|| "public.profiles" : "belongs to"
    "public.consultations" ||--o{ "public.messages" : "has many"
