-- This script unifies the expert verification status by removing the redundant
-- `verification_status` column from the `profiles` table and making the
-- `expert_profiles.verification_status` column the single source of truth.

-- 1. Remove the `verification_status` column from the `profiles` table
ALTER TABLE public.profiles
DROP COLUMN IF EXISTS verification_status;

-- 2. Update the `update_expert_verification_status` function to remove the logic
--    that updates the now-deleted `profiles.verification_status` column.
CREATE OR REPLACE FUNCTION public.update_expert_verification_status(
  p_expert_id uuid,
  p_new_status public.expert_verification_status,
  p_admin_notes text DEFAULT NULL,
  p_rejection_reasons text[] DEFAULT NULL
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER AS $$
DECLARE
    v_current_status expert_verification_status;
    v_admin_id UUID;
    v_result JSONB;
BEGIN
    -- Check if user is admin
    SELECT id INTO v_admin_id FROM public.profiles WHERE id = auth.uid() AND role = 'admin';
    IF v_admin_id IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: Only admins can update verification status';
    END IF;

    -- Get current verification status
    SELECT verification_status INTO v_current_status
    FROM public.expert_profiles
    WHERE id = p_expert_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Expert not found with id: %', p_expert_id;
    END IF;

    -- Update expert_profiles
    UPDATE public.expert_profiles
    SET
        verification_status = p_new_status,
        verified_at = CASE
            WHEN p_new_status = 'approved' THEN NOW()
            ELSE NULL
        END,
        verified_by = CASE
            WHEN p_new_status = 'approved' THEN v_admin_id
            ELSE NULL
        END,
        updated_at = NOW()
    WHERE id = p_expert_id;

    -- Update main profiles table account_activated
    UPDATE public.profiles
    SET
        account_activated = CASE
            WHEN p_new_status = 'approved' THEN true
            ELSE false
        END,
        updated_at = NOW()
    WHERE id = p_expert_id;

    -- Log the change in verification history
    INSERT INTO public.expert_verification_history (
        expert_id,
        previous_status,
        new_status,
        changed_by,
        admin_notes,
        rejection_reasons
    ) VALUES (
        p_expert_id,
        v_current_status,
        p_new_status,
        v_admin_id,
        p_admin_notes,
        p_rejection_reasons
    );

    -- Build result
    v_result := jsonb_build_object(
        'success', true,
        'expert_id', p_expert_id,
        'previous_status', v_current_status,
        'new_status', p_new_status,
        'message', 'Expert verification status updated successfully'
    );

    RETURN v_result;
END;
$$;

-- 3. Update the `handle_new_user` function to remove the logic that inserts
--    into the now-deleted `profiles.verification_status` column.
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER AS $$
BEGIN
  INSERT INTO public.profiles (
    id,
    email,
    first_name,
    last_name,
    phone_number,
    entity_type,
    account_activated,
    password_set,
    account_setup_completed,
    account_setup_step
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'first_name', '[Pending]'),
    COALESCE(NEW.raw_user_meta_data->>'last_name', '[Pending]'),
    NEW.raw_user_meta_data->>'phone_number',
    (NEW.raw_user_meta_data->>'entity_type')::public."EntityType",
    false,
    false,
    false,
    0
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone_number = EXCLUDED.phone_number,
    updated_at = NOW();

  RETURN NEW;
END;
$$;
