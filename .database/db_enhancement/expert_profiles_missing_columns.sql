-- Add missing columns to expert_profiles table for verification form
-- This script adds all the columns that are being submitted in the verification form
-- but are missing from the current expert_profiles table schema

-- Add missing columns to expert_profiles table
ALTER TABLE public.expert_profiles 
ADD COLUMN IF NOT EXISTS certifications TEXT,
ADD COLUMN IF NOT EXISTS current_position TEXT,
ADD COLUMN IF NOT EXISTS organization TEXT,
ADD COLUMN IF NOT EXISTS languages_spoken TEXT,
ADD COLUMN IF NOT EXISTS professional_memberships TEXT,
ADD COLUMN IF NOT EXISTS awards_honors TEXT;

-- Add comments to document the new columns
COMMENT ON COLUMN public.expert_profiles.certifications IS 'Professional certifications held by the expert';
COMMENT ON COLUMN public.expert_profiles.current_position IS 'Current job position/title of the expert';
COMMENT ON COLUMN public.expert_profiles.organization IS 'Current organization/company where the expert works';
COMMENT ON COLUMN public.expert_profiles.languages_spoken IS 'Languages spoken by the expert';
COMMENT ON COLUMN public.expert_profiles.professional_memberships IS 'Professional associations and memberships';
COMMENT ON COLUMN public.expert_profiles.awards_honors IS 'Awards, honors, and recognitions received';

-- Verify the columns were added successfully
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'expert_profiles' 
  AND table_schema = 'public'
  AND column_name IN ('certifications', 'current_position', 'organization', 'languages_spoken', 'professional_memberships', 'awards_honors')
ORDER BY column_name;
