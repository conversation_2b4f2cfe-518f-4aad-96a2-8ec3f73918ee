INSERT INTO "public"."profiles" ("id", "created_at", "updated_at", "email", "first_name", "last_name", "phone_number", "profile_picture_url", "email_verified", "phone_verified", "is_active", "language_preference", "password_set", "verification_status", "is_available", "account_activated", "account_setup_completed", "entity_type", "account_setup_step", "avatar_url", "role") VALUES ('bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da', '2025-09-22 22:53:00.598594+00', '2025-09-22 22:53:37.679662+00', '<EMAIL>', 'arrogant', 'alligator', null, null, 'true', 'false', 'true', 'en', 'false', 'pending', 'false', 'true', 'false', null, '0', null, 'farmer'), ('c6645c34-ea98-4852-b56b-ca4de7d1c01c', '2025-09-22 22:53:00.598594+00', '2025-09-22 22:53:37.679662+00', '<EMAIL>', 'psychological', 'duck', null, null, 'true', 'false', 'true', 'en', 'false', 'pending', 'false', 'true', 'false', null, '0', null, 'farmer');

INSERT INTO "auth"."users" ("instance_id", "id", "aud", "role", "email", "encrypted_password", "email_confirmed_at", "invited_at", "confirmation_token", "confirmation_sent_at", "recovery_token", "recovery_sent_at", "email_change_token_new", "email_change", "email_change_sent_at", "last_sign_in_at", "raw_app_meta_data", "raw_user_meta_data", "is_super_admin", "created_at", "updated_at", "phone", "phone_confirmed_at", "phone_change", "phone_change_token", "phone_change_sent_at", "confirmed_at", "email_change_token_current", "email_change_confirm_status", "banned_until", "reauthentication_token", "reauthentication_sent_at", "is_sso_user", "deleted_at", "is_anonymous") VALUES ('00000000-0000-0000-0000-000000000000', 'bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da', 'authenticated', 'authenticated', '<EMAIL>', '$2a$10$TpPG01Vzt.0ySCnzIfjDB.PVG3ghNFQIzIDSb9gIeAMfJhiL4rNuy', '2025-06-03 23:18:57.098227+00', null, '', null, '', null, '', '', null, '2025-09-30 15:43:31.707561+00', '{"provider": "email", "providers": ["email"]}', '{"sub": "bd1346bb-2c63-4c54-b4d3-a7f4c5bb08da", "role": "admin", "email": "<EMAIL>", "is_admin": true, "last_name": "alligator", "first_name": "arrogant", "phone_number": "98765431", "email_verified": true, "phone_verified": false}', null, '2025-06-03 23:18:47.043738+00', '2025-09-30 16:52:06.364865+00', null, null, '', '', null, '2025-06-03 23:18:57.098227+00', '', '0', null, '', null, 'false', null, 'false'), ('00000000-0000-0000-0000-000000000000', 'c6645c34-ea98-4852-b56b-ca4de7d1c01c', 'authenticated', 'authenticated', '<EMAIL>', '$2a$10$caqcs6QuAmeWqUT479zz6eob6MPDJ49Roj3fKVs2By7nGDeUi8Boi', '2025-06-26 12:37:33.491453+00', null, '', null, '', '2025-06-28 00:26:21.26866+00', '', '', null, '2025-09-22 23:01:22.618971+00', '{"provider": "email", "providers": ["email"]}', '{"sub": "c6645c34-ea98-4852-b56b-ca4de7d1c01c", "role": "expert", "email": "<EMAIL>", "is_admin": false, "last_name": "duck", "first_name": "psychological", "email_verified": true, "phone_verified": false}', null, '2025-06-26 12:37:33.455614+00', '2025-09-22 23:01:23.13565+00', null, null, '', '', null, '2025-06-26 12:37:33.491453+00', '', '0', null, '', null, 'false', null, 'false');