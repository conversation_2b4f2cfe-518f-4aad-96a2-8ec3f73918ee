## **Project "Agri-Industry Connect": Single Source of Truth**

**Version:** 1.0
**Date:** 22 July 2025
**Purpose:** This document serves as the primary reference for the development of the Agri-Industry Connect platform. It combines the original proposal with all subsequent client agreements and clarifications. All development should align with the specifications outlined here.

### **1. Project Vitals**

| Item | Details |
| :--- | :--- |
| **Project Name** | Agri-Industry Connect Platform (منصة تطوير الزراعة والصناعة) |
| **Primary Goal** | To connect farmers and factory workers with experts for remote consultation, improving productivity and problem-solving, with full offline capabilities for data entry. |
| **Key Stakeholders** | Project Owner (Client/Admin), <PERSON><PERSON><PERSON> (Ahmed Mesbah), Experts, Farmers, Factory Workers. |
| **Total Budget** | $150 |
| **Timeline** | 2 Months (August 2025 - September 2025) |
| **Key Deliverables** | Web Admin/Expert Dashboard, Cross-Platform Mobile App (iOS/Android). |
| **Ownership** | The client (Project Owner) will have full ownership of all source code for the web platform and mobile application. |

### **2. Summary of Key Agreements & Changes from Original Proposal**

This project will follow the original proposal with the following critical additions and clarifications based on client feedback:

1.  **Admin-Approved Registration:** Farmers and Factory Workers do **not** create their farm/factory profiles directly. They submit a registration request via the mobile app, which the **Project Admin must approve** via the web dashboard before the user's profile is activated.
2.  **Offline-First Mobile App:** The mobile application **must** function without an internet connection for core tasks like creating consultation requests. All data entered offline will be saved locally and **automatically synchronized** with the server once an internet connection is available. New recommendations from experts will be delivered via push notifications upon connection.
3.  **Future-Proofing for AI:** The system architecture will be designed to allow for future integration of AI models for automated recommendations, even though this is not part of the current scope.
4.  **Client-Defined Data Fields:** The specific data fields for registering Farms and Factories are now defined and must be implemented as specified in Section 6.

### **3. User Roles and Permissions**

| Role | Platform | Key Permissions |
| :--- | :--- | :--- |
| **Project Admin (مالك المشروع)** | Web Dashboard | - **Approve/Reject** new Farm and Factory registration requests. <br> - Manage all user accounts (Experts, Farmers, Workers). <br> - Invite new Experts. <br> - Monitor all consultation requests and their statuses. <br> |
| **Expert (الخبير)** | Web Dashboard | - View and accept assigned/available consultation requests. <br> - Communicate directly with users via a chat interface. <br> - Analyze user-submitted data (text, images). <br> - Provide official recommendations and solutions. <br> - Mark requests as "Resolved". <br> - Manage their own profile (specialization, etc.). |
| **Farmer / Factory Worker (المزارع / العامل)** | Mobile App | - Register a new Farm or Factory (pending Admin approval). <br> - **Create new consultation requests** (with text & images), both online and **offline**. <br> - View the status of their requests. <br> - Chat with the assigned Expert. <br> - Receive recommendations and solutions via the app and push notifications. <br> - View their profile and historical requests. |

### **4. Key Workflows**

#### **Workflow 1: New User (Farm/Factory) Registration & Approval**

1.  A new user downloads the mobile app.
2.  The user selects their category: "Agriculture" (زراعة) or "Industry" (صناعة).
3.  The user fills out the detailed registration form for their farm/factory (as specified in Section 6).
4.  Upon submission, the request is sent to the Admin's approval queue on the web dashboard. The user's app is in a "pending approval" state.
5.  The **Admin** reviews the submitted data.
6.  The **Admin** clicks "Approve" or "Reject".
7.  The user receives a push notification about the decision. If approved, their account is fully activated, and they can now create consultation requests.

#### **Workflow 2: Consultation Request (From Creation to Resolution)**

1.  A Farmer/Worker opens the mobile app and clicks "Create New Request".
2.  They fill in a title, a detailed description of the problem, and attach relevant photos.
3.  **If Offline:** The request is saved locally on the device.
4.  **Once Online:** The app automatically syncs the saved request to the server.
5.  The new request appears on the **Expert's** web dashboard.
6.  An Expert is assigned or claims the request. The request status changes to "In Progress".
7.  The Expert reviews the details. If more information is needed, they send a message through the built-in chat.
8.  The user receives a push notification for the new message and replies via the app.
9.  Once the Expert has enough information, they write their final recommendation/solution and mark the request as "Resolved".
10. The user is notified that their issue has been resolved and can view the final recommendation. The request is archived for future reference.

### **5. Technical Stack**

*   **Web Dashboard:** **Next.js**
*   **Mobile App:** **React Native** (using the **Expo** framework) for iOS & Android.
*   **Backend & Database:** **Supabase** (PostgreSQL, Authentication, Auto-generated APIs).
*   **Hosting:**
    *   Web Dashboard: **Netlify**
    *   Backend/Database: **Supabase Cloud**

### **6. Data Models & Required Fields**

#### **A. Farm Registration Data (بيانات قسم الزراعة)**

*   **Basic Information:**
    *   `farm_name` (اسم المزرعة)
    *   `location` (موقع المزرعة - المحافظة، المدينة، الإحداثيات)
    *   `owner_name` (اسم صاحب المزرعة)
    *   `contact_info` (رقم الهاتف والبريد الإلكتروني)
*   **Farm Details:**
    *   `farm_type` (نوع المزرعة - نباتية / حيوانية / مختلطة)
    *   `land_area` (مساحة الأرض)
    *   `water_source` (مصدر المياه - شبكة ري، بئر، ...)
    *   `soil_type` (نوع التربة - طينية / رملية / ...)
    *   `number_of_workers` (عدد العمال)
*   **Crops & Schedule:**
    *   `current_crops` (المحاصيل المزروعة حاليًا)
    *   `seasonal_schedule` (جدول الزراعة الموسمية)
*   **Equipment:**
    *   `equipment_list` (قائمة بالمعدات - جرارات، ماكينات ري...)
    *   `equipment_status` (حالة المعدات - جيدة / تحتاج صيانة / معطلة)
    *   `last_maintenance_date` (تاريخ آخر صيانة)
*   **Sensors (Optional):**
    *   `uses_sensors` (هل تستخدم حساسات؟ - نعم / لا)
    *   `sensor_types` (نوع الحساسات المستخدمة)
    *   `sensor_data_method` (توصيل بيانات الحساسات - مباشر / يدوي)

#### **B. Factory Registration Data (بيانات قسم الصناعة)**

*   **Basic Information:**
    *   `factory_name` (اسم المصنع)
    *   `location` (موقع المصنع - المدينة، المنطقة الصناعية، ...)
    *   `owner_name` (اسم مالك المصنع)
    *   `contact_info` (رقم الهاتف والبريد الإلكتروني)
*   **Factory Details:**
    *   `industry_type` (نوع الصناعة - غذائية / كيميائية / ...)
    *   `daily_production_volume` (حجم الإنتاج اليومي)
    *   `number_of_workers` (عدد العمال)
    *   `number_of_shifts` (عدد الورديات - 1، 2، 3)
*   **Machinery & Equipment:**
    *   `machinery_list` (قائمة الآلات الأساسية)
    *   `machine_technical_status` (الحالة الفنية لكل آلة)
    *   `last_maintenance_date` (تاريخ آخر صيانة لكل آلة)
    *   `daily_operating_hours` (عدد ساعات التشغيل اليومي)
*   **Factory Environment:**
    *   `average_temperature` (درجة الحرارة المتوسطة)
    *   `has_ventilation_system` (توفر نظام تهوية وتبريد - نعم / لا)
*   **Sensors (Optional):**
    *   `uses_sensors` (هل تستخدم مستشعرات؟ - نعم / لا)
    *   `sensor_types` (أنواع المستشعرات - اهتزاز، حرارة، ...)
    *   `data_connection_method` (كيفية توصيل البيانات - API / Excel / يدوي)

**Note:** The client requested that these forms be easy to fill, suggesting the use of checkboxes and dropdowns where possible. There should also be an option to upload data via Excel/CSV for the Admin.

### **7. Project Timeline & Milestones**

*   **Phase 1: Backend & Web Dashboard (Month 1 - August 2025)**
    *   Setup Supabase backend, database schemas, and authentication.
    *   Develop Admin Dashboard: User management, expert invitation, and **the critical farm/factory approval system**.
    *   Develop Expert Dashboard: Consultation queue, chat interface, and recommendation submission.

*   **Phase 2: Mobile Application (Month 2 - September 2025)**
    *   Develop user registration flow (for both Agriculture and Industry).
    *   Implement the consultation creation form.
    *   **Implement robust offline data storage and automatic synchronization logic.**
    *   Integrate chat and push notification system.
    *   Build UI for viewing request history and final recommendations.
    *   Final Testing and Deployment to App Stores.