# Web Portal: Onboarding Guide & Technical Roadmap

**Version:** 2.3 (Final)

Welcome to the Web Portal team! This guide serves as your technical entry point for understanding the project architecture, your development environment, and the core features you will be building.

---

## Part 1: Development Environment & Database Synchronization

Your first step is to set up a local development environment with an accurate database schema. This is essential for preventing bugs and integration issues.

### Option A: Docker-Based Schema Pull (Recommended)

For a reliable, containerized approach that works across all environments:

#### 1.1 Install Docker (if not already installed)

Follow the comprehensive Docker installation guide in `.guides/pull-latest-schema-guide.md`.

#### 1.2 Pull Latest Schema Using Docker

```bash
# Set your database connection string
export DATABASE_URL="*******************************************************/your_database?sslmode=require"

# Run pg_dump via Docker to get the complete schema
sudo docker run --rm -v "$(pwd)":/dump postgres:15 pg_dump --schema-only -f /dump/full_schema.sql "$DATABASE_URL"
```

This creates `full_schema.sql` with the complete database schema including all tables, functions, triggers, and permissions.

#### 1.3 Update Project Schema Files

```bash
# Update Supabase schema reference
cp full_schema.sql supabase/schemas/schema.sql

# Update database backup (optional)
cp full_schema.sql database-backups/schema.sql
```

### Option B: Traditional CLI Tools

#### 1. Update the Prisma Schema (`schema.prisma`)

To ensure your Prisma Client is aware of all database tables, run:

```bash
npx prisma db pull
```

#### 2. Update the Supabase SQL Schema (`schema.sql`)

To get an up-to-date reference for all tables, functions, and triggers, run:

```bash
npx supabase db dump -s public,auth --file supabase/schemas/schema.sql
```

**Note:** The Docker approach (Option A) is recommended as it provides more reliable schema dumps and avoids system-level PostgreSQL installations.

---

## Part 2: The Core Onboarding & Approval Workflow

This is the most critical workflow to understand. User account activation and the creation of their first asset are **one single, interconnected process**. The web portal's role is to serve as the administrative gatekeeper for this flow.

**End-to-End Workflow:**

1.  **User Signup (Mobile App):** A new user signs up. The `handle_new_user` trigger creates a `public.profiles` record for them, but with `account_activated` set to `false`. At this point, the user **cannot log in**.

2.  **Asset Submission (Mobile App):** The app immediately prompts the user to register their first asset (e.g., a farm). This submission does **not** create an asset directly. Instead, it creates a record in the `public.registration_requests` table with a `status` of `PENDING`.

3.  **Admin Review (Web Portal Task):** This is the primary task for the web portal team. You will build an interface that:
    *   Lists all requests from the `registration_requests` table where `status` is `PENDING`.
    *   Displays the details for each request from the `registration_data` JSON column for an admin to review.
    *   Provides an **"Approve"** button.

4.  **The Approval Action (Web Portal Backend):** The *only* action the "Approve" button needs to trigger is updating the status of the request in the database:
    ```sql
    UPDATE public.registration_requests
    SET status = 'APPROVED'
    WHERE id = 'the_request_id';
    ```

5.  **Automated Backend Magic (The Trigger):** The `UPDATE` command from the previous step fires the `on_registration_approved` trigger. This trigger automatically executes the `handle_registration_approval` function, which performs two critical actions:
    *   **Creates the Asset:** It parses the `registration_data` and inserts a new, official record into the `public.assets` table.
    *   **Activates the User:** It finds the user associated with the request and sets their `public.profiles.account_activated` flag to `true`.

**Conclusion:** Once the admin approves the first asset registration, the user's account is automatically activated, and they can then log in to the mobile app. Your team's core responsibility is building the UI for Step 3 and the simple backend action for Step 4.

---

## Part 3: Database Schema Overview

A high-level overview of the key tables in this workflow:

*   **`public.profiles`**
    *   **Purpose:** The central table for user data and status.
    *   **Key Columns:** `id` (links to `auth.users`), `account_activated` (boolean gatekeeper for login).

*   **`public.registration_requests`**
    *   **Purpose:** A **staging area** for pending asset registrations. This is the table your team will primarily interact with for the approval workflow.
    *   **Key Columns:** `id`, `user_id`, `status` (enum: `PENDING`, `APPROVED`), `registration_data` (JSON containing all the details of the submitted asset).

*   **`public.assets`**
    *   **Purpose:** Stores the final, approved asset records.
    *   **Behavior:** Records are only created in this table *after* an admin approves a request in the `registration_requests` table.

*   **Other Tables:** `consultations` and `messages` are used for the real-time chat feature between users and experts.

---

## Part 4: Key Development Tasks

These are the initial high-priority tasks for the web portal team.

### Task 1: Implement the Notification System

**Objective:** Build the backend service to send push notifications to mobile users when an admin takes action on the web portal.

*   **Architecture:** When an event occurs (like an approval), insert a row into the `public.notifications` table. A Supabase Edge Function should listen for these new rows and use a service like Expo's Push API to send the notification.
*   **Key Events:** User account activation, asset approval, consultation acceptance, new chat messages.

### Task 2: Implement Row-Level Security (RLS)

**Objective:** Secure the database by enabling and creating RLS policies for all tables containing sensitive or user-specific data.

*   **Core Principles:**
    *   **Mobile Users:** Must be strictly confined to their own data (e.g., `SELECT * FROM assets WHERE owner_id = auth.uid()`).
    *   **Web Portal Users:** Access must be role-based (e.g., an `expert` can only see consultations where `expert_id = auth.uid()`, while an `admin` can see all records).
*   **Action:** Define and apply RLS policies for all relevant tables, using helper functions like `is_admin()` where necessary to check roles.