# Expert Verification Feature Finalization Plan

## 📋 Executive Summary

This comprehensive development plan addresses the complete finalization of the expert verification feature, incorporating both expert-facing UI/UX enhancements and critical admin-facing functionalities. The plan resolves existing backend console errors and implements the requirements specified in the project context.

---

## 🎯 Phase 1: Expert-Facing Experience Overhaul

### Task 1.1: Redesign Verification Status Banner
**Priority: HIGH | Estimated Time: 4-6 hours**

#### Current Issues:
- Generic English-only messaging
- Blue theme with status badges and icons
- Status displayed as "PENDING" tags

#### Implementation:

**1.1.1 Update Verification Status Banner Component**
**File:** `src/components/verification-status-banner.tsx`

**Changes Required:**
```tsx
// Remove status badges and exclamation icons
- <Badge variant={...}>{status.replace('_', ' ').toUpperCase()}</Badge>
- {urgent && (<Badge variant="destructive">Action Required</Badge>)}

// Update styling - light yellow background with black text
const getStatusStyling = () => ({
  backgroundColor: '#FFFFE0', // Light yellow
  color: '#000000',           // Black text
  border: '1px solid #E5E5E5'
});

// Update button styling - black background with white text
<Button 
  className="bg-black text-white hover:bg-gray-800"
  size="sm"
>
  {action}
  <ArrowRight className="ml-2 h-4 w-4" />
</Button>
```

**1.1.2 Add Arabic Translations**
**File:** `src/locales/ar.json`

**Additions Required:**
```json
{
  "verification": {
    "completeVerification": "أكمل عملية التحقق من الخبرة",
    "waitingForVerification": "في انتظار التحقق",
    "startVerification": "ابدأ التحقق",
    "verificationDescription": "لتتمكن من الوصول إلى جميع الميزات، يرجى إكمال طلب التحقق من خبرتك"
  }
}
```

**1.1.3 Update Status Logic**
```tsx
// Update status messages based on verification state
const getStatusInfo = () => {
  switch (status) {
    case 'not_submitted':
      return {
        title: t("verification.completeVerification"),
        description: t("verification.verificationDescription"),
        action: t("verification.startVerification")
      };
    case 'pending':
      return {
        title: t("verification.waitingForVerification"),
        description: "تم إرسال طلبك وهو قيد المراجعة من قبل فريقنا",
        action: "عرض الحالة"
      };
    // ... other cases
  }
};
```

---

### Task 1.2: Overhaul Verification Form Page
**Priority: HIGH | Estimated Time: 8-10 hours**

#### Current Issues:
- Tabbed interface creates friction
- Generic titles and descriptions
- Missing RTL support in some areas
- No integrated document upload instructions

#### Implementation:

**1.2.1 Redesign Page Layout**
**File:** `src/app/dashboard/verification-pending/page.tsx`

**Major Changes:**
```tsx
// Replace tabbed interface with single scrollable form
return (
  <div className="container mx-auto p-6" dir={locale === "ar" ? "rtl" : "ltr"}>
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">
          {t("verification.verifyAccountTitle")} {/* "Verify Your Account to Get Started" */}
        </CardTitle>
        <CardDescription>
          {t("verification.verifyAccountDescription")} {/* "Fill in the form below..." */}
        </CardDescription>
        
        {/* Accuracy Notice */}
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mt-4">
          <p className="text-blue-800 text-sm">
            {t("verification.accuracyNotice")} {/* "Please ensure all information is accurate..." */}
          </p>
        </div>
      </CardHeader>

      <CardContent>
        <form className="space-y-8">
          {/* Basic Information Section */}
          <section>
            <h3 className="text-lg font-semibold mb-4 border-b pb-2">
              {t("verification.basicInformation")}
            </h3>
            {/* Form fields */}
            
            {/* Document Upload Integration */}
            <div className="mt-4 p-4 border-2 border-dashed border-gray-300 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">
                {t("verification.uploadNationalId")} {/* "Upload a clear image of your National ID" */}
              </p>
              <input type="file" accept="image/*,.pdf" />
            </div>
          </section>

          {/* Education Section */}
          <section>
            <h3 className="text-lg font-semibold mb-4 border-b pb-2">
              {t("verification.education")}
            </h3>
            {/* Form fields */}
            
            {/* Document Upload Integration */}
            <div className="mt-4 p-4 border-2 border-dashed border-gray-300 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">
                {t("verification.uploadCertifications")} {/* "Upload relevant certifications" */}
              </p>
              <input type="file" accept=".pdf,.jpg,.png" multiple />
            </div>
          </section>

          {/* Experience Section */}
          <section>
            <h3 className="text-lg font-semibold mb-4 border-b pb-2">
              {t("verification.experience")}
            </h3>
            {/* Form fields */}
          </section>

          {/* Submit Button */}
          <div className="pt-6 border-t">
            <Button 
              onClick={handleSubmitForm} 
              disabled={submitting}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3"
            >
              {submitting ? t("verification.submitting") : t("verification.submitForReview")}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
);
```

**1.2.2 Add Required Arabic Translations**
**File:** `src/locales/ar.json`

**New Translation Keys:**
```json
{
  "verification": {
    "verifyAccountTitle": "تحقق من حسابك للبدء",
    "verifyAccountDescription": "املأ النموذج أدناه ليتم مراجعته من قبل فريقنا والبدء",
    "accuracyNotice": "يرجى التأكد من أن جميع المعلومات المقدمة دقيقة وأصلية لتسريع عملية المراجعة",
    "basicInformation": "المعلومات الأساسية", 
    "education": "التعليم",
    "experience": "الخبرة",
    "uploadNationalId": "ارفع صورة واضحة لهويتك الوطنية",
    "uploadCertifications": "ارفع الشهادات ذات الصلة",
    "submitForReview": "إرسال للمراجعة",
    "submitting": "جاري الإرسال..."
  }
}
```

**1.2.3 Implement RTL Support**
- Add proper `dir` attribute handling
- Update CSS classes for RTL layout
- Ensure form inputs work correctly in Arabic

---

### Task 1.3: Implement Non-Invasive Feature Locking
**Priority: MEDIUM | Estimated Time: 6-8 hours**

#### Current Issues:
- Hard redirects create poor UX
- No clear messaging on restricted pages

#### Implementation:

**1.3.1 Create Overlay Component**
**File:** `src/components/verification-overlay.tsx`

```tsx
"use client";

import { useUserProfile } from "@/hooks/use-user-profile";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ShieldAlert } from "lucide-react";
import Link from "next/link";
import { useTranslation } from "@/components/i18n-provider";

export function VerificationOverlay() {
  const { role, expertProfile } = useUserProfile();
  const { t } = useTranslation();
  
  // Only show for unverified experts
  const shouldShow = role === 'expert' && 
    expertProfile?.verification_status !== 'approved';

  if (!shouldShow) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <Card className="max-w-md mx-auto">
        <CardContent className="p-6 text-center">
          <ShieldAlert className="h-12 w-12 mx-auto mb-4 text-orange-500" />
          <h3 className="text-lg font-semibold mb-2">
            {t("verification.accessRestricted")}
          </h3>
          <p className="text-gray-600 mb-4">
            {t("verification.verificationRequired")}
          </p>
          <Link href="/dashboard/verification-pending">
            <Button className="w-full">
              {t("verification.completeVerification")}
            </Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  );
}
```

---

## 🛠️ Phase 2: Admin Dashboard Functionality & Management

### Task 2.1: Fix User Management Data Fetching
**Priority: CRITICAL | Estimated Time: 4-6 hours**

#### Current Issues:
- No users showing in expert/client tabs
- Incorrect database queries causing 406 errors

#### Implementation:

**2.1.1 Fix Database Queries**
**File:** `src/app/dashboard/users/page.tsx`

**Current Problematic Query:**
```tsx
// BROKEN - verification_status doesn't exist in profiles table
.select(`id, first_name, last_name, email, created_at, verification_status`)
```

**Fixed Query:**
```tsx
// FIXED - Proper join with expert_profiles
const { data: experts, error } = await supabase
  .from('profiles')
  .select(`
    id, 
    first_name, 
    last_name, 
    email, 
    created_at,
    account_activated,
    expert_profiles!inner(
      verification_status,
      bio,
      years_of_experience,
      education,
      expert_type
    )
  `)
  .eq('expert_profiles.verification_status', 'pending'); // or other filters
```

**2.1.2 Update Data Processing**
```tsx
// Process the joined data correctly
const processedExperts = experts?.map(expert => ({
  ...expert,
  verification_status: expert.expert_profiles?.verification_status || 'pending',
  bio: expert.expert_profiles?.bio || '',
  years_of_experience: expert.expert_profiles?.years_of_experience || 0
})) || [];
```

**2.1.3 Fix Client Tab Data Fetching**
```tsx
// Fix client data fetching with proper joins
const { data: clients, error: clientsError } = await supabase
  .from('profiles')
  .select(`
    id,
    first_name,
    last_name,
    email,
    created_at,
    is_active,
    client_profiles!inner(
      client_type,
      created_at
    )
  `);
```

**2.1.4 Implement Client Details Routing**
```tsx
// Add navigation to client details
const handleClientView = (clientId: string) => {
  router.push(`/dashboard/clients/${clientId}`);
};
```

---

### Task 2.2: Enhance Expert Detail Page Functionality
**Priority: HIGH | Estimated Time: 6-8 hours**

#### Current Issues:
- Approve/Reject buttons not using database functions properly
- Missing consultations display for approved experts

#### Implementation:

**2.2.1 Fix Data Fetching**
**File:** `src/app/dashboard/users/[id]/page.tsx`

```tsx
// Use the database function for comprehensive data
useEffect(() => {
  const fetchExpertDetails = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('get_expert_verification_details', {
        p_expert_id: id
      });
      
      if (error) throw error;
      
      setExpertData(data);
      setUser(data.profile);
    } catch (error) {
      console.error('Error fetching expert details:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load expert details"
      });
    } finally {
      setLoading(false);
    }
  };
  
  fetchExpertDetails();
}, [id]);
```

**2.2.2 Add Consultations Tab for Approved Experts**
```tsx
// Add consultations tab that only shows for approved experts
{expertData?.verification_status === 'approved' && (
  <TabsContent value="consultations">
    <Card>
      <CardHeader>
        <CardTitle>Expert Consultations</CardTitle>
        <CardDescription>
          All consultations handled by this expert
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ConsultationsList expertId={id} />
      </CardContent>
    </Card>
  </TabsContent>
)}
```

**2.2.3 Create Consultations List Component**
**File:** `src/components/consultations-list.tsx`

```tsx
"use client";

import { useState, useEffect } from "react";
import { supabase } from "@/supabase/client";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface ConsultationsListProps {
  expertId: string;
}

export function ConsultationsList({ expertId }: ConsultationsListProps) {
  const [consultations, setConsultations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchConsultations = async () => {
      try {
        const { data, error } = await supabase
          .from('consultation_requests')
          .select(`
            id,
            problem_description,
            status,
            created_at,
            requester:profiles!requester_id(first_name, last_name),
            asset:assets!asset_id(name, asset_type)
          `)
          .eq('expert_id', expertId)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setConsultations(data || []);
      } catch (error) {
        console.error('Error fetching consultations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConsultations();
  }, [expertId]);

  if (loading) {
    return <div>Loading consultations...</div>;
  }

  return (
    <div className="space-y-4">
      {consultations.map((consultation) => (
        <Card key={consultation.id}>
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium">{consultation.problem_description}</h4>
                <p className="text-sm text-gray-600">
                  Client: {consultation.requester?.first_name} {consultation.requester?.last_name}
                </p>
                <p className="text-sm text-gray-600">
                  Asset: {consultation.asset?.name} ({consultation.asset?.asset_type})
                </p>
              </div>
              <Badge variant={consultation.status === 'completed' ? 'default' : 'secondary'}>
                {consultation.status}
              </Badge>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {consultations.length === 0 && (
        <p className="text-center text-gray-500 py-8">
          No consultations found for this expert
        </p>
      )}
    </div>
  );
}
```

---

### Task 2.3: Build Client Detail Page
**Priority: MEDIUM | Estimated Time: 6-8 hours**

#### Implementation:

**2.3.1 Create Client Detail Page**
**File:** `src/app/dashboard/clients/[id]/page.tsx`

```tsx
"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { supabase } from "@/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function ClientDetailPage() {
  const { id } = useParams();
  const [client, setClient] = useState(null);
  const [assets, setAssets] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchClientDetails = async () => {
      try {
        // Fetch client profile
        const { data: clientData, error: clientError } = await supabase
          .from('profiles')
          .select(`
            *,
            client_profiles!inner(
              client_type,
              created_at
            )
          `)
          .eq('id', id)
          .single();

        if (clientError) throw clientError;
        setClient(clientData);

        // Fetch client assets
        const { data: assetsData, error: assetsError } = await supabase
          .from('assets')
          .select('*')
          .eq('owner_id', id);

        if (assetsError) throw assetsError;
        setAssets(assetsData || []);

      } catch (error) {
        console.error('Error fetching client details:', error);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchClientDetails();
    }
  }, [id]);

  if (loading) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  if (!client) {
    return <div className="flex justify-center p-8">Client not found</div>;
  }

  return (
    <div className="container mx-auto p-6">
      {/* Back Button */}
      <div className="mb-6">
        <Link href="/dashboard/users">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
        </Link>
      </div>

      {/* Client Information */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Client Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium">Name</h4>
              <p>{client.first_name} {client.last_name}</p>
            </div>
            <div>
              <h4 className="font-medium">Email</h4>
              <p>{client.email}</p>
            </div>
            <div>
              <h4 className="font-medium">Client Type</h4>
              <Badge>{client.client_profiles.client_type}</Badge>
            </div>
            <div>
              <h4 className="font-medium">Member Since</h4>
              <p>{new Date(client.created_at).toLocaleDateString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Client Assets */}
      <Card>
        <CardHeader>
          <CardTitle>Registered Assets</CardTitle>
        </CardHeader>
        <CardContent>
          {assets.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {assets.map((asset) => (
                <Card key={asset.id}>
                  <CardContent className="p-4">
                    <h4 className="font-medium">{asset.name}</h4>
                    <Badge variant="outline" className="mt-2">
                      {asset.asset_type}
                    </Badge>
                    <p className="text-sm text-gray-600 mt-2">
                      Registered: {new Date(asset.created_at).toLocaleDateString()}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <p className="text-center text-gray-500 py-8">
              No assets registered for this client
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
```