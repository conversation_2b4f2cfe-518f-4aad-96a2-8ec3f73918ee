# GEMINI.md - Agri Industrial Project

This file provides a comprehensive overview of the Agri Industrial Project, its structure, and development conventions to be used as instructional context for future interactions.

## Project Overview

This is a modern web application for agricultural industrial management built with a robust and scalable tech stack.

*   **Frontend:**
    *   **Framework:** [Next.js](https://nextjs.org/) (a React framework)
    *   **Language:** [TypeScript](https://www.typescriptlang.org/)
    *   **Styling:** [Tailwind CSS](https://tailwindcss.com/) with `tailwindcss-rtl` for right-to-left language support.
    *   **UI Components:** A rich set of UI components from [Radix UI](https://www.radix-ui.com/) and icons from [Lucide React](https://lucide.dev/).
    *   **Testing:** [Vitest](https://vitest.dev/) for unit/component testing and [Playwright](https://playwright.dev/) for end-to-end testing.

*   **Backend:**
    *   **Platform:** [Supabase](https://supabase.com/)
    *   **Authentication:** Supabase Auth is used for user management, with protected routes under `/dashboard`.
    *   **Database:** Supabase Postgres database.
    *   **APIs:** Serverless functions are likely used for backend logic.

*   **Deployment & CI/CD:**
    *   **Hosting:** The project is configured for deployment on [Netlify](https://www.netlify.com/).
    *   **CI/CD:** [GitHub Actions](https://github.com/features/actions) are used for continuous integration and deployment.

## Building and Running

### Prerequisites

*   Node.js 18.x or later
*   npm or yarn
*   Git
*   Supabase account and project

### Local Development

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd Web-app
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    Create a `.env.local` file in the root directory and add your Supabase credentials:
    ```
    NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
    NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
    ```

4.  **Run the development server:**
    ```bash
    npm run dev
    ```
    The application will be available at [http://localhost:3000](http://localhost:3000).

### Key Scripts

*   `npm run dev`: Starts the development server.
*   `npm run build`: Creates a production build of the application.
*   `npm run start`: Starts the production server.
*   `npm run test`: Runs unit tests with Vitest.
*   `npm run test:e2e`: Runs end-to-end tests with Playwright.

## Development Conventions

*   **Coding Style:** The project uses ESLint with `next/core-web-vitals` and `next/typescript` configurations to enforce code quality and consistency.
*   **Authentication:**
    *   Authentication is handled via Supabase.
    *   The `middleware.ts` file protects routes starting with `/dashboard`, redirecting unauthenticated users to the `/sign-in` page.
    *   Authenticated users trying to access `/sign-in` or `/sign-up` are redirected to the `/dashboard`.
*   **Database:**
    *   The Supabase configuration is located in `supabase/config.toml`.
    *   Database migrations and seeds are managed through the Supabase CLI.
*   **Internationalization (i18n):** The presence of `tailwindcss-rtl` suggests that the application is designed to support both left-to-right (LTR) and right-to-left (RTL) languages.
*   **Error Monitoring:** The project is integrated with [Sentry](https://sentry.io/) for error tracking and monitoring.
