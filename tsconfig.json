{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["src/components/*"], "@/lib/*": ["src/lib/*"], "@/utils/*": ["src/utils/*"], "@/styles/*": ["src/styles/*"], "@/types/*": ["src/types/*"]}, "plugins": [{"name": "next"}], "typeRoots": ["./node_modules/@types", "./src/types"], "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "removeComments": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next.config.js"], "exclude": ["node_modules", ".next", "out"]}