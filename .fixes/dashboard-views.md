# Plan: Distinct Dashboard Views for Admin and Expert Users

This plan outlines the steps to create separate, role-based dashboard views for "admin" and "expert" users.

## 1. Create Role-Specific View Components

- **Create `src/components/dashboard/admin-view.tsx`**: This component will encapsulate the entire view for admin users. It will include the `DashboardMetrics` component and any other admin-specific UI elements.
- **Create `src/components/dashboard/expert-view.tsx`**: This component will encapsulate the view for expert users, including elements like "Consultation Requests" and "Upcoming Consultations."

## 2. Refactor the Main Dashboard Page

- **Convert to Server Component**: The main dashboard page, `src/app/dashboard/page.tsx`, will be converted into a server component by removing the `"use client"` directive.
- **Fetch User Role**: The page will fetch the current user's role using the `getUserRole` utility.
- **Conditionally Render Views**: Based on the fetched role, the page will render either the `AdminView` or `ExpertView` component.

## 3. Pass Data to View Components

- **Pass Profile Data**: The `profile` data fetched in the layout will be passed down to the role-specific view components, ensuring they have access to all necessary user information.

This approach will result in a clean, maintainable, and scalable solution for managing different dashboard experiences based on user roles.