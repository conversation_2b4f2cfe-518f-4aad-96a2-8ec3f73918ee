# Combined Authentication Flow Fixes Plan

This plan outlines the implementation steps to resolve two separate but related issues in the authentication system:
1.  A critical bug preventing expert sign-in.
2.  A non-critical but noisy `AuthSessionMissingError` in the browser console.

---

## Part 1: Fix Expert Sign-In Failure

### Issue Summary

The expert sign-in process fails with a `TypeError: response is undefined` on the client.

### Root Cause

The `signInAction` in `src/app/actions.ts` has a bug where it fails to return a structured error response if it encounters an issue while creating a new expert's profile in the database. This causes the server action to return `undefined`, which the client-side code cannot handle.

### Implementation Steps

1.  **Modify `src/app/actions.ts`:**
    -   Locate the `signInAction` function.
    -   Inside the logic for creating an expert profile (`if (!expertProfile)`), add a `return` statement to the `if (createError)` block.
    -   The function must return `{ success: false, error: "Failed to create expert profile. Please try again." }` to ensure the client always receives a valid JSON response.

**Code Snippet (Target Fix):**
```typescript
// File: src/app/actions.ts
// ... inside signInAction
if (!expertProfile) {
  const { error: createError } = await supabase
    .from("expert_profiles")
    .insert({ /* ... */ });

  if (createError) {
    console.error("Error creating expert profile:", createError);
    // FIX: Add this return statement
    return { success: false, error: "Failed to create expert profile. Please contact support." };
  }
}
```

---

## Part 2: Resolve `AuthSessionMissingError`

### Issue Summary

The browser console shows an `AuthSessionMissingError` on initial page loads for unauthenticated users.

### Root Cause

The `getInitialSession` function in `src/components/auth-provider.tsx` calls `supabase.auth.getUser()` *before* calling `supabase.auth.getSession()`. When no session exists, `getUser()` throws the expected error. The correct pattern is to check for a session first.

### Implementation Steps

1.  **Refactor `src/components/auth-provider.tsx`:**
    -   Locate the `getInitialSession` function within the `useEffect` hook.
    -   Restructure the logic to first call `supabase.auth.getSession()`.
    -   Only if a `session` object is returned should the code proceed to call `supabase.auth.getUser()`.
    -   If there is no session, set the `user` and `session` states to `null` and exit the function.

**Code Snippet (Target Refactor):**
```typescript
// File: src/components/auth-provider.tsx
// ... inside useEffect for getInitialSession
const getInitialSession = async () => {
  try {
    // 1. Get session first
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) throw sessionError;

    if (session) {
      // 2. Only get user if session exists
      const { data: { user: authUser }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      setSession(session);
      setUser(authUser);
    } else {
      // No session, so no user.
      setSession(null);
      setUser(null);
    }

  } catch (error) {
    console.error('Error getting initial session:', error);
    setSession(null);
    setUser(null);
  } finally {
    setIsLoading(false);
  }
};
```

This combined plan will create a more stable, robust, and clean authentication experience.