# Plan: Forgot Password Implementation

This document outlines the detailed plan to implement a secure forgot password feature for both admin and expert users of the web application.

## 1. High-Level Objective

The goal is to create a seamless, secure password recovery flow that integrates with Supabase Auth. This involves creating a "Forgot Password" page, a "Reset Password" page, and configuring the associated email templates and server-side logic. The implementation will handle both Admin and Expert user roles.

## 2. Component & File Breakdown

The implementation will involve creating one new page and modifying two existing files.

1.  **New File**: `src/app/(auth)/forgot-password/page.tsx` - A new page where users can request a password reset link.
2.  **Modified File**: `src/app/actions.ts` - To implement the correct logic for sending the reset email and updating the user's password.
3.  **Modified File**: `src/app/auth/set-password/page.tsx` - To handle both the initial password setup and the password reset flow.

## 3. Step-by-Step Implementation Plan

### Step 1: Create the "Forgot Password" Page

I will create a new page component at `src/app/(auth)/forgot-password/page.tsx`.

*   **UI:** The page will feature a simple form with a single field for the user's email address and a "Send Reset Link" button.
*   **Action:** The form will submit to the `forgotPasswordAction` server action defined in `src/app/actions.ts`.
*   **Feedback:** Toast notifications will be used to provide feedback to the user (e.g., "If an account exists for this email, a reset link has been sent.").

### Step 2: Implement Server Actions

I will update the `forgotPasswordAction` and `resetPasswordAction` in `src/app/actions.ts`.

*   **`forgotPasswordAction`**:
    *   This action will call `supabase.auth.resetPasswordForEmail`.
    *   Crucially, the `redirectTo` option will be set to point to the `auth/set-password` page. This ensures that when the user clicks the link in the email, they are taken to the correct page in our application to set their new password.

*   **`resetPasswordAction`** (to be used by the `set-password` page):
    *   This action will call `supabase.auth.updateUser` with the new password.
    *   It will perform validation to ensure the password and confirmation password match and meet security requirements.

### Step 3: Enhance the "Set Password" Page

The page at `src/app/auth/set-password/page.tsx` will be refactore to handle two distinct scenarios:
1.  **Initial Password Setup**: For new users who have just confirmed their email.
2.  **Password Reset**: For existing users who have followed a password reset link.

*   **Logic:** The page will check the URL for a `code` query parameter. The presence of this code indicates a password reset flow.
*   **If `code` is present:** The component will first attempt to exchange the code for a valid session using `supabase.auth.exchangeCodeForSession`. Upon success, the user will be allowed to set a new password.
*   **If no `code` is present:** The page will function as it currently does, for initial password setup, relying on the existing authenticated session.
*   The form's `onSubmit` handler will call the `resetPasswordAction` to finalize the password change.

### Step 4: Configure Supabase Email Template

For a professional user experience, the password reset email should be customized.

*   **Location:** In the Supabase Dashboard, navigate to `Authentication` -> `Email Templates`.
*   **Template:** Select the "Reset Password" template.
*   **Customization:** The template will be updated to match the application's branding. It is essential that the reset link remains correct:
    ```html
    <a href="{{ .SiteURL }}/auth/set-password?code={{ .Token }}">Reset Password</a>
    ```
    (Note: Supabase handles the token generation; we just need to ensure the URL points to our `set-password` page).

## 4. Visual Flow (Mermaid Diagram)

```mermaid
sequenceDiagram
    participant User
    participant SignInPage as "Sign-In Page"
    participant ForgotPasswordPage as "Forgot Password Page"
    participant ServerActions as "actions.ts"
    participant SupabaseAuth as "Supabase Auth"
    participant UserEmail as "User's Email"
    participant SetPasswordPage as "Set Password Page"

    User->>SignInPage: Clicks "Forgot Password?"
    SignInPage->>ForgotPasswordPage: Navigates user
    User->>ForgotPasswordPage: Enters email and submits
    ForgotPasswordPage->>ServerActions: Calls forgotPasswordAction(email)
    ServerActions->>SupabaseAuth: Calls resetPasswordForEmail(email)
    SupabaseAuth->>UserEmail: Sends password reset email
    User->>UserEmail: Clicks reset link in email
    UserEmail->>SetPasswordPage: Redirects user with token
    User->>SetPasswordPage: Enters and submits new password
    SetPasswordPage->>ServerActions: Calls resetPasswordAction(newPassword)
    ServerActions->>SupabaseAuth: Calls updateUser({ password })
    SupabaseAuth-->>SetPasswordPage: Confirms password update
    SetPasswordPage-->>User: Shows success message and redirects to sign-in
```

This plan creates a secure, user-friendly, and maintainable forgot password feature that leverages Supabase's built-in capabilities while fitting perfectly into the existing application structure.