# User Management Feature Enhancement & Linting Plan

## 1. Executive Summary

This document outlines a plan to enhance the administrator's user management page and resolve all outstanding linting issues in the user settings form. The feature enhancements include robust filtering, sorting, and searching capabilities. The linting fixes will be addressed by refactoring the `SettingsForm` component to improve state management and remove all unused variables and props. This plan ensures a clean, maintainable, and highly functional user management system.

## 2. Feature Enhancements & Fixes

### 2.1. Admin View: Core Feature Enhancements

1.  **Filtering & Sorting:**
    *   Implement dropdown filters for `verification_status` and `is_available`.
    *   Enable clickable table headers for sorting by name, join date, and status.
2.  **Search:**
    *   Enhance the search to query against `first_name` and `last_name`.
3.  **UI Enhancements:**
    *   Add a new column for "Availability" to the user table.
    *   Add a "Back" button to the user detail page.
    *   Align the "Actions" column header to the left.

### 2.2. Bug Fixes & Refactoring

1.  **Comprehensive Lint Fix for `settings-form.tsx`:**
    *   **State Refactoring:** The component will be refactored to use a single `formData` state object managed by a `useReducer` hook. This is a more scalable pattern than multiple `useState` calls and will help eliminate stale state issues.
    *   **Unused Variable Removal:** All unused variables, including `expertProfile`, and any others flagged by the linter, will be removed.
    *   **Prop Drilling:** The `updateProfileAction` will be updated to pass a single `formData` object, reducing prop drilling.

## 3. Development Task List

### Phase 1: Settings Form Refactor (The Lint Fix)

1.  **[ ] Task 1.1: Refactor `SettingsForm` State**
    *   In `src/app/dashboard/settings/settings-form.tsx`, create a `reducer` function to manage all form state transitions.
    *   Implement a `useReducer` hook to manage the form's state.
    *   Update all form inputs to dispatch actions to the reducer.
2.  **[ ] Task 1.2: Clean Up Unused Variables**
    *   Remove all unused variables from the component, including `expertProfile`.

### Phase 2: Frontend Enhancements

1.  **[ ] Task 2.1: Enhance Admin Users Page (`/dashboard/users/page.tsx`)**
    *   Implement the filtering, sorting, and UI enhancements as detailed in the previous plan.
2.  **[ ] Task 2.2: Update User Detail Page (`/dashboard/users/[id]/page.tsx`)**
    *   Add the "Back" button.