# Implementing Supabase Password Reset in Next.js: Complete Guide

## Overview

Implementing a secure password reset feature with Supabase in Next.js requires careful configuration of email templates, server-side authentication handling, and proper token verification. This guide provides the latest best practices for implementing this functionality automatically through Supabase's built-in token verification system, eliminating the need for custom token management.

## Prerequisites

Before implementing the password reset feature, ensure you have the following setup:

### Required Packages

Install the necessary Supabase packages for Next.js:

```bash
npm install @supabase/supabase-js @supabase/ssr
```

### Environment Configuration

Create a `.env.local` file with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Supabase Client Setup

### Server-Side Client

Create `utils/supabase/server.ts`:

```typescript
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}
```

### Client-Side Client

Create `utils/supabase/client.ts`:

```typescript
import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```

## Middleware Configuration

Create `middleware.ts` in your project root:

```typescript
import { type NextRequest } from 'next/server'
import { updateSession } from '@/utils/supabase/middleware'

export async function middleware(request: NextRequest) {
  return await updateSession(request)
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

Create `utils/supabase/middleware.ts`:

```typescript
import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) =>
            request.cookies.set(name, value)
          )
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const {
    data: { user },
  } = await supabase.auth.getUser()

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.redirect() make sure to:
  // 1. Pass along cookies (supabaseResponse.cookies.getAll())
  // 2. Set headers (supabaseResponse.headers.getAll())
  return supabaseResponse
}
```

## Email Template Configuration

This is a crucial step that enables automatic token processing through Supabase. Navigate to your Supabase Dashboard → Authentication → Email Templates and configure the Reset Password template[1]:

```html

  Password Reset
  Follow this link to reset the password for your user:
  
    
      Reset Password
    
  
  If you did not request this, please ignore this email.

```

Key points about the email template:
- Uses `{{ .TokenHash }}` instead of manual token generation[2]
- Sets `type=recovery` for password reset verification[2]
- Redirects to your confirmation route which handles automatic verification[3]

## Authentication Route Handler

Create `app/auth/confirm/route.ts` to handle the automatic token verification:

```typescript
import { type EmailOtpType } from '@supabase/supabase-js'
import { type NextRequest } from 'next/server'
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/'

  if (token_hash && type) {
    const supabase = await createClient()

    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })

    if (!error) {
      // User is now authenticated - redirect to password update page
      redirect(next)
    } else {
      // Redirect to error page with error message
      redirect(`/auth/error?error=${encodeURIComponent(error.message)}`)
    }
  }

  // If no token_hash or type, redirect to error page
  redirect('/auth/error?error=Missing token or type')
}
```

This route handler automatically:
- Extracts the `token_hash` and `type` from the URL parameters[3][4]
- Uses `supabase.auth.verifyOtp()` to validate the token and establish a session[2]
- Redirects the authenticated user to the password update page[4]

## Password Reset Request API

Create `app/api/reset-password/route.ts` to handle password reset requests:

```typescript
import { NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: Request) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm`,
    })

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json({ 
      message: 'Password reset email sent successfully' 
    })

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
```

## Forgot Password Form

Create `app/auth/forgot-password/page.tsx`:

```typescript
'use client'

import { useState, useTransition } from 'react'
import { useRouter } from 'next/navigation'

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [isPending, startTransition] = useTransition()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setMessage('')

    startTransition(async () => {
      try {
        const response = await fetch('/api/reset-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email }),
        })

        const data = await response.json()

        if (!response.ok) {
          setError(data.error || 'Something went wrong')
          return
        }

        setMessage('Check your email for the password reset link!')
      } catch (error) {
        setError('Something went wrong. Please try again.')
      }
    })
  }

  return (
    
      
        
          
            Reset your password
          
        
        
          
            
              Email address
            
             setEmail(e.target.value)}
              disabled={isPending}
            />
          

          {error && (
            {error}
          )}

          {message && (
            {message}
          )}

          
            {isPending ? 'Sending...' : 'Send reset email'}
          
        
      
    
  )
}
```

## Password Update Form

Create `app/auth/update-password/page.tsx`:

```typescript
'use client'

import { useState, useTransition, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/utils/supabase/client'

export default function UpdatePasswordPage() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')
  const [isPending, startTransition] = useTransition()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth/login')
        return
      }
      setIsAuthenticated(true)
    }

    checkAuth()
  }, [supabase.auth, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (password !== confirmPassword) {
      setError('Passwords do not match')
      return
    }

    if (password.length  {
      try {
        const { error } = await supabase.auth.updateUser({
          password: password
        })

        if (error) {
          setError(error.message)
          return
        }

        // Password updated successfully
        router.push('/auth/login?message=Password updated successfully')
      } catch (error) {
        setError('Something went wrong. Please try again.')
      }
    })
  }

  if (!isAuthenticated) {
    return Loading...
  }

  return (
    
      
        
          
            Update your password
          
        
        
          
            
              
                New Password
              
               setPassword(e.target.value)}
                disabled={isPending}
              />
            
            
              
                Confirm New Password
              
               setConfirmPassword(e.target.value)}
                disabled={isPending}
              />
            
          

          {error && (
            {error}
          )}

          
            {isPending ? 'Updating...' : 'Update password'}
          
        
      
    
  )
}
```

## Error Handling Page

Create `app/auth/error/page.tsx`:

```typescript
'use client'

import { useSearchParams } from 'next/navigation'
import Link from 'next/link'

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')

  return (
    
      
        
          
            Authentication Error
          
          
            
              {error || 'An error occurred during authentication'}
            
          
          
            
              Try requesting a new password reset link
            
          
        
      
    
  )
}
```

## Site URL Configuration

In your Supabase Dashboard, configure the following settings:

1. **Site URL**: Set to your domain (e.g., `https://yourdomain.com` or `http://localhost:3000` for development)
2. **Redirect URLs**: Add your authentication routes:
   - `https://yourdomain.com/auth/confirm`
   - `https://yourdomain.com/auth/update-password`

## Key Benefits of This Implementation

### Automatic Token Verification
This implementation leverages Supabase's built-in token verification system using `verifyOtp()` method[2]. You don't need to:
- Manually validate tokens
- Store temporary tokens in your database
- Implement custom expiration logic
- Handle token security concerns

### Secure Session Management
The authentication flow automatically:
- Validates the `token_hash` from the email link[3][4]
- Establishes a proper user session after verification[4]
- Protects the password update page with session authentication[5]

### Production-Ready Error Handling
The implementation includes comprehensive error handling for:
- Invalid or expired tokens[6]
- Network failures during API calls
- Authentication state validation
- User-friendly error messages

## Common Troubleshooting

### "Auth Session Missing" Error
This commonly occurs when the user session isn't properly established. The solution is to ensure the `verifyOtp()` method is called before accessing protected routes[7][6].

### PKCE Flow Issues in Production
Some users experience issues with PKCE flow in production environments like Vercel. This implementation uses the `token_hash` approach which is more reliable than the `code` parameter method[7][8].

### Email Template Configuration
Ensure your email template uses the exact format provided above. Common issues include:
- Using `{{.Token}}` instead of `{{.TokenHash}}`[1]
- Incorrect URL parameters in the reset link[9]
- Missing `type=recovery` parameter[2]

This implementation provides a robust, secure, and user-friendly password reset system that handles token verification automatically through Supabase's built-in functionality, eliminating the need for custom token management while maintaining security best practices[10][2][11].

[1] https://supabase.com/docs/guides/auth/auth-email-templates
[2] https://github.com/orgs/supabase/discussions/28655
[3] https://supabase.com/ui/docs/nextjs/password-based-auth
[4] https://www.yannisspyrou.com/posts/password-recovery-nextjs-supabase
[5] https://stackoverflow.com/questions/77933751/supabase-and-next-js-how-to-make-change-password-page-accessible-only-to-authe
[6] https://stackoverflow.com/questions/76733274/supabase-reset-password-error-auth-session-missing
[7] https://github.com/supabase/supabase/issues/27816
[8] https://stackoverflow.com/questions/77621185/nextjs-supabase-reset-password-email-verification-token
[9] https://github.com/orgs/supabase/discussions/29976
[10] https://supabase.com/docs/reference/javascript/auth-resetpasswordforemail
[11] https://github.com/orgs/supabase/discussions/29222
[12] https://www.youtube.com/watch?v=hPy6ncBzPTM
[13] https://supabase.com/docs/reference/kotlin/auth-resetpasswordforemail
[14] https://www.youtube.com/watch?v=7FemRsuDdGg
[15] https://stackoverflow.com/questions/79421869/supabase-reset-password-token
[16] https://www.reddit.com/r/Supabase/comments/16s64k1/reset_password_flow_in_nextjs_using_pages/
[17] https://www.reddit.com/r/Supabase/comments/1gng0hh/password_reset_tracking/
[18] https://github.com/supabase/supabase/issues/491
[19] https://stackoverflow.com/questions/79324183/next-js-15-supabase-ssr-auth-i-cant-secure-the-reset-password-route
[20] https://www.reddit.com/r/Supabase/comments/16o8keh/nextjs_password_reset/
[21] https://supabase.com/docs/guides/auth/server-side/nextjs
[22] https://www.youtube.com/watch?v=a6iIfclwPcw
[23] https://supabase.com/docs/reference/python/auth-resetpasswordforemail
[24] https://www.reddit.com/r/Supabase/comments/ry3zt4/nextjs_password_reset/
[25] https://techstaunch.com/blogs/implementing-authentication-in-next-js-with-supabase?tech_blog=true
[26] https://supabase.com/docs/guides/local-development/customizing-email-templates
[27] https://github.com/orgs/supabase/discussions/3360
[28] https://supabase.com/docs/guides/auth/jwts
[29] https://supabase.com/docs/guides/auth/auth-helpers/auth-ui
[30] https://github.com/orgs/supabase/discussions/20145