# User Management Feature Enhancement Plan

## 1. Executive Summary

This document outlines a plan to enhance the administrator's user management page with advanced features, including robust filtering, sorting, and searching capabilities. It also includes fixes for existing UI bugs and a linting issue. The goal is to provide administrators with a powerful and intuitive interface for managing a large number of users efficiently. This plan will be implemented after the successful consolidation of the database schema proposed in the `user-management-refinement.md` plan.

## 2. Feature Enhancements & Fixes

### 2.1. Admin View: Core Feature Enhancements

1.  **Filtering & Sorting:**
    *   **By Status:** Implement a dropdown filter to allow admins to view users by their `verification_status` (e.g., "Pending", "Approved", "Active", "Rejected").
    *   **By Availability:** Add a filter for an expert's `is_available` status.
    *   **Sorting:** Enable clickable table headers to sort users by name, join date, and status.
2.  **Search:**
    *   The current search will be enhanced to explicitly query against the `first_name` and `last_name` fields for more accurate results.
3.  **UI Enhancements:**
    *   **More Detailed View:** The main user table will be updated to include columns for `is_available` status.
    *   **Back Button:** A "Back" button will be added to the user detail page (`/dashboard/users/[id]`) for improved navigation.
    *   **Action Column Alignment:** The "Actions" column title will be aligned to the left to match the position of the action buttons, improving visual consistency.

### 2.2. Bug Fixes

1.  **Lint Issue in `settings-form.tsx`:** The unused `expertProfile` variable will be removed from the `useUserProfile` hook destructuring to resolve the linting error.

## 3. Development Task List

### Phase 1: Backend (No changes needed, relying on previous migration)

*   The database schema and RPC functions from the `user-management-refinement.md` plan are sufficient for these enhancements.

### Phase 2: Frontend Implementation

1.  **[ ] Task 2.1: Fix Lint Issue in `settings-form.tsx`**
    *   In `src/app/dashboard/settings/settings-form.tsx`, remove the `expertProfile` variable from the `useUserProfile` destructuring.
2.  **[ ] Task 2.2: Enhance Admin Users Page (`/dashboard/users/page.tsx`)**
    *   **Add State for Filters/Sorting:** Introduce new state variables to manage the current filter and sort order.
    *   **Update `fetchUsers` Query:** Modify the Supabase query to dynamically add `where` and `order` clauses based on the new state.
    *   **Implement UI Controls:**
        *   Add a `DropdownMenu` component for status filtering.
        *   Make `TableHead` components clickable to trigger sorting.
    *   **Refine UI Table:**
        *   Add a new column for "Availability."
        *   Adjust the "Actions" column header alignment.
3.  **[ ] Task 2.3: Update User Detail Page (`/dashboard/users/[id]/page.tsx`)**
    *   Add a "Back" button that navigates the user to the previous page.