# Plan: Comprehensive Authentication Flow Refactoring

## 1. Executive Summary

This document outlines a complete, three-phase plan to overhaul the expert user authentication system. The current implementation suffers from critical flaws, including a broken signup confirmation flow, an unreliable password reset mechanism, and inconsistent session management. These issues stem from incorrect email template usage, flawed client-side state handling on the `set-password` page, and inadequate route protection.

This plan will address these root causes by:
1.  **Fixing the New User Signup Flow:** Ensuring new users receive the correct confirmation email and are seamlessly guided to set their password.
2.  **Hardening the Forgot Password Flow:** Eliminating the "invalid flow state" error to create a reliable password reset process.
3.  **Implementing Robust Session Management:** Ensuring that logged-in users are correctly redirected to the dashboard and that public routes are protected.

The outcome will be a secure, reliable, and user-friendly authentication experience.

## 2. In-Depth Diagnosis of Core Issues

### 2.1. Incorrect Email for New Users
-   **Files Involved:** `src/app/actions.ts`
-   **The Issue:** New users are receiving a "Magic Link" email instead of a "Confirm Signup" email. This points to a misconfiguration in the `signUpAction` or Supabase email settings, leading to a broken initial password setup.
-   **The Fix:** We will correct the `emailRedirectTo` option in the `signUpAction` to point to the `auth/set-password` page. This will ensure that new users are redirected to the correct page after confirming their email.

### 2.2. "Invalid Flow State" on Password Reset
-   **Files Involved:** `src/app/auth/set-password/page.tsx`
-   **The Issue:** The `set-password` page fails during the code exchange process, especially in a development environment with Fast Refresh. This is caused by the component re-rendering and attempting to use the single-use PKCE code more than once.
-   **The Fix:** We will use a `useRef` flag to ensure the code exchange logic runs only once, even if the component re-renders.

### 2.3. Dual-Purpose Page Failure
-   **Files Involved:** `src/app/auth/set-password/page.tsx`
-   **The Issue:** The `set-password` page is not correctly handling its two required functions: initial password setup (which relies on an active session after email confirmation) and password reset (which relies on a URL code). It fails when no code is present.
-   **The Fix:** We will refactor the `useEffect` hook in the `SetPasswordComponent` to handle both scenarios.

### 2.4. Inadequate Route Protection
-   **Files Involved:** `middleware.ts`, `src/components/auth-provider.tsx`
-   **The Issue:** Authenticated users are not being consistently redirected away from public pages (like `/sign-in`), indicating that the middleware and client-side auth provider are not working in harmony.
-   **The Fix:** We will simplify the `AuthProvider` and consolidate all route protection logic in the middleware.

## 3. Step-by-Step Implementation Plan

---

### **Phase 1: Fix the New User Signup & Confirmation Flow**

*The goal of this phase is to ensure a new expert can sign up, confirm their email, and set their password without errors.*

**Step 1.1: Correct the `signUpAction` Email Redirect**

The `signUpAction` in `src/app/actions.ts` needs to explicitly point to the `set-password` page as the destination after a user confirms their email. This ensures the user is taken to the right place to complete their profile setup.

-   **File:** `src/app/actions.ts`
-   **Action:** Modify the `emailRedirectTo` option within the `supabase.auth.signUp` call to point directly to the intended `set-password` page.

**Step 1.2: Verify Supabase Email Templates**

We must ensure Supabase is configured to send the correct email for new user confirmations.

-   **Location:** Supabase Dashboard > Authentication > Email Templates.
-   **Action:**
    1.  Confirm that the **Confirm Signup** template is active and uses `{{ .ConfirmationURL }}`.
    2.  Ensure that the **Magic Link** template is **not** being used as the default for email confirmations. This is a common source of the errors you're seeing.

**Step 1.3: Refactor `set-password` Page to Handle Both User Journeys**

This is the most critical fix. The `set-password` page must be able to handle both a new user setting their password for the first time and an existing user resetting it.

-   **File:** `src/app/auth/set-password/page.tsx`
-   **Action:** The `useEffect` hook in the `SetPasswordComponent` will be refactored.
    -   If a `code` is present in the URL, it will proceed with the **password reset** flow (exchanging the code for a session).
    -   If no `code` is present, it will check for an **active session**. If a session exists (from a new user clicking their confirmation link), it will allow them to set their password.
    -   If neither a `code` nor a session is found, it will display a clear error message.

---

### **Phase 2: Harden the Forgot Password Flow**

*The goal of this phase is to create a reliable password reset experience for existing experts.*

**Step 2.1: Ensure Robust Code Exchange on `set-password` Page**

To prevent the "invalid flow state" error, we will use a `useRef` flag to ensure the code exchange logic runs only once, even if the component re-renders due to Fast Refresh.

-   **File:** `src/app/auth/set-password/page.tsx`
-   **Action:** Implement a `useRef` hook (`exchangeAttemptedRef`) that is set to `true` the first time the code exchange is attempted. Subsequent renders will see this flag and skip the exchange, preventing the error.

---

### **Phase 3: Fix the Expert Login Flow**

*The goal of this phase is to ensure that experts can log in reliably and that the UI correctly reflects their authenticated state.*

**Step 3.1: Refactor the `signInAction`**

The `signInAction` will be refactored to return a JSON object instead of using redirects. This will allow the client-side to handle the response and redirect the user only after the `AuthProvider` has been updated.

-   **File:** `src/app/actions.ts`
-   **Action:** Modify the `signInAction` to return a `{ success: true }` or `{ success: false, error: '...' }` object.

**Step 3.2: Update the Sign-In Page**

The sign-in page will be updated to handle the JSON response from the `signInAction`.

-   **File:** `src/app/(auth)/sign-in/page.tsx`
-   **Action:** The `handleFormSubmit` function will be updated to:
    1.  Await the response from the `signInAction`.
    2.  If the login is successful, manually trigger a `router.push('/dashboard')`.
    3.  If the login fails, display an error message.

---

### **Phase 4: Solidify Session Management and Route Protection**

*The goal of this phase is to ensure a seamless and secure navigation experience for authenticated users.*

**Step 4.1: Simplify the `AuthProvider`**

The `AuthProvider` should only be responsible for listening to auth state changes and providing the `user` and `session` objects to the rest of the application.

-   **File:** `src/components/auth-provider.tsx`
-   **Action:** Remove any complex logic or side effects from the `onAuthStateChange` listener. Its only job will be to update its internal state when Supabase reports a `SIGNED_IN`, `SIGNED_OUT`, or `TOKEN_REFRESHED` event.

**Step 4.2: Consolidate Route Protection in Middleware**

The middleware is the single best place to manage application-wide routing rules.

-   **File:** `middleware.ts`
-   **Action:** Enhance the middleware logic to:
    1.  Define a list of `protectedRoutes` (e.g., `['/dashboard']`) and `publicRoutes` (e.g., `['/sign-in', '/sign-up']`).
    2.  If a user is **not authenticated** and tries to access a protected route, redirect them to `/sign-in`.
    3.  If a user **is authenticated** and tries to access a public-only route, redirect them to `/dashboard`.

## 4. Visualizing the New, Unified Flow

```mermaid
graph TD
    subgraph "New User Signup"
        A[User Submits Signup Form] --> B{signUpAction};
        B --> C[Supabase Sends 'Confirm Signup' Email];
        C --> D[User Clicks Link];
        D --> E[/auth/set-password];
        E -- "No Code, Has Session" --> F[Set Password Form];
    end

    subgraph "Password Reset"
        G[User Submits Forgot Password Form] --> H{forgotPasswordAction};
        H --> I[Supabase Sends 'Reset Password' Email];
        I --> J[User Clicks Link];
        J --> K[/auth/set-password?code=...];
        K -- "Has Code" --> L[Exchange Code & Show Form];
    end

    subgraph "Session Management"
        M[User Navigates] --> N{Middleware};
        N -- "Is Authenticated?" --> O{Route Check};
        O -- "Accessing Public Route" --> P[Redirect to /dashboard];
        O -- "Accessing Protected Route" --> Q[Allow Access];
    end