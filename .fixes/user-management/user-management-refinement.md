# User Management & Verification System: Refined Implementation Plan

## 1. Executive Summary

The current implementation of the user verification system is failing due to a critical schema mismatch. The application logic is attempting to read from and write to an `expert_profiles` table, but the necessary status columns (`verification_status`, `is_available`) do not exist on the main `profiles` table, leading to errors and inconsistent UI behavior.

This plan outlines a refined strategy to consolidate all expert-related data directly into the `profiles` table. This will simplify the database schema, streamline the queries, and create a more robust and maintainable system. The fix involves a new database migration, updated RPC functions, and simplified frontend logic.

## 2. Problem Analysis

The root cause of all the persistent issues is the architectural decision to split user data between a `profiles` table and an `expert_profiles` table. This has led to:

*   **Schema Mismatch:** The RPC functions attempt to `UPDATE` columns on a table where they don't exist.
*   **Complex Queries:** The frontend needs to perform complex joins to get a complete user picture, leading to race conditions and stale data.
*   **Inconsistent State:** The UI cannot reliably determine an expert's true status, as the data is fragmented and often incomplete.

The provided `profiles` schema confirms that the necessary columns for verification and availability are missing.

## 3. Proposed Solution: Schema Consolidation

To resolve this permanently, I propose we consolidate all expert-specific flags into the main `profiles` table. This is a common and efficient pattern for handling user subtypes.

### 3.1. Database Schema Changes

A new database migration will be created to add the required columns to the `profiles` table.

```sql
-- Add verification_status to profiles
ALTER TABLE public.profiles
ADD COLUMN verification_status TEXT DEFAULT 'pending';

-- Add is_available for experts to profiles
ALTER TABLE public.profiles
ADD COLUMN is_available BOOLEAN DEFAULT false;
```

This change eliminates the need for the `expert_profiles` table for verification, simplifying our data model significantly.

### 3.2. Backend RPC Function Updates

The existing RPC functions will be updated to operate directly on the `profiles` table.

```sql
-- Updated approve_expert function
CREATE OR REPLACE FUNCTION approve_expert(expert_user_id uuid)
RETURNS void AS $$
BEGIN
  UPDATE public.profiles
  SET 
    verification_status = 'approved',
    is_available = true,
    updated_at = now()
  WHERE id = expert_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Updated reject_expert function
CREATE OR REPLACE FUNCTION reject_expert(expert_user_id uuid)
RETURNS void AS $$
BEGIN
  UPDATE public.profiles
  SET 
    verification_status = 'rejected',
    is_available = false,
    updated_at = now()
  WHERE id = expert_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

These functions will be placed in a new migration file to ensure they are correctly applied.

### 3.3. Frontend Logic Simplification

The frontend components will be refactored to remove all joins to `expert_profiles` and instead query the new columns directly from `profiles`.

*   **Admin Users Page (`/dashboard/users`):** The `fetchUsers` function will be simplified to a direct `SELECT` on the `profiles` table.
*   **User Detail Page (`/dashboard/users/[id]`):** The data fetch will be simplified, and the UI will conditionally render expert-specific fields based on the user's role and the new status columns.
*   **Middleware:** The route protection logic in `middleware.ts` will be updated to check `verification_status` on the `profiles` table.

## 4. Development Task List

### Phase 1: Database & Backend (The Foundation)

1.  **[ ] Task 1.1: Create New Database Migration**
    *   Create a new SQL file in the `migrations` folder.
    *   Add the `ALTER TABLE` statements to add `verification_status` and `is_available` to the `profiles` table.
2.  **[ ] Task 1.2: Update RPC Functions**
    *   In the same migration file, add the `CREATE OR REPLACE FUNCTION` statements for the updated `approve_expert` and `reject_expert` functions.

### Phase 2: Frontend Implementation (The User Experience)

1.  **[ ] Task 2.1: Overhaul Admin Users Page (`/dashboard/users/page.tsx`)**
    *   Update the `fetchUsers` query to remove the `expert_profiles` join.
    *   Select `verification_status` and `is_available` directly from `profiles`.
    *   Implement an **Optimistic UI Update** for the `handleApprove` and `handleReject` functions to provide immediate feedback.
2.  **[ ] Task 2.2: Refactor Settings Page (`/dashboard/settings/settings-form.tsx`)**
    *   Update the form to read the `verification_status` from the `useUserProfile` hook and display it correctly.
3.  **[ ] Task 2.3: Update Middleware (`src/middleware.ts`)**
    *   Modify the route protection logic to check the `verification_status` from the `profiles` table to prevent unverified experts from accessing protected routes.