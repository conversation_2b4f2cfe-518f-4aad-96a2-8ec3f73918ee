User Verification Plan
Overview
This plan addresses the synchronization and management of user verification statuses, focusing on the admin view for expert management and the expert view for access control and settings updates. The goal is to ensure accurate UI rendering, proper button visibility, and restricted access based on verification status.
Main Goals

Admin View:
Update the expert users table to correctly display verification status in the status column.
Remove "Approve" and "Reject" buttons for experts who are already approved or rejected.


Experts View:
Prevent unverified experts from accessing core app functionality.
Correctly update and display the verification status on the settings page.



Task List for Development
Admin View Tasks

Update UI to Reflect Verification Status
Modify src/app/dashboard/users/page.tsx to fetch and display verification_status from the profiles table.
Ensure the status column uses the updated verification_status value.


Conditional Button Rendering
Adjust the table row rendering in src/app/dashboard/users/page.tsx to hide "Approve" and "Reject" buttons when verification_status is "approved" or "rejected".


Database Schema Update
Add verification_status and is_available columns to the profiles table to align with the UI and RPC logic.



Experts View Tasks

Access Control for Unverified Experts
Implement a middleware or route guard in src/middleware.ts to check verification_status and redirect unverified experts to a verification-pending page.


Update Settings Page
Modify src/app/dashboard/my-profile/page.tsx or src/app/dashboard/settings/page.tsx to display and update verification_status.


Database Schema Update
Ensure verification_status and is_available are populated correctly in profiles after the schema change.



Implementation Approach
Step 1: Database Schema Modification

Add verification_status and is_available to profiles using SQL ALTER TABLE.
Migrate existing expert_profiles data to profiles to maintain consistency (manual step post-migration).

Step 2: Admin View Implementation

Update the fetchUsers function in src/app/dashboard/users/page.tsx to use the new profiles columns.
Adjust the table rendering logic to conditionally hide buttons based on verification_status.

Step 3: Experts View Implementation

Add a verification check in src/middleware.ts using Supabase auth and redirect logic.
Update the settings page component to reflect verification_status and prevent edits if unverified.

Step 4: Testing and Validation

Test the admin view for correct status display and button behavior.
Test expert access and settings page updates across verified and unverified states.

Notes

The plan assumes verification_status values are "pending", "approved", "rejected", or "in_review".
Coordination with backend teams is needed to handle data migration from expert_profiles to profiles.
Post-implementation, review RLS policies in .danger/profile-management-backend-setup.md to ensure access aligns with the new schema.

