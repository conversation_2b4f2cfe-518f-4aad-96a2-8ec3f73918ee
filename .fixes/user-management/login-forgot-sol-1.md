### Understanding and Resolving the "Invalid Link" Error in the Forgot Password Flow

You're encountering an "invalid link" error in the forgot password flow of your application, accompanied by a `404 Not Found` error when attempting to exchange a PKCE code for a session, and server logs indicating "invalid flow state, no valid flow state found." This issue stems from problems with the PKCE (Proof Key for Code Exchange) flow state management in your Supabase authentication setup. Below, I'll explain the issue, identify the root causes based on your logs and code, and provide a complete solution to resolve it.

---

### Problem Overview

The forgot password flow involves the following steps:
1. **User Requests Password Reset**: Via the `/forgot-password` page, triggering `forgotPasswordAction` in `src/app/actions.ts`, which sends a reset email with a link redirecting to `/auth/set-password?code=<code>`.
2. **User Clicks Reset Link**: The `set-password` page (`src/app/auth/set-password/page.tsx`) attempts to exchange the provided `code` for a session using `supabase.auth.exchangeCodeForSession`.
3. **Session Exchange Fails**: The exchange fails with a `404` error, and the server reports "invalid flow state," resulting in the "invalid link" error displayed to the user.

Your console logs show:
- `POST https://jrhbvcjwxvyrxrmgjfbu.supabase.co/auth/v1/token?grant_type=pkce 404 (Not Found)`
- `Code exchange error: invalid flow state, no valid flow state found`

Server logs confirm:
- `"error_code": "flow_state_not_found"`
- `"error": "404: invalid flow state, no valid flow state found"`

This indicates that the PKCE flow state, which Supabase uses to validate the code exchange, is either missing, expired, or mismatched.

---

### Root Cause Analysis

Based on the logs, code, and provided screenshots, the issue arises from several factors:

1. **PKCE Flow State Mismatch or Expiry**:
   - The PKCE flow requires a `state` parameter to be maintained between the initial request (sending the reset email) and the code exchange. If this state is not properly persisted or expires before the user clicks the link, Supabase returns a `404` error with "flow_state_not_found."
   - In your `forgotPasswordAction`, the `redirectTo` URL is set to `${origin}/auth/set-password`, but there's no explicit handling of the PKCE state parameter, which Supabase expects.

2. **Multiple Code Exchange Attempts**:
   - The `set-password` page uses `sessionStorage` to track processed codes (`processed_pkce_code_${code}`). However, `sessionStorage` is cleared when the browser tab is closed or refreshed, potentially leading to multiple exchange attempts if the page re-renders (e.g., due to Next.js Fast Refresh in development). Supabase rejects reused codes, causing the `404` error.

3. **Session State Inconsistency**:
   - The `AuthProvider` (`src/components/auth-provider.tsx`) and `set-password` page both attempt to manage session state, but they may not synchronize correctly. The console log "Auth provider: No session found" suggests that the session isn't available when expected, possibly due to timing issues or interference from the provider.

4. **Middleware Interference**:
   - The `middleware.ts` file updates the session for most routes but excludes `/auth/set-password`. While this exclusion prevents interference, it doesn't address potential session mismatches from prior middleware runs.

5. **Development Environment Issues**:
   - Next.js Fast Refresh in development can cause components to re-render unexpectedly, potentially triggering multiple code exchanges or disrupting state management.

---

### Solution

To resolve the "invalid link" error, we need to:
- Ensure the PKCE flow state is correctly managed.
- Prevent multiple code exchange attempts reliably.
- Handle session state independently on the `set-password` page.
- Minimize interference from other components like `AuthProvider`.

Below are the specific changes to implement:

#### 1. Update `src/app/auth/set-password/page.tsx`

We'll modify the `set-password` page to:
- Use `localStorage` instead of `sessionStorage` for persistence across page reloads.
- Fetch the session independently without relying on `AuthProvider`.
- Add safeguards to ensure the code is exchanged only once.

```diff
--- src/app/auth/set-password/page.tsx
+++ src/app/auth/set-password/page.tsx (updated)
@@ -3,7 +3,7 @@
 import { useState, useEffect, Suspense, useRef } from "react";
 import { useRouter, useSearchParams } from "next/navigation";
 import { supabase } from "@/supabase/client";
 import { resetPasswordAction } from "@/app/actions";
-import { useAuth } from "@/components/auth-provider";
 import Navbar from "@/components/navbar";
 import { SubmitButton } from "@/components/submit-button";
 import { Input } from "@/components/ui/input";
@@ -15,7 +15,7 @@
 function SetPasswordComponent() {
   const router = useRouter();
   const searchParams = useSearchParams();
   const { toast } = useToast();
-  const { user, isLoading: isAuthLoading } = useAuth();
   const [password, setPassword] = useState("");
   const [confirmPassword, setConfirmPassword] = useState("");
   const [isSubmitting, setIsSubmitting] = useState(false);
@@ -24,6 +24,7 @@
   const code = searchParams.get("code");
   const exchangeAttempted = useRef(false); // Prevents re-running on fast refresh
   const [session, setSession] = useState<any>(null); // Local session state
 
   useEffect(() => {
     if (code) {
       const processedCodeKey = `processed_pkce_code_${code}`;
-      // Check session storage to prevent re-running on fast refresh
-      if (sessionStorage.getItem(processedCodeKey)) {
+      // Check local storage to prevent re-running across reloads
+      if (localStorage.getItem(processedCodeKey)) {
         console.log("Code already processed. Fetching session.");
+        supabase.auth.getSession().then(({ data }) => {
+          setSession(data.session);
           setPageReady(true);
+        });
         return;
       }
 
       const exchangeCode = async () => {
-        // Mark code as processed immediately in session storage
-        sessionStorage.setItem(processedCodeKey, 'true');
+        // Mark code as processed immediately in local storage
+        localStorage.setItem(processedCodeKey, 'true');
         const { error, data } = await supabase.auth.exchangeCodeForSession(code);
         if (error) {
           console.error("Code exchange error:", error.message);
           setPageError("Invalid or expired password reset link. Please request a new one.");
+          setSession(null);
         } else {
+          setSession(data.session);
         }
         setPageReady(true);
       };
       exchangeCode();
     } else {
       // Initial password setup flow
+      supabase.auth.getSession().then(({ data }) => {
+        setSession(data.session);
         setPageReady(true);
+      });
     }
   }, [code]);
 
@@ -93,7 +94,7 @@
   // Show a loading state until we've processed the code or confirmed the user session
-  if (isAuthLoading || !pageReady) {
+  if (!pageReady) {
     return (
       <>
         <Navbar />
@@ -108,7 +109,7 @@
   }
 
   // If there was an error during code exchange, show it and stop.
-  if (pageError && !user) {
+  if (pageError && !session) {
       return (
           <>
               <Navbar />
@@ -123,7 +124,7 @@
   }
   
   // If this isn't a reset flow and there's no user, it's an invalid access attempt.
-  if (!code && !user) {
+  if (!code && !session) {
     return (
         <>
             <Navbar />
@@ -140,7 +141,7 @@
 
   const isResetFlow = !!code;
-  const title = isResetFlow ? "Reset Your Password" : `Welcome, ${user?.email || 'User'}`;
+  const title = isResetFlow ? "Reset Your Password" : `Welcome, ${session?.user?.email || 'User'}`;
   const description = isResetFlow ? "Enter your new password below." : "Set a password to secure your account.";
   const buttonText = isResetFlow ? "Reset Password" : "Set Password & Continue";
   const pendingButtonText = isResetFlow ? "Resetting..." : "Saving...";
```

**Explanation**:
- **Remove `AuthProvider` Dependency**: We no longer use `useAuth` to avoid synchronization issues. Instead, we manage the session locally with `setSession`.
- **Switch to `localStorage`**: This ensures the processed code flag persists across tab closures and reloads, preventing multiple exchange attempts.
- **Fetch Session Directly**: We use `supabase.auth.getSession()` to ensure the page has the latest session state, improving reliability.

#### 2. Update `middleware.ts`

We'll ensure the middleware doesn't interfere with the `set-password` page by explicitly bypassing session updates for this route.

```diff
--- middleware.ts
+++ middleware.ts (updated)
@@ -1,6 +1,12 @@
 import { updateSession } from "@/supabase/middleware";
 import { type NextRequest } from "next/server";
 
 export async function middleware(request: NextRequest) {
+  const path = request.nextUrl.pathname;
+  // Skip session update for set-password page to avoid interfering with code exchange
+  if (path.startsWith('/auth/set-password')) {
+    return NextResponse.next();
+  }
   return await updateSession(request);
 }
```

**Explanation**:
- **Bypass for `/auth/set-password`**: This prevents the middleware from modifying the session during the code exchange, reducing potential conflicts.

#### 3. Verify Supabase Configuration

Ensure your Supabase configuration aligns with the application's URLs:
- **Supabase Dashboard**: Go to Authentication > URL Configuration.
- **Redirect URLs**: Add `http://localhost:3000/auth/set-password` (for development) and your production URL (e.g., `https://yourdomain.com/auth/set-password`) to the list of allowed redirect URLs.
- **Site URL**: Set to `http://localhost:3000` for development or your production domain.

This ensures the `redirectTo` URL in `forgotPasswordAction` matches an allowed redirect, maintaining the PKCE flow state.

---

### Implementation Steps

1. **Apply the Code Changes**:
   - Replace the content of `src/app/auth/set-password/page.tsx` with the updated version above.
   - Replace the content of `middleware.ts` with the updated version above.

2. **Update Supabase Configuration**:
   - Log in to your Supabase dashboard.
   - Navigate to **Authentication > URL Configuration**.
   - Add the redirect URL as described above.
   - Save the changes.

3. **Clear Local Storage (Development)**:
   - Open your browser's developer tools (F12).
   - Go to the **Application** tab > **Local Storage** > `http://localhost:3000`.
   - Clear all entries to reset any stale `processed_pkce_code_*` flags.

---

### Testing the Solution

1. **Request a Password Reset**:
   - Go to `http://localhost:3000/forgot-password`.
   - Enter an email (e.g., `<EMAIL>`) and submit.

2. **Use the Reset Link**:
   - Check your email for the reset link (e.g., `http://localhost:3000/auth/set-password?code=<code>`).
   - Click the link to open the `set-password` page.

3. **Verify Behavior**:
   - The page should load without the "invalid link" error.
   - Enter and submit a new password.
   - You should be redirected to `/sign-in` with a success message.

4. **Check Logs**:
   - Open the browser console (F12 > Console) and server logs.
   - Ensure no `404` errors or "invalid flow state" messages appear during the code exchange.

---

### If the Issue Persists

If the "invalid link" error continues after applying these changes:
- **Add Debugging Logs**:
  - In `src/app/auth/set-password/page.tsx`, add `console.log("Code:", code)` before the `exchangeCode` call to verify the code is present.
  - In `src/app/actions.ts`, add `console.log("Reset email sent for:", email)` in `forgotPasswordAction` to confirm the email is sent.

- **Check Supabase Logs**:
  - In the Supabase dashboard, go to **Reports > API** and look for errors related to `/token` or `/recover`.

- **Test in Production Mode**:
  - Run `npm run build` and `npm start` to test in a production-like environment, as Fast Refresh might still interfere in development.

- **Contact Supabase Support**:
  - If the flow state issue persists despite correct configuration, it might be a Supabase server-side bug. Provide them with your logs and configuration details.

---

### Conclusion

The "invalid link" error is caused by a failure in the PKCE flow state management, exacerbated by multiple code exchange attempts and session state inconsistencies. By updating the `set-password` page to use `localStorage`, managing the session locally, and ensuring middleware compatibility, you should resolve the issue. After applying these changes and verifying your Supabase configuration, the forgot password flow should work seamlessly.

Let me know if you need further assistance or if the error persists after testing!