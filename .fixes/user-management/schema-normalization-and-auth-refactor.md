# Final Plan: Normalize the Database Schema and Refactor Authentication

## 1. High-Level Objective

The goal is to create a fully normalized database schema that separates core user data from role-specific data. This will create a more scalable, maintainable, and extensible system that is free of the data redundancy and synchronization issues that are currently plaguing the application.

## 2. The New Database Schema

Here is the proposed new database schema:

-   **`profiles`:** This table will store the core data for all users, regardless of their role. It will include fields such as `id`, `email`, `first_name`, `last_name`, and `verification_status`.
-   **`expert_profiles`:** This table will store the data that is unique to experts, such as `bio`, `education`, `average_rating`, and `total_reviews`.
-   **`farmer_profiles`:** This table will store the data that is unique to farmers, such as `farm_size` and `crop_types`.
-   **`admin_users`:** This table will remain as is, storing data that is unique to administrators.

This normalized schema will create a more efficient and organized data model that is easier to manage and extend.

## 3. The Implementation Plan

Here's how we'll implement this new, normalized schema:

1.  **Database Migration:** We will create a new database migration that will normalize the schema. Below is the SQL code to be included in the new migration file:

    ```sql
    -- Step 1: Add role-specific columns to expert_profiles
    ALTER TABLE expert_profiles
    ADD COLUMN expertise_area TEXT,
    ADD COLUMN qualifications TEXT;

    -- Step 2: Add role-specific columns to farmer_profiles
    ALTER TABLE farmer_profiles
    ADD COLUMN farm_name TEXT,
    ADD COLUMN farm_size_hectares NUMERIC,
    ADD COLUMN preferred_crops TEXT;

    -- Step 3: Move data from profiles to expert_profiles
    UPDATE expert_profiles ep
    SET
      expertise_area = p.expertise_area,
      qualifications = p.qualifications,
      years_of_experience = p.years_of_experience
    FROM profiles p
    WHERE ep.id = p.id;

    -- Step 4: Remove role-specific columns from profiles
    ALTER TABLE profiles
    DROP COLUMN expertise_area,
    DROP COLUMN qualifications,
    DROP COLUMN years_of_experience;
    ```
2.  **Refactor the `signInAction`:** We will update the `signInAction` to query only the `profiles` table. It will retrieve the user's core information, including their role and verification status, and use this to make all authentication and authorization decisions.
3.  **Refactor the UI:** We will update all relevant UI components to work with the new, normalized schema. This will involve:
    -   Updating the user profile and user management pages to source data from the appropriate tables.
    -   Ensuring that all queries and mutations are updated to reflect the new data model.

This will create a more robust and reliable application that is free of the data redundancy and synchronization issues that are currently plaguing the system.

## 4. Visualizing the New Architecture

Here's a look at the final proposed architecture:

```mermaid
graph TD
    subgraph "Database"
        A[profiles] -- "one-to-one" --> B[expert_profiles];
        A -- "one-to-one" --> C[farmer_profiles];
        A -- "one-to-one" --> D[admin_users];
    end

    subgraph "Authentication"
        E[signInAction] --> F{Query 'profiles' Table};
        F --> G{Authorize User};
    end

    subgraph "Profile Management"
        H[View Profile] --> I{Query 'profiles' & role-specific table};
        I --> J[Display Full Profile];
    end
```

This plan will create a more scalable, maintainable, and extensible system that is built on a solid foundation of well-structured data.