Authentication Flow Update Plan
This plan outlines the steps to replace the authentication flow in the web app from supporting farmers and experts to supporting experts and admins, with farmers still able to sign up but redirected to a limited view. For now, admins and experts will have full access to the dashboard. The plan includes updates to Supabase database interactions and implements expert profile management and admin authentication features.

1. Signup Page Update

Objective: Modify signup to support farmers and experts only, with role assignment and redirection.
Tasks:
Update the signup form to include a role selection dropdown (farmer or expert).
On form submission:
Create a user in Supabase Auth using supabase.auth.signUp().
Insert user details into the profiles table.
Assign the selected role in the user_roles table (e.g., farmer or expert).


Redirect after signup:
Experts: /dashboard (logged-in view).
Farmers: /download-app (mobile app download page with dummy link for now).


Add error handling for invalid inputs, duplicate emails, etc.


Note: Admin signup is not allowed here; it’s managed via the admin dashboard.


2. Login Page Update

Objective: Support login for farmers, experts, and admins with role-based redirection.
Tasks:
Use a single login form for all users (farmers, experts, admins) with email and password fields.
Implement error handling for incorrect credentials or server issues.
After successful login via supabase.auth.signInWithPassword():
Fetch the user’s role from the user_roles table.
Redirect based on role:
farmer: /download-app.
expert or admin: /dashboard.




Ensure adherence to Supabase authentication and roles schema (e.g., roles, user_roles tables).


the expert and admin dashboard views are similar


3. Expert Profile Management

Objective: Enable experts to manage their profiles in the dashboard.
Tasks:
Profile Creation and Editing:
Create a multi-step form for:
Professional info (specializations, experience, education).
Bio (rich text editor).
Profile photo upload (with preview/cropping).
Contact info.
Credential upload (sent for verification).
Service area input.


Save to expert_profiles and expert_credentials tables.


Specialization Management:
Allow multiple selections from specializations table.
Add experience level and descriptions, save to expert_specializations.


Portfolio Management:
Build a manager for case studies, success stories, images, and testimonials.
Save to expert_portfolio_items and expert_portfolio_images.


Verification Status:
Add a credential submission interface.
Display status indicators from expert_verifications.
Handle additional info requests from admins.


API Endpoints:
GET /api/experts/profile: Fetch profile data.
PUT /api/experts/profile: Update profile.
POST /api/experts/portfolio: Add portfolio item.
PUT /api/experts/portfolio/:id: Update portfolio item.
DELETE /api/experts/portfolio/:id: Delete portfolio item.
POST /api/experts/credentials: Submit credentials.


Restrict access to own data via RLS policies.




4. Admin Authentication and Management

Objective: Implement admin-specific features securely.
Tasks:
Admin User Management:
Create a page in the admin dashboard for user creation.
Add a form for email, password, and role (admin only).
Use an API route (e.g., /api/admin/create-user):
Verify caller has admin role.
Create user via Supabase Admin API (supabase.auth.admin.createUser).
Assign admin role in user_roles.


Save admin-specific data to admin_users.


Credential Verification:
Build a “Verification Queue” page listing pending requests from expert_credentials.
Allow admins to view credentials and approve/reject, updating expert_verifications.


Log admin actions in admin_audit_logs.




Notes

Remove farmer-specific dashboard features from the old flow.
Use Supabase MCP/connection to verify table schemas and adjust queries as needed.
Test redirects and access controls thoroughly.

