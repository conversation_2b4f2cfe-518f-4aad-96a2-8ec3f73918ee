# Plan: Full App Translation and UI Enhancement

This plan outlines a systematic approach to translate all pages for both admin and expert views, while also addressing specific UI adjustments for a polished and fully functional RTL experience.

## Phase 1: Admin View Translation and RTL

I will tackle the admin-specific pages one by one. For each page, the process will be:
1.  **Audit:** Identify all hardcoded strings.
2.  **Translate:** Add keys to `en.json` and `ar.json`.
3.  **Implement:** Use the `t()` function in the component.
4.  **Style:** Apply RTL variants (`rtl:*`, `ps-*`, `ms-*`, etc.) to ensure correct layout.

*   [ ] **Users Page (`/dashboard/users`)**
    *   Translate title, search placeholder, and table headers ("User", "Email", "Role", "Actions").
    *   Ensure table content aligns correctly using `text-start`.
    *   Adjust button styles within the table for RTL.
*   [ ] **Management Page (`/dashboard/management`)**
    *   Translate all text, including titles, descriptions, and buttons.
    *   Apply RTL styles to any forms or data displays.
*   [ ] **Analytics Page (`/dashboard/analytics`)**
    *   Translate titles, chart labels, and stat cards.
    *   **Crucial:** Investigate and configure the charting library (`Recharts`) to correctly render axes, tooltips, and legends in RTL mode. This may require specific props or custom wrappers.
*   [ ] **Settings Page (`/dashboard/settings`)**
    *   Translate all form labels, placeholders, and descriptions in `settings-form.tsx`.
    *   Ensure form layouts are RTL-friendly.

## Phase 2: Expert View Translation and RTL

Similar to the admin view, I will process each expert-specific page.

*   [ ] **Requests Page (`/dashboard/requests`)**
    *   Translate title, search/filter controls, and request card details.
    *   Apply RTL styling to the layout of request cards.
*   [ ] **Consultations Page (`/dashboard/consultations`)**
    *   Translate page title and all text within the consultation cards.
    *   Ensure the layout of the cards is mirrored correctly in RTL.
*   [ ] **Consultation Detail Page (`/dashboard/consultations/[id]`)**
    *   Translate all tabs ("Consultation Details", "Farm Data", "Communication").
    *   Translate all field labels and values.
    *   Ensure the layout of each tab's content is RTL-aware.
*   [ ] **My Profile Page (`/dashboard/my-profile`)**
    *   Translate all user profile field labels and action buttons.
    *   Adjust the layout of the profile page for RTL.

## Phase 3: Common Pages Translation and RTL

These pages are accessible to both roles and need to be handled as well.

*   [ ] **Main Dashboard Page (`/dashboard`)**
    *   Translate any welcome messages or overview components.
    *   Apply RTL styles to the dashboard grid or layout.
*   [ ] **Support Page (`/dashboard/support`)**
    *   Translate titles, FAQs, and contact information.
    *   Ensure the layout of the support page is correctly mirrored.
*   [ ] **Reset Password Page (`/dashboard/reset-password`)**
    *   Translate all form elements, including labels, placeholders, and buttons.
    *   Apply RTL styling to the form layout.

## Phase 4: Final Polish and Verification

*   **Component Review:** Re-check all common UI components in `src/components/ui` (e.g., Dialogs, Dropdowns, Tables) to ensure they work flawlessly in both LTR and RTL contexts when they contain translated content.
*   **End-to-End Testing:**
    *   Perform a full run-through of the app as an "admin" in both English and Arabic.
    *   Perform a full run-through of the app as an "expert" in both English and Arabic.
    *   Verify that all text is translated and there are no layout issues.