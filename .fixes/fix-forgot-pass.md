Based on the terminal and console logs, as well as the screenshots you provided, the issue you're encountering with the "Invalid or expired password reset link" error in your AgriConnect application stems from a problem in the Proof Key for Code Exchange (PKCE) flow used by Supabase for password reset authentication. Specifically, the `code_verifier` cookie required to validate the authorization code is not being set or transmitted correctly, causing the code exchange process to fail. Below, I’ll explain the problem in detail and provide a step-by-step solution to resolve it.

---

### Understanding the Problem

The password reset process involves sending a reset email with a link containing an authorization code (e.g., `032736`, `754862`, `217749`, or `9680880`, as seen in your screenshots). When the user clicks this link (e.g., `http://localhost:3000/auth/set-password?code=032736`), the application attempts to exchange the code for a session token using Supabase’s authentication API. However, this exchange fails, resulting in the "Link Invalid" error displayed on the webpage. Here’s why:

#### Evidence from Logs and Screenshots
1. **Terminal Logs:**
   - The `forgotPasswordAction` successfully initiates the password reset process, sending an email with the `redirectTo` URL: `http://localhost:3000/auth/set-password?type=recovery`.
   - Middleware logs show `updateSession response cookies: []` for `/auth/set-password`, indicating no cookies are being set in the response, including the critical PKCE `code_verifier` cookie (`sb-jrhbvcjwxvyrxrmgjfbu-auth-token-code-verifier`).
   - Repeated `Cookie get for /auth/set-password` logs confirm that the PKCE cookie is `undefined` when accessed.

2. **Console Logs:**
   - The client-side `useEffect` in `page.tsx` initializes with the code (e.g., `032736`) but finds no cookies: `Available cookies before exchange: {"": undefined}`.
   - The `exchangeCodeForSession` call fails with the error: `invalid request: both auth code and code verifier should be non-empty`, indicating the `code_verifier` is missing.
   - HTTP requests to `https://jrhbvcjwxvyrxrmgjfbu.supabase.co/auth/v1/token?grant_type=pkce` return errors:
     - `404 (Not Found)`: Suggests the endpoint might be misconfigured or unavailable.
     - `400 (Bad Request)`: Confirms the request is invalid due to the missing `code_verifier`.

3. **Screenshots:**
   - All four screenshots show the "Link Invalid" error on the `/auth/set-password` page with different codes (`754862`, `217749`, `9680880`, `032736`).
   - Console output in the screenshots consistently shows the absence of the PKCE cookie and the same code exchange error.

#### Root Cause
The PKCE flow requires two components to validate the authorization code:
- **Authorization Code**: Provided in the URL query parameter (e.g., `code=032736`).
- **Code Verifier**: A secret value generated by the client, stored in a cookie, and sent with the code during the exchange process.

Supabase’s `resetPasswordForEmail` function should set the `code_verifier` cookie when the reset email is generated, but this isn’t happening. Possible reasons include:
- Misconfigured Supabase redirect URI settings.
- The `Secure` flag on the cookie preventing it from being set over `http://localhost` (non-HTTPS).
- A bug or misconfiguration in the Supabase client or server setup.

---

### Solution

To resolve this issue, we need to ensure the `code_verifier` is properly set and transmitted. Below are the steps to fix the problem, starting with the most likely causes:

#### Step 1: Verify Supabase Configuration
The redirect URL must match exactly in Supabase’s settings, or the PKCE flow will fail.

1. **Check Supabase Project Settings:**
   - Log in to your Supabase dashboard.
   - Navigate to **Authentication > URL Configuration**.
   - Ensure the redirect URI `http://localhost:3000/auth/set-password` is listed under **Redirect URLs**. If not, add it and save the changes.
   - Verify that PKCE is enabled (this is the default for Supabase, but confirm the `code_challenge_method` is set to `s256`).

2. **Action:**
   - If the redirect URI was missing or incorrect, update it and test the password reset flow again.

#### Step 2: Adjust the `redirectTo` URL
The `redirectTo` URL in your `forgotPasswordAction` must be precise to avoid mismatches.

1. **Update the Code:**
   Modify `src/app/actions.ts` to explicitly include the port and query parameters:

   ```typescript
   // src/app/actions.ts
   export const forgotPasswordAction = async (formData: FormData): Promise<{
     success: boolean;
     error?: string;
     message?: string;
   }> => {
     const email = formData.get("email")?.toString();
     const supabase = await createClient();
     const origin = headers().get("origin");

     if (!email) {
       return { success: false, error: "Email is required" };
     }

     // Explicitly set redirectTo with port and type
     const redirectTo = "http://localhost:3000/auth/set-password?type=recovery";

     const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
       redirectTo,
       // @ts-ignore
       captchaToken: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
     });

     console.log("Password reset initiated:", {
       email,
       redirectTo,
       success: !error,
       error: error?.message,
       timestamp: new Date().toISOString(),
     });

     if (error) {
       console.error("Detailed Forgot Password Error:", JSON.stringify(error, null, 2));
       return {
         success: false,
         error: `Could not send password reset link: ${error.message}. Please check the server logs for details.`,
       };
     }

     return {
       success: true,
       message: "If an account exists, a password reset link has been sent to your email.",
     };
   };
   ```

2. **Test:**
   - Trigger a password reset and check the email link. It should look like `http://localhost:3000/auth/set-password?code=<code>&type=recovery`.

#### Step 3: Handle the `Secure` Flag for Localhost
Cookies with the `Secure` flag won’t be set over `http://localhost` because it’s not HTTPS. This might prevent the `code_verifier` cookie from being stored.

1. **Run Locally with HTTPS:**
   - Use a tool like `mkcert` to generate a local SSL certificate:
     ```bash
     mkcert -install
     mkcert localhost
     ```
   - Update your Next.js app to use HTTPS. Add a `next.config.js` file if you don’t have one:
     ```javascript
     module.exports = {
       server: {
         https: {
           key: "./localhost-key.pem",
           cert: "./localhost.pem",
         },
       },
     };
     ```
   - Start your app with `next dev --experimental-https`.

2. **Update `redirectTo`:**
   - Change the `redirectTo` in `forgotPasswordAction` to `https://localhost:3000/auth/set-password?type=recovery`.
   - Update Supabase redirect URI to match: `https://localhost:3000/auth/set-password`.

3. **Test:**
   - Trigger a password reset and verify that the `code_verifier` cookie appears in the browser’s developer tools (Application > Cookies).

#### Step 4: Fallback - Manual PKCE Flow (Optional)
If the above steps don’t work, implement the PKCE flow manually on the client side.

1. **Generate and Store `code_verifier`:**
   Update `page.tsx` to handle the PKCE flow:

   ```typescript
   // src/app/auth/set-password/page.tsx
   "use client";

   import { useEffect, useState } from "react";
   import { createClient } from "@/supabase/client";

   export default function SetPasswordPage({ searchParams }) {
     const [error, setError] = useState(null);
     const supabase = createClient();

     useEffect(() => {
       const exchangeCode = async () => {
         const code = searchParams?.code;
         if (!code) {
           setError("No code provided");
           return;
         }

         // Generate code_verifier and code_challenge manually
         const generateRandomString = (length) =>
           crypto
             .getRandomValues(new Uint8Array(length))
             .reduce((str, byte) => str + byte.toString(36), "")
             .slice(0, length);
         const codeVerifier = generateRandomString(128);
         const encoder = new TextEncoder();
         const codeChallenge = btoa(
           String.fromCharCode(
             ...new Uint8Array(
               await crypto.subtle.digest("SHA-256", encoder.encode(codeVerifier))
             )
           )
         )
           .replace(/\+/g, "-")
           .replace(/\//g, "_")
           .replace(/=+$/, "");

         // Store code_verifier in a cookie
         document.cookie = `sb-jrhbvcjwxvyrxrmgjfbu-auth-token-code-verifier=${codeVerifier}; path=/`;

         try {
           const { data, error } = await supabase.auth.exchangeCodeForSession({
             authCode: code,
             codeVerifier,
           });
           if (error) throw error;
           console.log("Session established:", data);
         } catch (err) {
           setError("Invalid or expired password reset link");
           console.error("Code exchange error:", err.message);
         }
       };

       exchangeCode();
     }, [searchParams]);

     return (
       <div>
         {error ? (
           <>
             <h1 className="text-red-500">Link Invalid</h1>
             <p>Invalid or expired password reset link. Please request a new one.</p>
           </>
         ) : (
           <p>Processing...</p>
         )}
       </div>
     );
   }
   ```

2. **Test:**
   - Trigger a password reset and check if the manual flow succeeds.

---

### Additional Notes
- **Timestamp Issue:** Your logs show a future date (`2025-06-27`). Ensure your system clock is correct, as token validation depends on accurate time. Update it with:
  ```bash
  sudo ntpdate pool.ntp.org
  ```
- **Debugging:** Add more logging in `page.tsx` to trace cookie values:
  ```typescript
  console.log("Cookies after setting:", document.cookie);
  ```

---

### Final Steps
1. Start with **Step 1** and **Step 2**—these are the most likely fixes.
2. If the issue persists, try **Step 3** (HTTPS).
3. Use **Step 4** as a last resort if Supabase’s automatic PKCE flow remains broken.

After implementing these changes, test the password reset flow by requesting a new link and clicking it. The "Link Invalid" error should disappear, and the code exchange should succeed, allowing the user to reset their password. If problems persist, check the Supabase dashboard logs for additional errors or share more detailed logs for further assistance.