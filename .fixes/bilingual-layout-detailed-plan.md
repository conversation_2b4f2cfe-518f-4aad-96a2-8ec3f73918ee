# Detailed Plan: Bilingual Layout for Consultation Page

## 1. Context & Objective

The primary goal is to refactor the "Consultation" page to support both English (Left-to-Right, LTR) and Arabic (Right-to-Left, RTL) layouts seamlessly. The layout should adapt automatically based on the `dir` attribute on the `<html>` or `<body>` tag. This requires restructuring the consultation cards and applying CSS that respects text direction.

## 2. File Analysis & Affected Components

*   **`src/app/dashboard/consultations/page.tsx`**: The main file. Currently uses a mix of flexbox and Tailwind CSS `rtl:` utility classes.
*   **`src/app/globals.css`**: The ideal place for new, global CSS rules to centralize the directional logic.
*   **`src/components/ui/card.tsx`**: A generic UI component that should not contain direction-specific logic.

## 3. Proposed Solution: The "Logical Properties" Approach (Recommended)

This approach is the modern best practice for internationalization.

*   **Step 1: Refactor the HTML in `consultations/page.tsx`**
    *   Wrap the card's content in a single flex container.
    *   Create two distinct `<div>` elements for the "Content" and "Meta" sections with semantic class names (`consultation-card__content`, `consultation-card__meta`).
    *   **Benefit**: This creates a clear, semantic structure, isolating layout logic to the parent container.

*   **Step 2: Add Global CSS to `globals.css`**
    *   Add centralized CSS rules using logical properties:

    ```css
    .consultation-card-layout {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    [dir="rtl"] .consultation-card-layout {
      flex-direction: row-reverse;
    }
    
    .consultation-card__content,
    .consultation-card__meta {
      text-align: start;
    }
    
    .consultation-card__content > div {
       text-align: start;
    }
    ```
    *   **Benefit**: This is the most robust and maintainable solution. The component's JSX stays clean, and the layout logic is in one place, automatically adapting to the `dir` attribute.

## 4. Alternative Solutions (Less Ideal)

*   **Alternative #1: "Pure Tailwind CSS" Approach**
    *   **How**: Continue adding `rtl:` prefixes (`rtl:flex-row-reverse`, `rtl:text-right`, etc.) to every element.
    *   **Cons**: Extremely verbose, hard to read and maintain, and error-prone.

*   **Alternative #2: "JavaScript-driven" Approach**
    *   **How**: Use the `locale` from `useTranslation()` to conditionally apply classes in JSX.
    *   **Cons**: Tightly couples logic and styling (an anti-pattern), is less performant, and bypasses native browser optimizations for `dir`.

## 5. Recommendation

The **"Logical Properties" Approach** is strongly recommended as it is the most modern, maintainable, and scalable solution.