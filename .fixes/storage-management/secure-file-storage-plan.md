# Plan: Secure File Storage Implementation

## 1. High-Level Goal

The goal of this plan is to establish a secure and well-organized file storage system using Supabase Storage. This involves creating distinct storage buckets for different types of files, each protected by robust Row Level Security (RLS) policies. These policies will ensure that users can only access or upload files according to their roles and permissions, as defined in the `profiles` and `user_roles` tables.

## 2. Bucket Creation and Configuration

Based on the database schema and application needs, we will create and configure three distinct storage buckets.

### 2.1. Bucket: `profile-pictures`
This bucket will store user profile images.

*   **Configuration:**
    *   **Name:** `profile-pictures`
    *   **Public Bucket:** `true`. This allows easy access for displaying avatars across the app. RLS policies will still protect write operations.
    *   **File Size Limit:** `5MB`. Prevents excessively large image uploads.
    *   **Allowed MIME types:** `image/jpeg, image/png`. Restricts uploads to common image formats.

### 2.2. Bucket: `expert-credentials`
This bucket is for sensitive documents related to expert verification (e.g., certificates, licenses).

*   **Configuration:**
    *   **Name:** `expert-credentials`
    *   **Public Bucket:** `false`. These documents must remain private.
    *   **File Size Limit:** `10MB`. Allows for high-quality PDF scans or images.
    *   **Allowed MIME types:** `application/pdf, image/jpeg, image/png`.

### 2.3. Bucket: `consultation-attachments`
This bucket will hold files exchanged during consultations between farmers and experts.

*   **Configuration:**
    *   **Name:** `consultation-attachments`
    *   **Public Bucket:** `false`. Attachments are private to consultation participants.
    *   **File Size Limit:** `25MB`. Accommodates larger files like videos or detailed documents.
    *   **Allowed MIME types:** (Leave blank). Allows for flexibility as users may share various file types (images, videos, documents). Security is enforced by RLS policies.

---

## 3. Bucket Policies (RLS)

The following RLS policies will be applied to the `storage.objects` table.

### 3.1. `profile-pictures` Policies

```sql
-- Allow users to upload their own profile picture
CREATE POLICY "Allow individual profile picture uploads"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'profile-pictures' AND
  owner = auth.uid()
);

-- Allow users to update their own profile picture
CREATE POLICY "Allow individual profile picture updates"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  owner = auth.uid()
) WITH CHECK (
  bucket_id = 'profile-pictures'
);

-- Public buckets are viewable by default, but RLS is needed for other operations.
-- This policy allows authenticated users to see files if needed for specific queries.
CREATE POLICY "Allow authenticated users to view profile pictures"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'profile-pictures'
);

-- Allow users to delete their own profile picture
CREATE POLICY "Allow individual profile picture deletes"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'profile-pictures' AND
  owner = auth.uid()
);
```

### 3.2. `expert-credentials` Policies

```sql
-- Allow experts to upload their own credentials
CREATE POLICY "Allow experts to upload their credentials"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'expert-credentials' AND
  owner = auth.uid() AND
  EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid() AND r.name = 'expert'
  )
);

-- Allow experts to view their own credentials
CREATE POLICY "Allow experts to view their own credentials"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'expert-credentials' AND
  owner = auth.uid()
);

-- Allow admins to view all expert credentials
CREATE POLICY "Allow admins to view all expert credentials"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'expert-credentials' AND
  EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid() AND r.is_admin_role = true
  )
);
```

### 3.3. `consultation-attachments` Policies

This policy assumes a folder structure like `consultation-attachments/{consultation_id}/{file_name}`.

```sql
-- Allow participants of a consultation to upload attachments
CREATE POLICY "Allow consultation participants to upload attachments"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'consultation-attachments' AND
  owner = auth.uid() AND
  EXISTS (
    SELECT 1 FROM consultations c
    WHERE c.id = (storage.foldername(name))[1]::uuid
      AND (c.farmer_id = auth.uid() OR c.expert_id = auth.uid())
  )
);

-- Allow participants of a consultation to view attachments
CREATE POLICY "Allow consultation participants to view attachments"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'consultation-attachments' AND
  EXISTS (
    SELECT 1 FROM consultations c
    WHERE c.id = (storage.foldername(name))[1]::uuid
      AND (c.farmer_id = auth.uid() OR c.expert_id = auth.uid())
  )
);
```

## 4. Implementation Steps

1.  **Create Buckets**: Use the Supabase dashboard to create the three buckets (`profile-pictures`, `expert-credentials`, `consultation-attachments`) with the configurations specified in Section 2.
2.  **Apply RLS Policies**: Execute the SQL statements from Section 3 in the Supabase SQL Editor to apply the RLS policies.
3.  **Frontend Integration**:
    *   Update the user profile page to handle uploads to the `profile-pictures` bucket.
    *   Update the expert profile/credentials section to handle uploads to the `expert-credentials` bucket.
    *   Update the consultation chat/details page to handle uploads to the `consultation-attachments` bucket, ensuring the correct `consultation_id` is used in the file path.

## 5. Mermaid Diagram

```mermaid
graph TD
    subgraph "User Roles"
        A[Admin]
        B[Expert]
        C[Farmer]
    end

    subgraph "Storage Buckets & Policies"
        Bucket1[profile-pictures]
        Bucket2[expert-credentials]
        Bucket3[consultation-attachments]
    end

    subgraph "Relationships"
        A -- "CRUD (All)" --> Bucket1
        A -- "Read (All)" --> Bucket2
        B -- "CRUD (Own)" --> Bucket1
        B -- "CRUD (Own)" --> Bucket2
        C -- "CRUD (Own)" --> Bucket1
        
        B -- "CRUD (Involved)" --> Bucket3
        C -- "CRUD (Involved)" --> Bucket3
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bfb,stroke:#333,stroke-width:2px