# Admin View Updates Plan

This document outlines the plan to implement the requested changes to the admin view of the web app.

## Phase 1: Dashboard and Users Page

### Step 1: Update Dashboard Page

*   **File:** `src/app/dashboard/page.tsx`
*   **Change:** Read the `AdminView` component at `src/components/dashboard/views/admin-view.tsx` and rename the "Active sessions" card to "Consultations".

### Step 2: Update Users Page

*   **File:** `src/app/dashboard/users/page.tsx`
*   **Changes:**
    *   Remove the "Contact" column from the table header.
    *   Remove the "Contact" button from the "Actions" column.
    *   Fix the pagination for both the experts and farmers tables.

## Phase 2: Management and Analytics Pages

### Step 3: Update Management Page

*   **File:** `src/app/dashboard/management/page.tsx`
*   **Changes:**
    *   Update the styling of the administrators' table to match the users' table. This will involve updating the class names for the table, buttons, and actions.

### Step 4: Update Analytics Page

*   **File:** `src/app/dashboard/analytics/page.tsx`
*   **Changes:**
    *   Rename the "Active sessions" card to "Consultations".
    *   Remove the "Filter", "Export", and "Refresh" buttons.
    *   Remove the "Popular Topics", "Top Performing Experts", and "Revenue Trends" sections.

## Phase 3: Support Page

### Step 5: Update Support Page

*   **File:** `src/app/dashboard/support/page.tsx`
*   **Changes:**
    *   Change the "Avg Response Time" card to display the total number of tickets.
    *   Rename the "Recent Tickets" card to "Tickets".

## Plan Summary

```mermaid
graph TD
    subgraph "Phase 1: Dashboard and Users"
        A[Dashboard Page] --> B(Rename Card);
        C[Users Page] --> D{Remove Column & Button};
        C --> E(Fix Pagination);
    end

    subgraph "Phase 2: Management and Analytics"
        F[Management Page] --> G(Update Table Style);
        H[Analytics Page] --> I{Remove Sections & Buttons};
        H --> J(Rename Card);
    end

    subgraph "Phase 3: Support Page"
        K[Support Page] --> L(Update Card Data);
        K --> M(Rename Card);
    end