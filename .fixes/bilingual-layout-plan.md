# Plan for Bilingual Layout Implementation

This document outlines the plan to implement a bilingual (English/LTR and Arabic/RTL) layout for the "Consultation" page.

### 1. Update Root Layout (`src/app/layout.tsx`) to Set `dir` Attribute

The root layout will be modified to dynamically set the `dir` attribute on the `<html>` element based on the current locale. This is the cornerstone of our strategy, as it will allow us to toggle between LTR and RTL layouts seamlessly.

### 2. Create a Dedicated CSS File for Component Styles

To keep styling modular and maintainable, a new CSS file will be created at `src/app/dashboard/consultations/consultations.css`. This file will contain all the specific styles for the consultation cards, making it easy to manage and update in the future.

### 3. Refactor the `ConsultationsPage` Component

The `ConsultationsPage` component (`src/app/dashboard/consultations/page.tsx`) will be refactored to align with the new design. This involves structuring the card content into two main sections: "Content Section" and "Meta/Actions Section." The component will also be updated to correctly import and apply the new styles.

### 4. Implement CSS Using Logical Properties

In the newly created `consultations.css` file, the styles will be written using **CSS logical properties** (`text-align: start`, `margin-inline-start`, `padding-inline`, etc.). This modern approach is ideal for bilingual layouts because it automatically adapts to the writing direction (`LTR` or `RTL`) set by the `dir` attribute.

### 5. Create Implementation Documentation

Once the implementation is complete, a clear, step-by-step guide will be provided in a markdown file. This documentation will explain how to toggle between layouts and why logical properties are the best practice for this use case.

### Visualization

```mermaid
graph TD
    A[Start] --> B{1. Update Root Layout with `dir` attribute};
    B --> C{2. Create `consultations.css`};
    C --> D{3. Refactor `ConsultationsPage` Component};
    D --> E{4. Implement CSS with Logical Properties};
    E --> F{5. Create Documentation};
    F --> G[End];