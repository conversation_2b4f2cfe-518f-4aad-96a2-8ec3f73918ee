# Code Verifier Missing Issue Analysis

## Current Error

```
Code exchange error: invalid request: both auth code and code verifier should be non-empty
```

## Debug Output

```javascript
// Current params being sent
Attempting code exchange with params: 
Object { code: "928873" }
```

## Issue Breakdown

1. **What's Missing**
   - Code verifier is completely absent
   - Only receiving auth code
   - No PKCE state preservation

2. **Where It's Breaking**
   ```typescript
   // src/app/auth/set-password/page.tsx
   const { data, error: exchangeError } = 
     await supabase.auth.exchangeCodeForSession(window.location.href);
   ```
   The code exchange fails because the PKCE flow requires both:
   - Auth code (which we have)
   - Code verifier (which we're missing)

3. **Why It's Breaking**
   - Supabase's PKCE flow requires a code verifier to be generated at the start
   - This verifier must be stored and retrieved during code exchange
   - Our current implementation is not handling this PKCE requirement

## Key Files Involved

1. **Password Reset Initiation**
   ```typescript
   // src/app/actions.ts
   export const forgotPasswordAction = async (formData: FormData) => {
     // This needs to generate and store a code verifier
     const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
       redirectTo: `${origin}/auth/set-password?type=recovery`
     });
   };
   ```

2. **Code Exchange**
   ```typescript
   // src/app/auth/set-password/page.tsx
   // This needs to retrieve and use the stored code verifier
   const exchangeCode = async () => {
     const { data, error: exchangeError } = 
       await supabase.auth.exchangeCodeForSession(window.location.href);
   };
   ```

3. **Middleware**
   ```typescript
   // src/middleware.ts
   // This needs to preserve PKCE cookies
   export async function middleware(req: NextRequest) {
     const res = await updateSession(req);
   }
   ```

## Required Flow

1. **Password Reset Request**
   ```typescript
   1. Generate code verifier (random string)
   2. Generate code challenge (hash of verifier)
   3. Store code verifier in secure cookie
   4. Send code challenge with reset request
   ```

2. **Email Link Click**
   ```typescript
   1. Receive auth code in URL
   2. Retrieve stored code verifier
   3. Use both for code exchange
   4. Establish session
   ```

3. **Password Update**
   ```typescript
   1. Verify session exists
   2. Update password
   3. Clear PKCE state
   4. Redirect to sign-in
   ```

## Immediate Fix Required

1. **Generate Code Verifier**
   ```typescript
   // In forgotPasswordAction
   const codeVerifier = generateCodeVerifier(); // Need to implement
   const codeChallenge = generateCodeChallenge(codeVerifier); // Need to implement
   ```

2. **Store Verifier Securely**
   ```typescript
   // In forgotPasswordAction
   cookies().set('pkce_verifier', codeVerifier, {
     httpOnly: true,
     secure: true,
     sameSite: 'lax'
   });
   ```

3. **Retrieve for Exchange**
   ```typescript
   // In set-password page
   const codeVerifier = cookies().get('pkce_verifier')?.value;
   const { data } = await supabase.auth.exchangeCodeForSession(
     window.location.href,
     { verifier: codeVerifier }
   );
   ```

## Important Notes

1. **Do Not Change**
   - Admin sign-in flow (working correctly)
   - Sign-out functionality (working correctly)
   - Current session management for other flows

2. **Security Considerations**
   - Code verifier must be cryptographically secure
   - Must be stored securely (httpOnly cookie)
   - Must be single-use only
   - Must be cleared after use

3. **Browser Support**
   - Must work across all major browsers
   - Must handle cookie limitations
   - Must work with privacy settings

## Next Steps

1. **Implement Code Verifier**
   - Add verifier generation
   - Add secure storage
   - Add retrieval logic

2. **Test Flow**
   - Test complete flow
   - Test error cases
   - Test browser compatibility

3. **Monitor**
   - Add logging
   - Track success rates
   - Monitor for errors
