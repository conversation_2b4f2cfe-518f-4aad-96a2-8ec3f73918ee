# Comprehensive Debugging Plan: "Link Invalid" Error in Password Reset Flow

## 1. The Persistent Issue
Despite multiple attempts, the password reset flow consistently fails with an "Invalid or expired password reset link" error. The core of the problem is that the PKCE `code_verifier`, which is essential for securely exchanging the authentication `code` for a user session, is not available when the `set-password` page loads.

The technical error, whether it appears as "invalid flow state" or "code verifier missing," points to the same root cause: the state stored in the `sb-jrhbvcjwxvyrxrmgjfbu-auth-token-code-verifier` cookie is being lost or is inaccessible between the start of the password reset and the final code exchange.

## 2. Context and Previously Attempted Fixes
Our debugging process has confirmed the following:
*   The `forgotPasswordAction` in [`src/app/actions.ts`](src/app/actions.ts:1) successfully calls Supabase.
*   Supabase successfully sends the password reset email.
*   The terminal logs show that Supabase *does* set the PKCE verifier cookie (`sb-jrhbvcjwxvyrxrmgjfbu-auth-token-code-verifier`) in the response headers.
*   The browser console logs on the [`/auth/set-password`](/auth/set-password) page show that this cookie is `undefined` when the page's JavaScript runs.

The following fixes have been attempted and have not resolved the issue:
1.  **Middleware Logic Refactor:** The middleware in [`src/middleware.ts`](src/middleware.ts:1) was refactored to use the `updateSession` helper from `@/supabase/middleware` to correctly handle session cookies.
2.  **Middleware Matcher Correction:** The `config.matcher` in [`src/middleware.ts`](src/middleware.ts:1) was corrected to a comprehensive regex to ensure it runs on all required paths, including `/auth/set-password`.

Since these logical fixes failed, the problem is likely not in the application logic itself, but in the subtle configuration or environment interaction.

## 3. Potential Sources of the Issue (New Hypotheses)
We must now consider more fundamental causes. Here are the most likely hypotheses:

*   **Hypothesis A (Most Likely): Cookie Domain/Path Mismatch.** The cookie might be set for a domain or path that the browser considers invalid for the `/auth/set-password` page. This can happen due to:
    *   A mismatch between `http://localhost` and `localhost`.
    *   Slight differences in the `NEXT_PUBLIC_SUPABASE_URL` environment variable between the server and client environments.
    *   The `Path` attribute of the cookie being too restrictive.

*   **Hypothesis B: HTTP vs. HTTPS `Secure` Flag.** If the Supabase project is configured to enforce HTTPS, it might be setting the cookie with the `Secure` flag. Browsers will not send a `Secure` cookie over a non-secure `http://localhost` connection.

*   **Hypothesis C: Incorrect `updateSession` Integration.** While the `updateSession` function is being called, the way the response (`res`) is being handled and returned alongside other custom logic in [`src/middleware.ts`](src/middleware.ts:1) might still be flawed, causing the cookie to be dropped.

*   **Hypothesis D: A Bug or Edge Case in `@supabase/ssr`.** While less likely, it's possible we've encountered a specific edge case in the version of the Supabase SSR helper library being used that affects the PKCE flow.

## 4. A New, Systematic Debugging Strategy
To definitively diagnose this, we will move from broad fixes to precise isolation and verification.

*   **Step 1: Simplify and Isolate the Middleware.** We will temporarily replace the entire content of [`src/middleware.ts`](src/middleware.ts:1) with the most minimal implementation possible, removing all custom logic (role checks, redirects, etc.). This will isolate the Supabase session handling and prove whether our other logic is interfering.

    ```typescript
    // TEMPORARY: src/middleware.ts
    import { updateSession } from '@/supabase/middleware'
    import type { NextRequest } from 'next/server'

    export function middleware(req: NextRequest) {
      return updateSession(req)
    }

    export const config = {
      matcher: [
        '/((?!_next/static|_next/image|favicon.ico|public|images).*)',
      ],
    }
    ```

*   **Step 2: Inspect the Cookie Details.** When the password reset is triggered, we will use the browser's developer tools to perform a detailed inspection of the `sb-jrhbvcjwxvyrxrmgjfbu-auth-token-code-verifier` cookie. We must record its:
    *   `Domain`
    *   `Path`
    *   `Secure` flag
    *   `HttpOnly` flag
    *   `Expires` / `Max-Age`

*   **Step 3: Verify Environment Variables.** We will add `console.log` statements to both the server (`actions.ts`) and the client (`set-password/page.tsx`) to print the exact value of `process.env.NEXT_PUBLIC_SUPABASE_URL` and `process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY`. This will rule out any subtle environmental inconsistencies.

This rigorous, step-by-step approach will allow us to isolate the variable causing the cookie to be lost and finally resolve this persistent issue.