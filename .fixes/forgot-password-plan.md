### Plan to Implement Forgot Password Feature

This plan covers the necessary Supabase configurations, frontend and backend enhancements, and a testing strategy to ensure the feature is robust and reliable.

#### 1. Supabase Configuration

*   **Email Templates:** To provide a consistent user experience, we'll customize the default Supabase password reset email template. This will involve updating the email subject and body to match your application's branding and tone.
*   **Redirect URLs:** For security, we must add the password reset URL to your Supabase project's allow list. This prevents malicious actors from redirecting users to phishing sites. I will guide you on where to add `[your-site-url]/auth/set-password`.

#### 2. Frontend Enhancements (`src/app/auth/set-password/page.tsx`)

*   **Refined Error Handling:** I'll improve the error messages to be more specific and helpful. For instance, we can distinguish between an expired link and an invalid one, guiding the user on the next steps.
*   **Improved User Experience:**
    *   After successfully resetting the password, the user will be automatically redirected to the sign-in page with a confirmation message.
    *   I'll ensure that loading states are handled gracefully, providing visual feedback to the user while background processes are running.

#### 3. Backend Enhancements (`src/app/actions.ts`)

*   **Security:**
    *   **Rate Limiting:** To prevent abuse, I'll add rate-limiting to the `forgotPasswordAction`. This will restrict the number of password reset requests from a single IP address in a given time frame.
    *   **Input Sanitization:** I'll ensure that all user inputs are properly sanitized to protect against cross-site scripting (XSS) and other injection attacks.
*   **Enhanced Logging:** I'll implement more detailed logging for both `forgotPasswordAction` and `resetPasswordAction`. This will capture essential information for easier debugging and troubleshooting.

#### 4. Testing Strategy

I'll outline a thorough testing plan to verify the end-to-end flow, including:
*   Submitting a password reset request with a valid and invalid email.
*   Using a valid, expired, and invalid reset link.
*   Setting a new password that meets (and doesn't meet) the validation criteria.
*   Verifying that the user can log in with the new password.

### Forgot Password Flow

Here is a diagram illustrating the complete password reset process:

```mermaid
sequenceDiagram
    participant User
    participant ForgotPasswordPage as "Forgot Password Page"
    participant ServerActions as "Server Actions"
    participant Supabase

    User->>ForgotPasswordPage: Enters email and clicks "Send Reset Link"
    ForgotPasswordPage->>ServerActions: Calls forgotPasswordAction(email)
    ServerActions->>Supabase: Calls resetPasswordForEmail(email)
    Supabase-->>ServerActions: Returns success or error
    Supabase->>User: Sends password reset email
    ServerActions-->>ForgotPasswordPage: Returns success or error message
    ForgotPasswordPage-->>User: Displays message

    User->>User: Clicks link in email
    User->>SetPasswordPage: Navigates to set-password page with token
    SetPasswordPage->>Supabase: Exchanges code for session
    Supabase-->>SetPasswordPage: Returns session or error
    SetPasswordPage-->>User: Shows password form or error

    User->>SetPasswordPage: Enters new password and submits
    SetPasswordPage->>ServerActions: Calls resetPasswordAction(password)
    ServerActions->>Supabase: Calls updateUser({ password })
    Supabase-->>ServerActions: Returns success or error
    ServerActions-->>SetPasswordPage: Returns success or error
    SetPasswordPage-->>User: Shows success message and redirects to sign-in