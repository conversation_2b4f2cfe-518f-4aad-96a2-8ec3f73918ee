{"tech_stack": ["TypeScript", "Next.js", "Supabase", "Tailwind CSS"], "features": [{"name": "Reset Password", "description": "Handles the password reset process by sending a reset link to the user's email.", "files": ["src/app/api/reset-password/route.ts"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Reset Password API", "version": "1.0.0"}, "paths": {"/api/reset-password": {"post": {"summary": "Send a password reset email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}}, "required": ["email"]}}}}, "responses": {"200": {"description": "Password reset link sent"}, "400": {"description": "Bad request"}}}}}}}, {"name": "Sentry Example API", "description": "A faulty API route to test Sentry's error monitoring.", "files": ["src/app/api/sentry-example-api/route.ts"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Sentry Example API", "version": "1.0.0"}, "paths": {"/api/sentry-example-api": {"get": {"summary": "Trigger a Sentry error", "responses": {"500": {"description": "Internal Server Error"}}}}}}}, {"name": "Upload Avatar", "description": "Handles avatar uploads for users. It uploads the file to Supabase storage and updates the user's profile with the new avatar URL.", "files": ["src/app/api/upload-avatar/route.ts"], "api_doc": {"openapi": "3.0.0", "info": {"title": "Upload Avatar API", "version": "1.0.0"}, "paths": {"/api/upload-avatar": {"post": {"summary": "Upload a user avatar", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"avatar": {"type": "string", "format": "binary"}}, "required": ["avatar"]}}}}, "responses": {"302": {"description": "Redirect to dashboard settings"}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}}}}}}}]}