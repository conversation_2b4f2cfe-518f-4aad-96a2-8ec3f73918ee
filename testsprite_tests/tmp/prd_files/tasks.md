# Implementation Plan

- [-] 1. Enhance existing authentication and role-based access control
  - [x] 1.1 Update authentication system to use latest database schema
    - Modify existing DashboardLayout to properly handle role-based access from database profiles
    - Update getUserRole utility to use the latest database schema with proper role detection
    - Enhance existing role-based sidebar navigation to restrict admin features from experts
    - Add middleware protection for admin-only routes
    - _Requirements: 1.1, 1.2, 1.3, 10.1, 10.2, 10.3_

  - [ ] 1.2 Manual testing for authentication and role-based access
    - Test admin login: verify admin dashboard access → check admin-only navigation items → test admin features
    - Test expert login: verify expert dashboard access → check expert-only navigation → verify admin features blocked
    - Test role-based route protection: expert tries admin route → verify access denied → check redirect
    - Test session management: login → wait for timeout → verify auto-logout → test token refresh
    - Test password reset flow: request reset → check email → reset password → verify login works
    - Test edge cases: invalid credentials, expired sessions, concurrent logins, role changes
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 10.1, 10.2, 10.3_

- [x] 2. Build asset registration approval system (new feature)
  - [x] 2.1 Create database functions for asset registration management
    - Create SQL file with functions for fetching registration requests with pagination and filtering
    - Add function for updating registration status with proper audit logging
    - Create function for extracting asset data from registration JSON for display
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 2.2, 2.3, 2.4_

  - [x] 2.2 Create new asset registration management page for admin
    - Build new page at `/dashboard/management` to display pending asset registration requests
    - Create table component to show registration requests from `registration_requests` table
    - Add filtering by entity_type (FARM/FACTORY) and status
    - Implement search functionality for registration requests
    - _Requirements: 2.2_

  - [x] 2.3 Create asset registration details modal/page
    - Build detailed view component for individual registration requests
    - Parse and display structured registration data from JSON `registration_data` field
    - Show farm/factory information with proper Arabic/English formatting
    - Add image gallery for uploaded asset photos from registration data
    - _Requirements: 2.2, 2.6_

  - [x] 2.4 Implement registration approval/rejection functionality
    - Create approval action buttons using existing `approve_registration_request` function
    - Build rejection form with admin notes field
    - Implement API calls to update registration status and trigger asset creation
    - Add success/error notifications using existing toast system
    - _Requirements: 2.3, 2.4, 2.5, 2.8_

  - [ ] 2.5 Manual testing for asset registration system
    - Test asset registration list display with different filter combinations
    - Test registration details view with various JSON data structures
    - Test approval workflow: approve registration → verify asset creation → check user notification
    - Test rejection workflow: reject registration → verify admin notes → check user notification
    - Test edge cases: malformed JSON data, missing required fields, concurrent approvals
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.8_

- [ ] 3. Enhance existing expert verification system
  - [ ] 3.1 Create database functions for expert verification management
    - Create SQL file with functions for expert verification status management
    - Add function for blocking/unblocking expert access to core features based on verification status
    - Create function for managing expert document uploads and verification workflow
    - Add trigger to automatically block unverified experts from accessing consultation features
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 3.4, 3.5, 3.8_

  - [ ] 3.2 Modify existing users page to show expert verification queue
    - Update existing `/dashboard/users` page to separate expert verification from user management
    - Enhance existing expert filtering to show verification status properly
    - Add verification document upload status indicators
    - Modify existing expert status badges to reflect verification stages
    - _Requirements: 3.4_

  - [ ] 3.3 Create expert document review interface (new feature)
    - Build new expert details page at `/dashboard/users/[id]` for document review
    - Create document viewer component for expert credentials and qualifications
    - Add image gallery for uploaded qualification documents
    - Display expert expertise areas and experience details from profiles table
    - _Requirements: 3.4_

  - [ ] 3.4 Enhance existing expert approval system and add access blocking
    - Modify existing approve/reject functions in users page to handle verification workflow
    - Add middleware to block unverified experts from accessing consultation features
    - Update existing email notification system for verification status changes
    - Build rejection feedback form with improvement suggestions
    - Create verification status check component for expert dashboard
    - _Requirements: 3.5, 3.6, 3.7, 3.8_

  - [ ] 3.5 Manual testing for expert verification system
    - Test expert verification queue display and filtering functionality
    - Test document review interface with various document types and formats
    - Test verification approval: approve expert → verify access granted → check email notification
    - Test verification rejection: reject expert → verify access blocked → check feedback delivery
    - Test access blocking: unverified expert login → verify consultation features blocked → check verification prompt
    - Test edge cases: missing documents, invalid file formats, concurrent verification attempts
    - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8_

- [ ] 4. Enhance existing consultation management system
  - [ ] 4.1 Create database functions for consultation management
    - Create SQL file with functions for consultation assignment and status management
    - Add function for fetching available experts based on expertise and availability
    - Create function for consultation workflow state transitions with proper validation
    - Add function for consultation resolution with recommendation storage
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 4.1, 4.2, 4.5, 4.6_

  - [ ] 4.2 Create admin consultation assignment interface (new feature)
    - Build new admin consultation queue page to replace dummy data in existing requests page
    - Connect to real consultation data from database consultations table
    - Create expert selection dropdown with availability status from profiles table
    - Implement consultation assignment API functionality using new database functions
    - _Requirements: 4.1, 4.2_

  - [ ] 4.3 Enhance existing expert consultation dashboard
    - Modify existing `/dashboard/requests` page to show only assigned consultations for experts
    - Update existing consultation cards to display real data from consultations table
    - Enhance existing status indicators with proper consultation workflow states
    - Add consultation details modal with user and asset context information
    - _Requirements: 4.3, 4.5, 4.8_

  - [ ] 4.4 Enhance existing consultation resolution workflow
    - Modify existing consultation detail pages to include resolution functionality
    - Update existing consultation status update system to handle resolution workflow
    - Add final recommendation text editor to existing consultation interface
    - Enhance existing consultation archive system in consultations page
    - _Requirements: 4.5, 4.6_

  - [ ] 4.5 Manual testing for consultation management system
    - Test admin consultation queue display and expert assignment functionality
    - Test expert consultation dashboard with assigned consultations only
    - Test consultation assignment: assign to expert → verify expert notification → check status update
    - Test consultation resolution: complete consultation → verify recommendation saved → check user notification
    - Test consultation workflow: pending → assigned → in progress → resolved states
    - Test edge cases: expert unavailability, concurrent assignments, invalid status transitions
    - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.8_

- [ ] 5. Build real-time chat system for consultations (new feature)
  - [ ] 5.1 Create database functions for chat system
    - Create SQL file with functions for consultation message management
    - Add function for real-time message insertion with proper validation
    - Create function for message read status tracking and updates
    - Add function for file attachment handling in chat messages
    - Create triggers for real-time message notifications
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 11.1, 11.2, 11.3, 11.5_

  - [ ] 5.2 Create chat interface components for consultation pages
    - Build new chat component to integrate with existing consultation detail pages
    - Create message list component with proper RTL support matching existing UI
    - Build message input component with file upload using existing upload patterns
    - Add typing indicator functionality using Supabase real-time
    - _Requirements: 11.1, 11.4, 11.7, 11.8_

  - [ ] 5.3 Implement real-time messaging functionality
    - Set up Supabase real-time subscriptions for consultation_messages table
    - Create message sending API endpoints following existing API patterns
    - Implement message delivery confirmation using existing notification system
    - Add offline message queuing using existing error handling patterns
    - _Requirements: 11.2, 11.3, 11.6_

  - [ ] 5.4 Add file sharing capabilities to chat
    - Integrate with existing file upload system for image sharing in chat
    - Create file attachment preview using existing image handling components
    - Add image zoom functionality matching existing image gallery patterns
    - Build file validation using existing security measures
    - _Requirements: 11.5_

  - [ ] 5.5 Manual testing for real-time chat system
    - Test chat interface integration with consultation pages
    - Test real-time messaging: send message → verify instant delivery → check read status
    - Test file sharing: upload image → verify preview → test zoom functionality
    - Test typing indicators and online/offline status
    - Test chat in both Arabic and English interfaces with RTL support
    - Test edge cases: network disconnection, large file uploads, concurrent messaging
    - _Requirements: 11.1, 11.2, 11.3, 11.4, 11.5, 11.6, 11.7, 11.8_

- [ ] 6. Enhance existing notification system
  - [ ] 6.1 Create database functions for notification system
    - Create SQL file with functions for notification management and delivery tracking
    - Add function for creating notifications based on system events (triggers)
    - Create function for push notification payload generation for mobile app
    - Add function for email notification queue management
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 12.1, 12.2, 12.4, 12.5_

  - [ ] 6.2 Build on existing toast notification infrastructure
    - Extend existing toast system in `use-toast.ts` for real-time notifications
    - Create notification badge components for sidebar navigation
    - Implement notification persistence using existing local storage patterns
    - Add notification sound and visual indicators to existing UI components
    - _Requirements: 12.1, 12.2, 12.3, 12.5_

  - [ ] 6.3 Build email notification system (new feature)
    - Create email templates for expert verification status updates
    - Implement email sending functionality via Supabase Edge Functions
    - Integrate with existing user management system for email preferences
    - Build email delivery status tracking in existing admin interface
    - _Requirements: 12.4_

  - [ ] 6.4 Implement push notification integration (new feature)
    - Set up push notification service integration for mobile app communication
    - Create notification payload formatting for different event types
    - Implement notification retry mechanism using existing error handling
    - Add notification preferences to existing user profile management
    - _Requirements: 12.6, 12.7, 12.8_

  - [ ] 6.5 Manual testing for notification system
    - Test in-app notifications: trigger event → verify toast display → check persistence
    - Test email notifications: expert verification status change → verify email sent → check delivery status
    - Test push notifications: consultation assignment → verify mobile notification → check payload format
    - Test notification preferences: update settings → verify respected in delivery
    - Test notification badges and counters in sidebar navigation
    - Test edge cases: email delivery failures, push notification service downtime, notification flooding
    - _Requirements: 12.1, 12.2, 12.3, 12.4, 12.5, 12.6, 12.7, 12.8_

- [ ] 7. Enhance existing analytics dashboard
  - [ ] 7.1 Create database functions for analytics data
    - Create SQL file with functions for dashboard analytics and metrics calculation
    - Add function for user registration trends over time with date filtering
    - Create function for consultation metrics and status distribution
    - Add function for expert performance and activity statistics
    - Prompt user to execute SQL file in Supabase dashboard
    - _Requirements: 8.1, 8.2, 8.3, 8.4_

  - [ ] 7.2 Replace dummy data in existing admin dashboard
    - Update existing AdminView component to show real metrics from database
    - Modify existing DashboardMetrics component to fetch actual user, expert, and asset counts
    - Enhance existing dashboard cards with real-time data from profiles and assets tables
    - Add active consultations and pending requests counters using existing consultation system
    - _Requirements: 8.1, 8.2, 8.3, 8.4_

  - [ ] 7.3 Enhance existing chart components
    - Update existing DashboardChart component to display real consultation data
    - Add time-series charts for consultation trends using existing chart infrastructure
    - Enhance existing responsive layouts for different screen sizes
    - Integrate with existing RTL support for Arabic interface
    - _Requirements: 8.3, 8.6, 8.7_

  - [ ] 7.4 Manual testing for analytics dashboard
    - Test dashboard metrics display with real data from database
    - Test chart functionality: select date range → verify data updates → check chart rendering
    - Test analytics in both Arabic and English interfaces
    - Test responsive layout on different screen sizes
    - Test data refresh and real-time updates when new data is added
    - Test edge cases: no data periods, large datasets, date range edge cases
    - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.6, 8.7_

- [ ] 8. Enhance existing security and data protection
  - [ ] 8.1 Strengthen existing input validation
    - Enhance existing form validation in user management and consultation forms
    - Review and strengthen existing API endpoint validation
    - Improve existing file upload validation in avatar and document upload systems
    - Enhance existing XSS prevention measures in user-generated content display
    - _Requirements: 9.3, 9.4_

  - [ ] 8.2 Strengthen existing authentication security
    - Enhance existing session management in AuthProvider component
    - Review existing Supabase Auth configuration for rate limiting
    - Improve existing password reset flow in forgot-password page
    - Add account lockout mechanism to existing authentication system
    - _Requirements: 9.1, 9.5_

  - [ ] 8.3 Review and enhance existing database security
    - Review existing Row Level Security (RLS) policies in database schema
    - Enhance existing audit logging in account_activation_history table
    - Review data encryption for sensitive fields in existing profiles table
    - Improve existing data deletion procedures with proper audit trails
    - _Requirements: 9.2, 9.7_

- [ ] 9. Enhance existing multi-language support
  - [ ] 9.1 Complete existing Arabic/English interface support
    - Complete missing translations in existing i18n system (ar.json, en.json)
    - Enhance existing RTL layout in DashboardLayout and components
    - Improve existing language-specific formatting in date/number displays
    - Enhance existing language preference persistence in LocaleProvider
    - _Requirements: 7.1, 7.2, 7.6_

  - [ ] 9.2 Enhance existing content localization system
    - Extend existing translation system for admin-generated content
    - Create language-specific templates for new notification system
    - Enhance existing language detection in i18n-provider
    - Improve existing fallback mechanisms for missing translations
    - _Requirements: 7.3, 7.4, 7.5_

  - [ ] 9.3 Manual testing for multi-language support
    - Test language switching: switch Arabic ↔ English → verify UI updates → check RTL/LTR layout
    - Test Arabic interface: navigate all pages → verify RTL layout → check text alignment and icons
    - Test English interface: navigate all pages → verify LTR layout → check proper text rendering
    - Test language persistence: switch language → refresh page → verify language maintained
    - Test missing translations: check fallback to English → verify no broken UI elements
    - Test edge cases: mixed content languages, special characters, long text strings
    - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6_

- [ ] 10. Enhance existing error handling and user experience
  - [ ] 10.1 Improve existing error boundaries and error handling
    - Enhance existing ErrorBoundary and ChunkErrorBoundary components
    - Improve existing error pages and user-friendly error messages
    - Enhance existing error logging using existing patterns
    - Add graceful degradation for non-critical features in existing components
    - _Requirements: 9.5_

  - [ ] 10.2 Enhance existing loading states and offline handling
    - Improve existing loading skeletons in dashboard and user management pages
    - Enhance existing offline detection and user notification systems
    - Improve existing retry mechanisms for failed API calls in Supabase client
    - Add optimistic updates to existing user management and consultation systems
    - _Requirements: 11.6_

- [ ] 11. Enhance integration with mobile application APIs
  - [ ] 11.1 Review and enhance existing API endpoints
    - Review existing Supabase API configuration for mobile app communication
    - Enhance existing authentication and authorization for mobile requests
    - Review existing data synchronization patterns for offline mobile functionality
    - Add API versioning support to existing Supabase configuration
    - _Requirements: 13.1, 13.3, 13.7_

  - [ ] 11.2 Enhance existing real-time synchronization
    - Review existing Supabase real-time configuration for web and mobile sync
    - Enhance existing conflict resolution in database functions
    - Integrate push notification delivery with existing notification system
    - Review and optimize existing API performance patterns
    - _Requirements: 13.2, 13.4, 13.5_

- [ ] 12. Testing and quality assurance for existing and new features
  - [ ] 12.1 Write tests for existing and new functionality
    - Create unit tests for existing utility functions in utils directory
    - Build component tests for existing and new UI components
    - Test existing authentication flow and role-based access
    - Add database function testing for existing and new database functions
    - _Requirements: All requirements validation_

  - [ ] 12.2 Implement integration testing for the complete system
    - Test existing and new API endpoint integrations
    - Build real-time functionality testing for new chat system
    - Test existing and enhanced role-based access control
    - Add cross-browser compatibility testing for existing RTL support
    - _Requirements: All requirements validation_

- [ ] 13. Performance optimization and deployment preparation
  - [ ] 13.1 Fix critical web portal loading performance issues
    - Investigate and fix 32.4s initial startup time and 19.5s instrumentation compilation
    - Remove excessive middleware cookie logging that's causing performance degradation
    - Fix Supabase realtime-js critical dependency warning affecting bundle size
    - Optimize middleware.ts to reduce redundant cookie checks (currently checking same cookies 5x per request)
    - Remove or optimize deprecated util._extend usage causing Node.js warnings
    - Implement proper error handling for "user aborted request" retries
    - Add performance monitoring to identify bottlenecks in page load (currently 60797ms for GET /)
    - _Requirements: System performance and scalability_

  - [ ] 13.2 Optimize existing application performance
    - Review existing code splitting and lazy loading in dashboard components
    - Optimize existing database queries and review indexing in schema
    - Enhance existing image optimization in avatar and file upload systems
    - Review existing performance monitoring patterns
    - _Requirements: System performance and scalability_

  - [ ] 13.3 Enhance existing deployment configuration
    - Review existing environment variables and secrets configuration
    - Enhance existing Netlify deployment configuration
    - Review existing database migration patterns and backup procedures
    - Improve existing monitoring and logging infrastructure
    - _Requirements: System reliability and maintenance_

- [ ] 14. Fix RTL (Right-to-Left) language support issues
  - [ ] 14.1 Fix RTL issues on main landing page
    - Audit and fix RTL layout problems on the main landing page
    - Add language toggle buttons for Arabic/English switching on landing page
    - Ensure proper text alignment, icon positioning, and layout flow for Arabic interface
    - Test navigation, buttons, and interactive elements in RTL mode
    - Verify responsive design works correctly in both RTL and LTR modes
    - _Requirements: 7.1, 7.2, 7.6_

  - [ ] 14.2 Complete RTL audit and fixes for main app interface
    - Conduct comprehensive RTL audit of all dashboard pages and components
    - Fix remaining RTL layout issues in sidebar navigation, forms, and data tables
    - Ensure proper RTL support in modals, dropdowns, and overlay components
    - Fix text alignment and spacing issues in Arabic interface
    - Verify icon positioning and directional elements work correctly in RTL
    - Test all interactive elements (buttons, inputs, selects) in Arabic mode
    - _Requirements: 7.1, 7.2, 7.6_

  - [ ] 14.3 Manual testing for RTL language support
    - Test complete user journey in Arabic: landing page → registration → dashboard navigation
    - Verify language toggle functionality works seamlessly throughout the application
    - Test all forms and input fields with Arabic text input and RTL layout
    - Verify data tables, charts, and complex layouts render correctly in RTL
    - Test responsive behavior on different screen sizes in both Arabic and English
    - Check for any text overflow, alignment issues, or broken layouts in RTL mode
    - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6_