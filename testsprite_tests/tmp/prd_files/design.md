# Design Document

## Overview

The AgriDustria web portal is a Next.js-based application that provides administrative and expert interfaces for managing agricultural and industrial consultations. The system integrates with Supabase for backend services and implements role-based access control to ensure appropriate feature access for different user types.

The architecture follows a modern web application pattern with:
- **Frontend**: Next.js 14 with TypeScript and Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Real-time, Storage)
- **Authentication**: Supabase Auth with JWT tokens
- **Real-time**: Supabase Real-time subscriptions for chat and notifications
- **Deployment**: Netlify for web hosting, Supabase Cloud for backend

## Architecture

### System Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Portal - Next.js]
        MOB[Mobile App - React Native]
    end
    
    subgraph "API Layer"
        API[Supabase API Gateway]
        AUTH[Supabase Auth]
        RT[Real-time Engine]
    end
    
    subgraph "Data Layer"
        DB[(PostgreSQL Database)]
        STORAGE[File Storage]
    end
    
    subgraph "External Services"
        EMAIL[Email Service]
        PUSH[Push Notifications]
    end
    
    WEB --> API
    MOB --> API
    API --> AUTH
    API --> DB
    API --> STORAGE
    RT --> WEB
    RT --> MOB
    AUTH --> EMAIL
    API --> PUSH
```

### Database Schema Architecture

The system uses the existing Supabase schema with key tables:

- **auth.users**: Core authentication managed by Supabase
- **public.profiles**: Extended user profile information
- **public.registration_requests**: Asset registration requests from mobile users
- **public.assets**: Approved farm/factory assets
- **public.consultations**: Consultation requests and management
- **public.consultation_messages**: Chat messages between experts and users
- **public.account_activation_history**: Audit trail for user actions

### Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant W as Web Portal
    participant S as Supabase Auth
    participant D as Database
    
    U->>W: Login Request
    W->>S: Authenticate
    S->>D: Validate Credentials
    D-->>S: User Data
    S-->>W: JWT Token + User Info
    W->>D: Fetch Profile Data
    D-->>W: Profile + Role Info
    W-->>U: Dashboard (Role-based)
```

## Components and Interfaces

### 1. Authentication System

**Components:**
- `AuthProvider`: Context provider for authentication state
- `LoginForm`: User login interface
- `PasswordReset`: Password recovery functionality

**Key Features:**
- JWT-based authentication with Supabase
- Role-based redirection after login
- Secure session management
- Password recovery via email

### 2. Dashboard Layout System

**Components:**
- `DashboardLayout`: Main layout wrapper with sidebar and header
- `DashboardSidebar`: Role-based navigation menu
- `DashboardHeader`: Top navigation with user profile and language switcher

**Role-Based Navigation:**

**Admin Navigation:**
- Dashboard (Analytics)
- Users Management
- Asset Registration Requests
- Consultation Management
- Expert Verification
- Settings
- Support

**Expert Navigation:**
- Dashboard (Personal Stats)
- Assigned Consultations
- Chat Interface
- My Profile
- Support

### 3. Admin Management Interfaces

#### User Management
**Components:**
- `UsersList`: Display all platform users
- `UserDetails`: Individual user profile management
- `UserActions`: Activate/deactivate user accounts

#### Asset Registration Management
**Components:**
- `RegistrationRequestsList`: Pending asset registration requests
- `RegistrationDetails`: Detailed view of asset registration data
- `ApprovalActions`: Approve/reject registration requests

#### Expert Verification Management
**Components:**
- `ExpertVerificationQueue`: List of experts pending verification
- `ExpertDocuments`: Document viewer for expert credentials
- `VerificationActions`: Approve/reject expert verification

### 4. Expert Interface Components

#### Consultation Management
**Components:**
- `ConsultationQueue`: List of assigned consultations
- `ConsultationDetails`: Detailed consultation view with user data
- `ConsultationChat`: Real-time chat interface
- `ConsultationActions`: Mark as resolved, request more info

#### Profile Management
**Components:**
- `ExpertProfile`: Expert profile editing interface
- `ExpertStats`: Personal consultation statistics
- `AvailabilityToggle`: Set availability status

### 5. Real-time Chat System

**Components:**
- `ChatInterface`: Main chat component
- `MessageList`: Display conversation history
- `MessageInput`: Send new messages
- `FileUpload`: Share images and documents
- `TypingIndicator`: Show when user is typing

**Real-time Features:**
- Instant message delivery
- Typing indicators
- Online/offline status
- Message read receipts
- File sharing capabilities

### 6. Notification System

**Components:**
- `NotificationProvider`: Context for notification state
- `NotificationList`: Display recent notifications
- `NotificationItem`: Individual notification component
- `NotificationSettings`: User notification preferences

**Notification Types:**
- Asset registration status updates
- Consultation assignments
- Chat messages
- Expert verification status
- System announcements

## Data Models

### Core Data Models

#### User Profile Model
```typescript
interface Profile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  entity_type: 'FARM' | 'FACTORY';
  account_activated: boolean;
  password_set: boolean;
  role: 'admin' | 'expert' | 'farmer' | 'factory_worker';
  created_at: string;
  updated_at: string;
}
```

#### Asset Registration Model
```typescript
interface RegistrationRequest {
  id: string;
  user_id: string;
  entity_type: 'FARM' | 'FACTORY';
  registration_data: {
    registration_steps: Array<{
      step_name: string;
      step_data: Record<string, any>;
    }>;
  };
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  reviewed_by?: string;
  reviewed_at?: string;
  admin_notes?: string;
  created_at: string;
  updated_at: string;
}
```

#### Consultation Model
```typescript
interface Consultation {
  id: string;
  requester_id: string;
  expert_id?: string;
  asset_id: string;
  title: string;
  description: string;
  status: 'PENDING' | 'ASSIGNED' | 'IN_PROGRESS' | 'RESOLVED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  images?: string[];
  assigned_at?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
}
```

#### Chat Message Model
```typescript
interface ConsultationMessage {
  id: string;
  consultation_id: string;
  sender_id: string;
  sender_type: 'expert' | 'user';
  message_type: 'text' | 'image' | 'file';
  content: string;
  file_url?: string;
  read_at?: string;
  created_at: string;
}
```

### Database Relationships

```mermaid
erDiagram
    PROFILES ||--o{ REGISTRATION_REQUESTS : creates
    PROFILES ||--o{ ASSETS : owns
    PROFILES ||--o{ CONSULTATIONS : requests
    PROFILES ||--o{ CONSULTATIONS : handles
    ASSETS ||--o{ CONSULTATIONS : subject_of
    CONSULTATIONS ||--o{ CONSULTATION_MESSAGES : contains
    PROFILES ||--o{ CONSULTATION_MESSAGES : sends
```

## Error Handling

### Error Handling Strategy

1. **Authentication Errors**
   - Invalid credentials: Redirect to login with error message
   - Session expired: Auto-refresh token or redirect to login
   - Insufficient permissions: Show access denied page

2. **Data Loading Errors**
   - Network errors: Show retry mechanism
   - Data not found: Display appropriate empty states
   - Server errors: Show generic error message with support contact

3. **Form Validation Errors**
   - Client-side validation for immediate feedback
   - Server-side validation for security
   - Clear error messages with correction guidance

4. **Real-time Connection Errors**
   - Connection lost: Show offline indicator
   - Message delivery failed: Queue for retry
   - Reconnection handling: Automatic with user notification

### Error Boundaries

```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, ErrorBoundaryState> {
  // Catch JavaScript errors anywhere in component tree
  // Log errors to monitoring service
  // Display fallback UI
}
```

## Testing Strategy

### Testing Approach

1. **Unit Testing**
   - Component testing with React Testing Library
   - Utility function testing with Jest
   - Database function testing with Supabase test client

2. **Integration Testing**
   - API endpoint testing
   - Authentication flow testing
   - Real-time functionality testing

3. **End-to-End Testing**
   - Critical user journeys (login, consultation flow)
   - Cross-browser compatibility
   - Mobile responsiveness

### Test Coverage Areas

- **Authentication**: Login, logout, role-based access
- **Admin Functions**: User management, asset approval, expert verification
- **Expert Functions**: Consultation management, chat functionality
- **Real-time Features**: Chat messages, notifications
- **Data Integrity**: Form validation, database constraints
- **Error Handling**: Network failures, invalid data, permission errors

### Testing Tools

- **Jest**: Unit testing framework
- **React Testing Library**: Component testing
- **Cypress**: End-to-end testing
- **Supabase Test Client**: Database testing
- **MSW (Mock Service Worker)**: API mocking

## Performance Considerations

### Optimization Strategies

1. **Frontend Performance**
   - Code splitting with Next.js dynamic imports
   - Image optimization with Next.js Image component
   - Lazy loading for non-critical components
   - Memoization for expensive calculations

2. **Database Performance**
   - Proper indexing on frequently queried columns
   - Row Level Security (RLS) policies for data access
   - Connection pooling with Supabase
   - Query optimization for large datasets

3. **Real-time Performance**
   - Selective subscriptions to reduce bandwidth
   - Message batching for high-frequency updates
   - Connection management and cleanup
   - Offline queue for message reliability

4. **Caching Strategy**
   - Browser caching for static assets
   - API response caching where appropriate
   - User profile caching in local state
   - Image caching for consultation attachments

### Monitoring and Analytics

- **Performance Monitoring**: Core Web Vitals tracking
- **Error Tracking**: Sentry integration for error reporting
- **User Analytics**: Basic usage statistics (respecting privacy)
- **Database Monitoring**: Query performance and connection health

## Security Considerations

### Security Measures

1. **Authentication Security**
   - JWT token validation on all protected routes
   - Secure token storage (httpOnly cookies where possible)
   - Session timeout and refresh mechanisms
   - Multi-factor authentication support (future enhancement)

2. **Authorization Security**
   - Role-based access control (RBAC)
   - Row Level Security (RLS) in database
   - API endpoint protection
   - Feature-level permission checks

3. **Data Security**
   - Input validation and sanitization
   - SQL injection prevention through parameterized queries
   - XSS prevention through proper output encoding
   - File upload validation and virus scanning

4. **Communication Security**
   - HTTPS enforcement for all communications
   - Secure WebSocket connections for real-time features
   - API rate limiting to prevent abuse
   - CORS configuration for cross-origin requests

### Privacy and Compliance

- **Data Minimization**: Collect only necessary user data
- **Data Retention**: Implement appropriate data retention policies
- **User Consent**: Clear consent mechanisms for data processing
- **Data Export**: Allow users to export their data
- **Right to Deletion**: Implement user data deletion capabilities