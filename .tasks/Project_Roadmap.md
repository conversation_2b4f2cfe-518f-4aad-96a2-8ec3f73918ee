This document provides a comprehensive, high-level overview of all features and tasks for the project, serving as the single source of truth for development planning. It is structured to align with the platform components: Core Infrastructure, Admin Web App, Expert Web App, and Farmer Mobile App. 

---
## I. Core Infrastructure & Shared Features

  

This section covers foundational tasks that are shared across all platforms.

  

### 1. ~~Project Setup & Configuration~~

- [x] Set up Next.js project for web apps (experts and admins).

- [x] Set up React Native project for mobile app (farmers).

- [x] Configure Supabase project (database, auth, storage).

- [x] Set up basic CI/CD pipeline for web app on Netlify.

- [x] Set up CI/CD pipeline for mobile app (e.g., using EAS Build).

  

### 2. Database Schema & Security (2 days)

- [ ] **Implement Supabase Row-Level Security (RLS) policies for all tables.**

- [ ] **Backend:**

	- [ ] Define and apply RLS policies for `profiles` to ensure users can only access their own data.
	
	- [ ] Define and apply RLS policies for `farms`, `consultations`, `payments`, etc., based on user roles and ownership.

- [x] **Configure Supabase file storage for secure uploads.**

- [x] **Storage Buckets:**

	- [x] Create private bucket: `avatars`. **Policies:** Allow public read, but only authenticated users can upload/update/delete their own file.
	
	- [x] Create private bucket: `expert-documents`. **Policies:** Only the owning expert and admins can perform any action.
	
	- [x] Create private bucket: `farm-attachments`. **Policies:** The owning farmer, assigned expert, and admins can read. Only the farmer can write/delete.

  

### 3. User Authentication & Profile Management (1 day)

- [ ] **Implement password recovery flow for all platforms.**

- [ ] **Frontend (Web & Mobile):**

	- [ ] Add a "Forgot Password" link on the sign-in pages (`app/(auth)/sign-in/page.tsx`).
	
	- [ ] Create a "Forgot Password" page to request a reset link.
	
	- [ ] Create a "Reset Password" page (`app/auth/set-password/page.tsx`) to handle the password update.

- [ ] **Backend:**

	- [ ] Configure the redirect URL in Supabase Auth settings to point to the "Reset Password" page.

- [ ] **Implement shared user profile management components.**

- [ ] **Frontend (Web & Mobile):**

	- [ ] Create a `my-profile` page where users can view and edit their information from the `profiles` table.
	
	- [ ] Implement an avatar upload component that uploads to the `avatars` storage bucket and updates the `profile_picture_url` in the `profiles` table.

  

### 4. API Gateway & Services (1 day)

- [ ] **Secure all API endpoints and database functions.**

- [ ] **Backend:**

	- [ ] For all PostgreSQL functions, set the `SECURITY DEFINER` property where necessary to allow elevated privileges securely.
	
	- [ ] Ensure all Supabase Edge Functions check for user authentication and role before executing logic.

  

### 5. Localization Support (Arabic/English) (1 day)

- [ ] **Implement multilingual notifications.**

- [ ] **Backend:**

	- [ ] When sending notifications (email or push), retrieve the user's `language_preference` from their `profiles` record.
	
	- [ ] Load the appropriate translation file (e.g., `en.json`, `ar.json`) and send the notification in the user's preferred language.

  

### 6. Notification System (2 days)

- [ ] **Implement base setup for Push & Email Notifications.**

- [ ] **Configuration:**

	- [ ] Configure Expo Push Notifications (FCM for Android, APNs for iOS).
	
	- [ ] Configure an email service provider (e.g., Resend) and add its API key to environment variables.

- [ ] **Integration:**

	- [ ] **Mobile App:** On login, request notification permissions and save the Expo push token to the `profiles.push_token` column.
	
	- [ ] **Edge Function:** Create a central `send-notification` Edge Function that accepts `user_id`, `title`, `message`, and `type` (`email` or `push`). It will fetch the user's contact info and dispatch the notification via the appropriate service.
	
	- [ ] **Database Triggers:** Create database webhooks on the `consultations` and `payments` tables. When a `status` field changes, the webhook will call the `send-notification` function with the correct context.

- [ ] **Testing:**

	- [ ] Unit test the `send-notification` Edge Function by invoking it directly.
	
	- [ ] Integration test by manually updating a record in the database and verifying a notification is received on a test device or email client.

  

### 7. Payment Gateway Integration (Paymob with Escrow) (3 days)

- [ ] **Develop core logic for secure, escrow-based transaction processing.**

- [ ] **Payment Flow:**

1. **Farmer Pays:** On consultation request, call an Edge Function to create a Paymob payment intent. The funds are charged but held by Paymob.

2. **Database Update:** A record is created in the `payments` table with `status: 'held'`.

3. **Expert Accepts:** The consultation request is accepted by the expert.

4. **Consultation Completed:** The expert marks the consultation as `'completed'`.

5. **Release Funds:** This action triggers an Edge Function that calls Paymob's API to release the held funds to the expert's account.

6. **Final DB Update:** A webhook from Paymob confirms the payout. The `payments` status is updated to `'paid_out'`, and a record is created in the `expert_earnings` table.

- [ ] **Dispute Management:**

- [ ] **Schema Change:** Add a `status` column to the `consultations` table (e.g., `scheduled`, `active`, `completed`, `disputed`).

```sql

ALTER TABLE consultations

ADD COLUMN status TEXT DEFAULT 'scheduled';

```

- [ ] **Backend:** Create admin-only functions that can trigger a refund to the farmer or a forced payout to the expert in case of a dispute.

  

  

---

  

  

## II. Admin Web Application

  

### 1. Admin Management (1 day)

- [ ] **Implement UI for creating, editing, and viewing admin accounts**

- [ ] **Frontend:**

	- [ ] Create a page at [`src/app/dashboard/management/page.tsx`](src/app/dashboard/management/page.tsx) to list all admin users.
	
	- [ ] Develop a form on this page to invite a new admin by email.

- [ ] **Backend:**

	- [ ] Create the PostgreSQL function `create_admin_user` (as previously defined) to handle the creation logic.

- [ ] **Database:**

	- [ ] No schema changes required.

  

### 2. Experts Management (1 day)

- [ ] **Implement dashboard to review, approve, or reject expert applications**

- [ ] **Frontend:**

	- [ ] Create a page at [`src/app/dashboard/users/page.tsx`](src/app/dashboard/users/page.tsx) to list all experts with a `pending` verification status.
	
	- [ ] On the user detail page [`src/app/dashboard/users/[id]/page.tsx`](src/app/dashboard/users/[id]/page.tsx), display expert-submitted documents and profile information for review.

- [ ] **Backend:**

	- [ ] Create a PostgreSQL function `get_pending_experts()` to fetch applications.
	
	- [ ] Create functions `approve_expert(expert_id_in uuid)` and `reject_expert(expert_id_in uuid, reason text)` to update the `verification_status` in the `expert_profiles` table.

- [ ] **Database:**

	- [ ] No schema changes required.

- [ ] **Implement UI for viewing, suspending, or deleting expert accounts**

- [ ] **Frontend:**

	- [ ] On the [`src/app/dashboard/users/page.tsx`](src/app/dashboard/users/page.tsx), add controls to suspend (set `is_active` to `false` in `profiles`) or delete an expert.

- [ ] **Backend:**

	- [ ] Create a function `toggle_expert_status(expert_id_in uuid, new_status boolean)` to suspend/reactivate experts.

- [ ] **Database:**

	- [ ] No schema changes required.

  

### 3. Farmer Management (1 day)

- [ ] **Implement UI to view/edit farmer profiles and subscription status**

- [ ] **Frontend:**

	- [ ] Enhance the [`src/app/dashboard/users/page.tsx`](src/app/dashboard/users/page.tsx) to also list farmer profiles.
	
	- [ ] On the detail page [`src/app/dashboard/users/[id]/page.tsx`](src/app/dashboard/users/[id]/page.tsx), add a section to manage their subscription.

- [ ] **Backend:**

	- [ ] Create a function `get_farmers()` to fetch all user profiles with the 'farmer' role.

- [ ] **Database:**

	- [ ] **Schema Change:** Apply the following SQL to add `subscription_status` to the `farmer_profiles` table.

```sql

ALTER TABLE farmer_profiles

ADD COLUMN subscription_status TEXT DEFAULT 'inactive';

```

  

### 4. Ticket and Issue Management (1 day)

- [ ] **Implement a ticketing system to create, track, and resolve support tickets**

- [ ] **Frontend:**

	- [ ] Utilize [`src/app/dashboard/support/page.tsx`](src/app/dashboard/support/page.tsx) to list all support tickets from the `support_tickets` table.
	
	- [ ] Create a detail view (`src/app/dashboard/support/[id]/page.tsx`) to see ticket history, comments, and attachments.

- [ ] **Backend:**

	- [ ] Create a function `get_all_tickets()` to fetch tickets for the dashboard.
	
	- [ ] Create a function `get_ticket_details(ticket_id_in uuid)` to fetch a single ticket with its related comments and attachments.

- [ ] **Database:**

	- [ ] No schema changes required.

  

### 5. Financial & System Monitoring (2 days)

- [ ] **Implement dashboard for revenue and transaction reporting**

- [ ] **Frontend:**

	- [ ] Create a page at [`src/app/dashboard/analytics/page.tsx`](src/app/dashboard/analytics/page.tsx) with charts and tables to visualize financial data.

- [ ] **Backend:**

	- [ ] Create the `get_financial_overview()` function (as previously defined) to aggregate key metrics.

- [ ] **Database:**

	- [ ] No schema changes required.

  

  

---

  

  

## III. Expert Web Application

  

### 1. Expert Verification & Onboarding (1 day)

- [ ] **Implement document upload functionality for verification**

- [ ] **Frontend:**

	- [ ] In [`src/app/dashboard/my-profile/page.tsx`](src/app/dashboard/my-profile/page.tsx), create a component for file uploads that allows experts to submit documents.

- [ ] **Backend:**

	- [ ] Handle file uploads to a Supabase Storage bucket (`expert-documents`).
	
	- [ ] Create a record in the `files` table and link it via the `expert_documents` table with a `pending` status.

- [ ] **Database:**

	- [ ] No schema changes required.

- [ ] **Create an onboarding flow to ensure profile is complete and verified**

- [ ] **Frontend:**

	- [ ] In [`src/middleware.ts`](src/middleware.ts), add logic to check if an authenticated expert's `verification_status` is `verified`.
	
	- [ ] If not verified, redirect the expert to the [`src/app/dashboard/verification-pending/page.tsx`](src/app/dashboard/verification-pending/page.tsx) or their profile page.

- [ ] **Backend:**

	- [ ] No direct backend action is needed.

- [ ] **Database:**

	- [ ] No schema changes required.

  

### 2. Professional Profile & Package Management (1 day)

- [ ] **Implement UI for experts to create/edit their professional profile**

- [ ] **Frontend:**

	- [ ] The [`src/app/dashboard/my-profile/page.tsx`](src/app/dashboard/my-profile/page.tsx) will serve as the profile editing form.

- [ ] **Backend:**

	- [ ] On form submission, update the corresponding record in the `expert_profiles` table.

- [ ] **Database:**

	- [ ] No schema changes required.

- [ ] **Implement UI to create, edit, and delete consultation packages**

- [ ] **Frontend:**

	- [ ] In [`src/app/dashboard/my-profile/page.tsx`](src/app/dashboard/my-profile/page.tsx), add a section to manage consultation packages.

- [ ] **Backend:**

	- [ ] Implement logic to `INSERT`, `UPDATE`, and `DELETE` records in the `consultation_packages` table.

- [ ] **Database:**

	- [ ] No schema changes required.

  

### 3. Expert Dashboard (1 day)
 
- [ ] **Display key statistics and provide an overview of recent activity**

- [ ] **Frontend:**

	- [ ] The main dashboard page at [`src/app/dashboard/page.tsx`](src/app/dashboard/page.tsx) will display key metrics using components like [`src/components/dashboard-metrics.tsx`](src/components/dashboard-metrics.tsx).

- [ ] **Backend:**

	- [ ] Use the `get_expert_dashboard_stats` function (as previously defined).

- [ ] **Database:**

	- [ ] No schema changes required.

  

### 4. Consultation Management (1 day)

- [ ] **Implement "Requests" page to view and manage incoming consultation requests**

- [ ] **Frontend:**

	- [ ] Use [`src/app/dashboard/requests/page.tsx`](src/app/dashboard/requests/page.tsx) to list all `pending` consultation requests.
	
	- [ ] Add "Accept" and "Reject" buttons.

- [ ] **Backend:**

	- [ ] Create a function `get_expert_consultation_requests(expert_id_in uuid)` to fetch requests.
	
	- [ ] On "Accept", update `consultation_requests` status and create a record in the `consultations` table.

- [ ] **Database:**

	- [ ] No schema changes required.

- [ ] **Implement "Active Consultations" page with text-based chat**

- [ ] **Frontend:**

	- [ ] Use [`src/app/dashboard/consultations/page.tsx`](src/app/dashboard/consultations/page.tsx) to list `active` consultations.
	
	- [ ] The detail page [`src/app/dashboard/consultations/[id]/page.tsx`](src/app/dashboard/consultations/[id]/page.tsx) will contain the chat interface.

- [ ] **Backend:**

	- [ ] Enable Supabase Realtime on the `consultation_messages` table.

- [ ] **Database:**

	- [ ] No schema changes required.

  

### 5. Earnings & Payout Management (1 day)

- [ ] **Implement a dashboard to track earnings per consultation**

- [ ] **Frontend:**

	- [ ] Create an "Earnings" page at [`src/app/dashboard/analytics/page.tsx`](src/app/dashboard/analytics/page.tsx) to display a detailed breakdown of earnings.

- [ ] **Backend:**

	- [ ] Use the `get_expert_earnings_history` function (as previously defined).

- [ ] **Database:**

	- [ ] No schema changes required.

  

  

---

  

  

## IV. Farmer Mobile Application

  

### 1. Farm Details Registration & Management (1 day)

- [ ] **Implement UI to create and manage multiple farm profiles**

- [ ] **Frontend:**

	- [ ] Create a form in `app/(tabs)/farms.tsx` to input `name`, `farm_type`, `size_hectares`, and `location_address`.
	
	- [ ] Add a button to trigger the creation of a new farm.
	
	- [ ] Develop a UI component (`components/ui/FarmCard.tsx`) to display a summary of each farm.
	
	- [ ] Implement a file picker to select and upload photos for a farm.

- [ ] **Backend:**

	- [ ] On form submission, call a Supabase function to insert a new record into the `farms` table.
	
	- [ ] Handle file uploads to a Supabase Storage bucket named `farm-attachments`.
	
	- [ ] Create a new record in the `files` table and link it via the `farm_attachments` table.

- [ ] **Database:**

	- [ ] No schema changes required. Interacts with `farms`, `files`, and `farm_attachments`.

- [ ] **Implement offline data entry and synchronization for farm data**

- [ ] **Frontend:**

	- [ ] Integrate a local database solution (e.g., WatermelonDB or SQLite) to store farm data created/edited while offline.
	
	- [ ] Create a "sync" button or automatic trigger to push local changes to the server when connectivity is restored.

- [ ] **Backend:**

	- [ ] Create a Supabase Edge Function to handle batch-upserting of offline data.

- [ ] **Database:**

	- [ ] **Schema Change:** Apply the following SQL to modify the `farms` table.

```sql

ALTER TABLE farms

ADD COLUMN created_offline boolean,

ADD COLUMN synced_at timestamp;

```

  

### 2. Expert Discovery & Search (1 day)

- [ ] **Implement search and filter functionality to find experts**

- [ ] **Frontend:**

	- [ ] In `app/(tabs)/experts.tsx`, display a list of experts fetched from the backend.
	
	- [ ] Add a search bar and filter options (e.g., a dropdown for specializations).

- [ ] **Backend:**

- [ ] Create the following PostgreSQL function `get_available_experts()` to fetch experts.

```sql

CREATE OR REPLACE FUNCTION get_available_experts(

search_query TEXT DEFAULT NULL,

spec_id UUID DEFAULT NULL

)

RETURNS TABLE(id uuid, first_name text, last_name text, profile_picture_url character, bio text, average_rating numeric) AS $$

BEGIN

RETURN QUERY

SELECT

p.id,

p.first_name,

p.last_name,

p.profile_picture_url,

ep.bio,

ep.average_rating

FROM

profiles p

JOIN

expert_profiles ep ON p.id = ep.id

LEFT JOIN

expert_specializations es ON p.id = es.expert_id

WHERE

ep.is_available = TRUE

AND p.is_active = TRUE

AND (search_query IS NULL OR p.first_name ILIKE '%' || search_query || '%' OR p.last_name ILIKE '%' || search_query || '%')

AND (spec_id IS NULL OR es.specialization_id = spec_id)

GROUP BY

p.id, ep.id;

END;

$$ LANGUAGE plpgsql;

```

- [ ] **Database:**

	- [ ] No schema changes required. Interacts with `expert_profiles`, `profiles`, and `expert_specializations`.

- [ ] **Implement UI to view expert profiles and their consultation packages**

- [ ] **Frontend:**

	- [ ] Create a new screen `app/expert-details.tsx` to display an expert's full profile (bio, experience, ratings).
	
	- [ ] On this screen, list the consultation packages offered by the expert.

- [ ] **Backend:**

	- [ ] Create the following PostgreSQL function `get_expert_details(expert_id_in uuid)` to fetch all necessary data.

```sql

CREATE OR REPLACE FUNCTION get_expert_details(expert_id_in UUID)

RETURNS json AS $$

DECLARE

expert_data json;

BEGIN

SELECT json_build_object(

'profile', (SELECT row_to_json(p) FROM profiles p WHERE p.id = expert_id_in),

'expert_profile', (SELECT row_to_json(ep) FROM expert_profiles ep WHERE ep.id = expert_id_in),

'reviews', (SELECT json_agg(er) FROM expert_reviews er WHERE er.expert_id = expert_id_in),

'packages', (SELECT json_agg(cp) FROM consultation_packages cp WHERE cp.expert_id = expert_id_in AND cp.is_active = true)

) INTO expert_data;

RETURN expert_data;

END;

$$ LANGUAGE plpgsql;

```

- [ ] **Database:**

	- [ ] No schema changes required.

  

### 3. Consultation & Communication (1 day)

- [ ] **Implement workflow to submit consultation requests**

- [ ] **Frontend:**

	- [ ] Create a "Request Consultation" form where a farmer can describe the problem, select a farm, and choose a package.

- [ ] **Backend:**

	- [ ] On submission, insert a new record into the `consultation_requests` table with a `pending` status.

- [ ] **Database:**

	- [ ] No schema changes required.

- [ ] **Implement payment flow for consultation requests via Paymob**

- [ ] **Frontend:**

	- [ ] After submitting a request, navigate the user to a payment screen that integrates with the Paymob SDK.

- [ ] **Backend:**

	- [ ] Create a Supabase Edge Function to securely generate a payment token for Paymob.
	
	- [ ] Create a webhook endpoint to listen for payment confirmation from Paymob. On success, create a record in the `payments` table.

- [ ] **Database:**

	- [ ] No schema changes required.

- [ ] **Track consultation status (pending, accepted, active, completed)**

- [ ] **Frontend:**

	- [ ] In `app/(tabs)/consultations.tsx`, display a list of all consultations and their current status.

- [ ] **Backend:**

	- [ ] Create the following PostgreSQL function `get_farmer_consultations(farmer_id_in uuid)` to fetch all relevant data.

```sql

CREATE OR REPLACE FUNCTION get_farmer_consultations(farmer_id_in UUID)

RETURNS TABLE (

request_id uuid,

request_status character,

problem_description text,

expert_id uuid,

expert_name text

) AS $$

BEGIN

RETURN QUERY

SELECT

cr.id,

cr.status,

cr.problem_description,

cr.expert_id,

(p.first_name || ' ' || p.last_name) as expert_name

FROM

consultation_requests cr

JOIN

profiles p ON cr.expert_id = p.id

WHERE

cr.farmer_id = farmer_id_in;

END;

$$ LANGUAGE plpgsql;

```

- [ ] **Database:**

	- [ ] No schema changes required.

- [ ] **Implement text-based chat with the paired expert**

- [ ] **Frontend:**

	- [ ] Create a chat screen that opens when a consultation is `active`.

	- [ ] Subscribe to Supabase Realtime to listen for new messages on the `consultation_messages` table.

- [ ] **Backend:**

	- [ ] Enable Supabase Realtime on the `consultation_messages` table.

- [ ] **Database:**

	- [ ] No schema changes required.

- [ ] **Implement push notifications for consultation updates**

- [ ] **Frontend:**

	- [ ] Configure the Expo app to request notification permissions and handle incoming push notifications.

- [ ] **Backend:**

	- [ ] Use Supabase Database Webhooks on the `consultation_requests` table to trigger an Edge Function whenever the `status` changes. This function will send a push notification to the relevant farmer.

- [ ] **Database:**

	- [ ] **Schema Change:** Apply the following SQL to modify the `profiles` table.

```sql

ALTER TABLE profiles

ADD COLUMN push_token text;

```

  

### 4. Payment Management (1 day)

- [ ] **Implement UI for setting up payment methods**

- [ ] **Frontend:**

	- [ ] Create a screen where users can view and manage their saved payment methods (if applicable via Paymob).

- [ ] **Backend:**

	- [ ] Securely interact with Paymob's API to manage customer payment information.

- [ ] **Provide a transaction history view with receipts**

- [ ] **Frontend:**

	- [ ] Create a screen listing all past payments.
	
	- [ ] Allow users to view details for each transaction.

- [ ] **Backend:**

	- [ ] Create the following PostgreSQL function `get_farmer_payments(farmer_id_in uuid)` to fetch all records from the `payments` table for the current user.

```sql

CREATE OR REPLACE FUNCTION get_farmer_payments(farmer_id_in UUID)

RETURNS TABLE (

payment_id uuid,

total_amount numeric,

status character,

payment_date timestamp

) AS $$

BEGIN

RETURN QUERY

SELECT

p.id,

p.total_amount,

p.status,

p.payment_date

FROM

payments p

WHERE

p.farmer_id = farmer_id_in;

END;

$$ LANGUAGE plpgsql;

```

- [ ] **Database:**

	- [ ] No schema changes required.

  

  

---

  

  

## V. Finalization & Deployment

  

  

### 1. Testing & Optimization (3 days)

  

- [ ] Test all MVP features end-to-end on all platforms.

  

- [ ] Optimize database queries and API response times.

  

- [ ] Test offline sync and real-time features for reliability.

  

- [ ] Conduct a final security review and fix critical bugs.

  

  

### 2. Deployment (1 day)

  

- [x] Deploy web apps to Netlify.

  

- [x] Prepare mobile app for distribution (APK or app store submission).

  

- [ ] Finalize Supabase backups and monitoring.

  

  

### 3. Documentation (1 day)

  

- [ ] Create API documentation.

  

- [ ] Create user guides for all three user roles (admin, expert, farmer).