# Aggressive Project Delivery Timeline

This document outlines a compressed timeline to deliver the project by July 5th, 2025. It groups tasks thematically to be worked on in parallel across all platforms.

- **Start Date:** June 25, 2025
- **Target Completion Date:** July 5, 2025

---

### **Week 1: Foundational Backend & User Systems (June 25 - June 29)**

#### **Day 1: Wednesday, June 25 - Core DB & Storage Setup**
- [ ] **DB Schema:** Apply all required schema changes (`push_token`, offline sync fields, subscription status).
- [ ] **Storage:** Configure all three Supabase Storage buckets (`avatars`, `expert-documents`, `farm-attachments`) with their respective access policies.

#### **Day 2: Thursday, June 26 - User Authentication**
- [ ] **Password Recovery:** Implement the full password recovery flow (frontend forms on web/mobile and backend logic).
- [ ] **Profile Management:**
    - [ ] **Web/Mobile:** Build the shared "My Profile" page.
    - [ ] **Backend:** Implement the avatar upload logic.

#### **Day 3: Friday, June 27 - Admin & Expert Management**
- [ ] **Admin UI:** Build the admin management page for creating/viewing other admins.
- [ ] **Expert Verification:**
    - [ ] **Admin:** Build the dashboard for reviewing and approving/rejecting expert applications.
    - [ ] **Expert:** Implement the document upload UI on the expert profile page.
- [ ] **User Suspension:** Implement the UI and backend logic for admins to suspend expert/farmer accounts.

#### **Day 4: Saturday, June 28 - Farmer Management & Consultation Setup**
- [ ] **Farmer Management:** Build the admin UI for viewing farmer profiles and their subscription status.
- [ ] **Consultation Packages (Expert):** Implement the UI and backend logic for experts to create, edit, and delete their consultation packages.
- [ ] **Consultation Requests (Farmer):** Build the mobile UI for farmers to submit a consultation request.

#### **Day 5: Sunday, June 29 - Consultation Flow**
- [ ] **Request Management (Expert):** Build the "Requests" page for experts to accept/reject consultations.
- [ ] **Status Tracking (Farmer):** Implement the UI for farmers to track the status of their requests.
- [ ] **Backend:** Implement the logic that creates a `consultations` record when an expert accepts a request.

---

### **Week 2: Core Functionality & Finalization (June 30 - July 5)**

#### **Day 6: Monday, June 30 - Payment Integration (Part 1)**
- [ ] **Backend:** Implement the Supabase Edge Functions to handle Paymob payment intent creation.
- [ ] **Frontend (Farmer):** Integrate the Paymob SDK and payment flow into the mobile app after a consultation request is submitted.
- [ ] **Database:** Ensure the `payments` table is correctly populated with `status: 'held'`.

#### **Day 7: Tuesday, July 1 - Payment Integration (Part 2)**
- [ ] **Backend:** Implement the Paymob webhook to handle successful payments.
- [ ] **Backend:** Create the Edge Function to handle the release of funds to the expert upon consultation completion.
- [ ] **Dispute Management (Admin):** Build the admin controls for managing disputes (refunding or forcing payout).

#### **Day 8: Wednesday, July 2 - Real-time Chat & Notifications**
- [ ] **Chat (Farmer & Expert):** Build the chat UI for both the mobile and web apps. Enable and integrate Supabase Realtime on the `consultation_messages` table.
- [ ] **Notifications:**
    - [ ] Implement the `send-notification` Edge Function.
    - [ ] Set up the database triggers to call the function on status changes.
    - [ ] Integrate the Expo push notification client on the mobile app.

#### **Day 9: Thursday, July 3 - Dashboards & Analytics**
- [ ] **Financial Dashboard (Admin):** Build the UI and connect the backend function for financial reporting.
- [ ] **Expert Dashboard:** Build the UI and connect the backend function for the expert's statistics.
- [ ] **Earnings History (Expert):** Build the UI and connect the backend function for the expert's earnings page.

#### **Day 10: Friday, July 4 - Testing & Optimization**
- [ ] **Testing:** Perform rapid end-to-end testing of all user flows.
- [ ] **Optimization:** Use `EXPLAIN ANALYZE` on critical PostgreSQL functions and add necessary indexes.
- [ ] **Bug Fixing:** Address any critical bugs identified during testing.

#### **Day 11: Saturday, July 5 - Finalization & Security**
- [ ] **RLS Implementation:** Implement and apply all Row-Level Security policies for `profiles`, `farms`, `consultations`, and `payments`.
- [ ] **Security Review:** Conduct a final review of all storage policies and function security.
- [ ] **Documentation:** Write concise API and user-level documentation.
- [ ] **Deployment:** Deploy the latest changes to Netlify and prepare the mobile app for submission.

---
### **Target Project Completion: July 5, 2025**