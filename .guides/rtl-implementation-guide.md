# RTL (Right-to-Left) Implementation Guide

## 📋 **Proper RTL Pattern (WORKING SOLUTION)**

Based on successful implementation in the AgriDustria web portal, here's the correct pattern to follow for RTL support in React/Next.js with Tailwind CSS.

## ✅ **Core Principles**

### **1. Root Container Setup**
Always set the `dir` attribute on the root container:

```tsx
<div dir={isRTL ? 'rtl' : 'ltr'}>
  {/* Your content */}
</div>
```

### **2. Text Alignment (CRITICAL PATTERN)**
```tsx
// ✅ CORRECT - Use this pattern for all text elements
<div className="text-left rtl:text-right">
  <h1>Your Title</h1>
</div>

// ❌ WRONG - Don't use conditional classes
<div className={`${isRTL ? 'text-right' : 'text-left'}`}>
```

### **3. Flex Direction for Icons + Text**
```tsx
// ✅ CORRECT - For icon + text combinations
<div className="flex items-center gap-2 text-left rtl:text-right">
  <Icon className="h-5 w-5" />
  <span>Text</span>
</div>

// For RTL icon reversal, add rtl:flex-row-reverse if needed
<div className="flex items-center gap-2 text-left rtl:text-right rtl:flex-row-reverse">
```

### **4. Layout Positioning**
```tsx
// ✅ CORRECT - For justify positioning
<div className="flex justify-end rtl:justify-start">
  <button>Action</button>
</div>

// For space between elements
<div className="flex justify-between rtl:flex-row-reverse">
```

## 🎯 **Working Examples**

### **Modal/Dialog Title (PROVEN WORKING)**
```tsx
// This is the exact pattern that works for modal titles
<DialogHeader>
  <div className="flex items-center gap-2 text-left rtl:text-right">
    <FileText className="h-5 w-5" />
    <DialogTitle className="text-xl font-semibold">{t("title")}</DialogTitle>
  </div>
  <DialogDescription className="text-left rtl:text-right">
    {t("description")}
  </DialogDescription>
</DialogHeader>
```

### **Card Title with Status Badge**
```tsx
<CardTitle className="flex items-center justify-between rtl:flex-row-reverse">
  <div className="flex items-center gap-2 text-left rtl:text-right">
    <User className="h-5 w-5" />
    <span>{t("title")}</span>
  </div>
  <Badge variant="default">
    {t("status")}
  </Badge>
</CardTitle>
```

### **Form Fields and Labels**
```tsx
<div>
  <label className="text-sm font-medium text-muted-foreground text-left rtl:text-right">
    {t("fieldLabel")}
  </label>
  <p className="text-sm text-left rtl:text-right">
    {fieldValue}
  </p>
</div>
```

### **User Profile with Avatar**
```tsx
<div className="flex items-center gap-3 text-left rtl:text-right">
  <Avatar>
    <AvatarFallback>JD</AvatarFallback>
  </Avatar>
  <div className="text-left rtl:text-right">
    <p className="font-medium">John Doe</p>
    <p className="text-sm text-muted-foreground"><EMAIL></p>
  </div>
</div>
```

### **Action Buttons**
```tsx
<div className="flex gap-2 pt-4 border-t justify-end rtl:justify-start">
  <Button variant="outline">{t("cancel")}</Button>
  <Button variant="default">{t("confirm")}</Button>
</div>
```

## 🚫 **Common Mistakes to Avoid**

### **1. Using Conditional Classes**
```tsx
// ❌ DON'T DO THIS
<div className={`${isRTL ? 'text-right' : 'text-left'}`}>
  
// ✅ DO THIS INSTEAD
<div className="text-left rtl:text-right">
```

### **2. Forgetting the dir Attribute**
```tsx
// ❌ Missing dir attribute
<Dialog>
  <DialogContent>
  
// ✅ Include dir attribute
<Dialog>
  <DialogContent dir={isRTL ? 'rtl' : 'ltr'}>
```

### **3. Overcomplicating Flex Layouts**
```tsx
// ❌ Too many conditional classes
<div className={`flex ${isRTL ? 'flex-row-reverse justify-start' : 'justify-end'}`}>

// ✅ Simple and clean
<div className="flex justify-end rtl:justify-start rtl:flex-row-reverse">
```

## 📝 **Implementation Checklist**

- [ ] Set `dir` attribute on root container
- [ ] Apply `text-left rtl:text-right` to all text elements
- [ ] Use `rtl:flex-row-reverse` for layout reversals
- [ ] Use `rtl:justify-start` / `rtl:justify-end` for positioning
- [ ] Test with actual Arabic text
- [ ] Verify icons are positioned correctly
- [ ] Check form field alignment
- [ ] Validate button positioning

## 🔍 **Testing Guidelines**

1. **Switch Language**: Test switching between Arabic and English
2. **Text Alignment**: Verify all text starts from the correct side
3. **Icon Positioning**: Check icons are on the correct side for each language
4. **Layout Flow**: Ensure natural reading flow for each language
5. **Interactive Elements**: Test buttons, forms, and navigation

## 📚 **Reference Implementation**

See the successful implementation in:
- `src/components/dashboard-header.tsx` (page titles)
- `src/app/dashboard/management/page.tsx` (card titles)
- `src/components/registration-details-modal.tsx` (modal titles)

This guide is based on the working RTL implementation in the AgriDustria web portal project.