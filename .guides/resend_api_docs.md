# Send Email

> Start sending emails through the Resend Email API.

export const ResendParamField = ({children, body, path, ...props}) => {
  const [lang, setLang] = useState(() => {
    return localStorage.getItem('code') || '"Node.js"';
  });
  useEffect(() => {
    const onStorage = event => {
      const key = event.detail.key;
      if (key === 'code') {
        setLang(event.detail.value);
      }
    };
    document.addEventListener('mintlify-localstorage', onStorage);
    return () => {
      document.removeEventListener('mintlify-localstorage', onStorage);
    };
  }, []);
  const toCamelCase = str => typeof str === 'string' ? str.replace(/[_-](\w)/g, (_, c) => c.toUpperCase()) : str;
  const resolvedBody = useMemo(() => {
    const value = JSON.parse(lang);
    return value === 'Node.js' ? toCamelCase(body) : body;
  }, [body, lang]);
  const resolvedPath = useMemo(() => {
    const value = JSON.parse(lang);
    return value === 'Node.js' ? toCamelCase(path) : path;
  }, [path, lang]);
  return <ParamField body={resolvedBody} path={resolvedPath} {...props}>
      {children}
    </ParamField>;
};

## Body Parameters

<ParamField body="from" type="string" required>
  Sender email address.

  To include a friendly name, use the format `"Your Name <<EMAIL>>"`.
</ParamField>

<ParamField body="to" type="string | string[]" required>
  Recipient email address. For multiple addresses, send as an array of strings.
  Max 50.
</ParamField>

<ParamField body="subject" type="string" required>
  Email subject.
</ParamField>

<ParamField body="bcc" type="string | string[]">
  Bcc recipient email address. For multiple addresses, send as an array of
  strings.
</ParamField>

<ParamField body="cc" type="string | string[]">
  Cc recipient email address. For multiple addresses, send as an array of
  strings.
</ParamField>

<ResendParamField body="scheduled_at" type="string">
  Schedule email to be sent later. The date should be in natural language (e.g.: `in 1 min`) or ISO 8601 format (e.g:
  `2024-08-05T11:52:01.858Z`).

  [See examples](/dashboard/emails/schedule-email)
</ResendParamField>

<ResendParamField body="reply_to" type="string | string[]">
  Reply-to email address. For multiple addresses, send as an array of strings.
</ResendParamField>

<ParamField body="html" type="string">
  The HTML version of the message.
</ParamField>

<ParamField body="text" type="string">
  The plain text version of the message.

  <Info>
    If not provided, the HTML will be used to generate a plain text version. You
    can opt out of this behavior by setting value to an empty string.
  </Info>
</ParamField>

<ParamField body="react" type="React.ReactNode">
  The React component used to write the message. *Only available in the Node.js
  SDK.*
</ParamField>

<ParamField body="headers" type="object">
  Custom headers to add to the email.
</ParamField>

<ParamField body="attachments" type="array">
  Filename and content of attachments (max 40MB per email, after Base64 encoding of the attachments).

  [See examples](/dashboard/emails/attachments)

  <Expandable defaultOpen="true" title="properties">
    <ParamField body="content" type="buffer | string">
      Content of an attached file, passed as a buffer or Base64 string.
    </ParamField>

    <ParamField body="filename" type="string">
      Name of attached file.
    </ParamField>

    <ParamField body="path" type="string">
      Path where the attachment file is hosted
    </ParamField>

    <ResendParamField body="content_type" type="string">
      Content type for the attachment, if not set will be derived from the filename property
    </ResendParamField>

    <ResendParamField body="content_id" type="string">
      You can embed images using the `content_id` parameter for the attachment. To show the image, you need to include the ID in the `src` attribute of the `img` tag (e.g., `<img src="cid:...">`) of your HTML. [Learn about inline images](/dashboard/emails/embed-inline-images).
    </ResendParamField>
  </Expandable>
</ParamField>

<ParamField body="tags" type="array">
  Custom data passed in key/value pairs.

  [See examples](/dashboard/emails/tags).

  <Expandable defaultOpen="true" title="properties">
    <ParamField body="name" type="string" required>
      The name of the email tag.

      It can only contain ASCII letters (a–z, A–Z), numbers (0–9), underscores (\_), or dashes (-).

      It can contain no more than 256 characters.
    </ParamField>

    <ParamField body="value" type="string" required>
      The value of the email tag.

      It can only contain ASCII letters (a–z, A–Z), numbers (0–9), underscores (\_), or dashes (-).

      It can contain no more than 256 characters.
    </ParamField>
  </Expandable>
</ParamField>

<ParamField body="template" type="object">
  <Snippet file="templates-beta-banner.mdx" />

  To send using a template, provide a `template` object with:

  * `id`: id of the published template
  * `variables`: array of variable objects (if applicable)

  <Info>
    If a `template` is provided, you cannot send `html`, `text`, or `react` in the payload, otherwise the API will return a validation error.

    When sending a template, the payload for `from`, `subject`, and `reply_to` take precedence over the template's defaults for these fields. If the template does not provide a default value for these fields, you must provide them in the payload.
  </Info>
</ParamField>

<ParamField body="id" type="string" required>
  The id of the published email template. Required if `template` is provided. Only published templates can be used when sending emails.

  See the [errors reference](/api-reference/errors) for more details.
</ParamField>

<ParamField body="variables" type="object">
  Template variables object with key/value pairs.

  ```ts
  variables: {
  	CTA: 'Sign up now',
  	CTA_LINK: 'https://example.com/signup'
  }
  ```

  When sending the template, the HTML will be parsed. If all the variables used in the template were provided, the email will be sent. If not, the call will throw a validation error.

  See the [errors reference](/api-reference/errors) for more details.

  [See examples](/dashboard/templates/introduction).

  <Expandable defaultOpen="true" title="properties">
    <ParamField body="key" type="string" required>
      The key of the variable.

      May only contain ASCII letters (a–z, A–Z), numbers (0–9), and underscores (\_).

      It can contain no more than 50 characters.
    </ParamField>

    <ParamField body="value" value="string | number | boolean | object | list" required>
      The value of the variable.

      Observe these technical limitations:

      * `string`: maximum length of 50 characters
      * `number`: not greater than 2^53 - 1
      * `boolean`: `true` or `false`
      * `object`: maximum length of 50 keys
      * `list`: maximum length of 50 indexes
    </ParamField>
  </Expandable>
</ParamField>

## Headers

<ParamField header="Idempotency-Key" type="string">
  Add an idempotency key to prevent duplicated emails.

  * Should be **unique per API request**
  * Idempotency keys expire after **24 hours**
  * Have a maximum length of **256 characters**

  [Learn more](/dashboard/emails/idempotency-keys)
</ParamField>

<RequestExample>
  ```ts Node.js
  import { Resend } from 'resend';

  const resend = new Resend('re_xxxxxxxxx');

  const { data, error } = await resend.emails.send({
    from: 'Acme <<EMAIL>>',
    to: ['<EMAIL>'],
    subject: 'hello world',
    html: '<p>it works!</p>',
    replyTo: '<EMAIL>',
  });
  ```

  ```php PHP
  $resend = Resend::client('re_xxxxxxxxx');

  $resend->emails->send([
    'from' => 'Acme <<EMAIL>>',
    'to' => ['<EMAIL>'],
    'subject' => 'hello world',
    'html' => '<p>it works!</p>',
    'reply_to': '<EMAIL>'
  ]);
  ```

  ```python Python
  import resend

  resend.api_key = "re_xxxxxxxxx"

  params: resend.Emails.SendParams = {
    "from": "Acme <<EMAIL>>",
    "to": ["<EMAIL>"],
    "subject": "hello world",
    "html": "<p>it works!</p>",
    "reply_to": "<EMAIL>"
  }

  email = resend.Emails.send(params)
  print(email)
  ```

  ```rb Ruby
  require "resend"

  Resend.api_key = "re_xxxxxxxxx"

  params = {
    "from": "Acme <<EMAIL>>",
    "to": ["<EMAIL>"],
    "subject": "hello world",
    "html": "<p>it works!</p>",
    "reply_to": "<EMAIL>"
  }

  sent = Resend::Emails.send(params)
  puts sent
  ```

  ```go Go
  import (
  	"fmt"

  	"github.com/resend/resend-go/v2"
  )

  func main() {
    ctx := context.TODO()
    client := resend.NewClient("re_xxxxxxxxx")

    params := &resend.SendEmailRequest{
        From:        "Acme <<EMAIL>>",
        To:          []string{"<EMAIL>"},
        Subject:     "hello world",
        Html:        "<p>it works!</p>",
        ReplyTo:     "<EMAIL>"
    }

    sent, err := client.Emails.SendWithContext(ctx, params)

    if err != nil {
      panic(err)
    }
    fmt.Println(sent.Id)
  }
  ```

  ```rust Rust
  use resend_rs::types::{CreateEmailBaseOptions};
  use resend_rs::{Resend, Result};

  #[tokio::main]
  async fn main() -> Result<()> {
    let resend = Resend::new("re_xxxxxxxxx");

    let from = "Acme <<EMAIL>>";
    let to = ["<EMAIL>"];
    let subject = "hello world";
    let html = "<p>it works!</p>";
    let reply_to = "<EMAIL>";

    let email = CreateEmailBaseOptions::new(from, to, subject)
      .with_html(html);

    let _email = resend.emails.send(email).await?;

    Ok(())
  }
  ```

  ```java Java
  import com.resend.*;

  public class Main {
      public static void main(String[] args) {
          Resend resend = new Resend("re_xxxxxxxxx");

          CreateEmailOptions params = CreateEmailOptions.builder()
                  .from("Acme <<EMAIL>>")
                  .to("<EMAIL>")
                  .subject("hello world")
                  .html("<p>it works!</p>")
                  .replyTo("<EMAIL>")
                  .build();

          CreateEmailResponse data = resend.emails().send(params);
      }
  }
  ```

  ```csharp .NET
  using Resend;

  IResend resend = ResendClient.Create( "re_xxxxxxxxx" ); // Or from DI

  var resp = await resend.EmailSendAsync( new EmailMessage()
  {
      From = "Acme <<EMAIL>>",
      To = "<EMAIL>",
      Subject = "hello world",
      HtmlBody = "<p>it works!</p>",
      ReplyTo = "<EMAIL>",
  } );
  Console.WriteLine( "Email Id={0}", resp.Content );
  ```

  ```bash cURL
  curl -X POST 'https://api.resend.com/emails' \
       -H 'Authorization: Bearer re_xxxxxxxxx' \
       -H 'Content-Type: application/json' \
       -d $'{
    "from": "Acme <<EMAIL>>",
    "to": ["<EMAIL>"],
    "subject": "hello world",
    "html": "<p>it works!</p>",
    "reply_to": "<EMAIL>"
  }'
  ```
</RequestExample>

<ResponseExample>
  ```json Response
  {
    "id": "49a3999c-0ce1-4ea6-ab68-afcd6dc2e794"
  }
  ```
</ResponseExample>
