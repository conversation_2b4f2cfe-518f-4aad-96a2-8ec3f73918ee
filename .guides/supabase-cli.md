# Supabase CLI — Quick Run Guide

## Prerequisites
- Docker Engine installed
- Docker Compose v2 available as `docker compose`
  - Tip: we installed the Compose plugin at `~/.docker/cli-plugins/docker-compose`

## Install the CLI
- Global (system-wide):
  ```bash
  npm i -g supabase
  supabase --version
  ```
- Project-local (already set up here):
  ```bash
  npx supabase --version
  ```

## Common commands
- Initialize in this project:
  ```bash
  npx supabase init
  ```
- Authenticate and link to your project:
  ```bash
  npx supabase login
  npx supabase projects list
  npx supabase link --project-ref <PROJECT_ID>
  ```
- Start/stop local stack (requires Docker + Compose):
  ```bash
  npx supabase start
  npx supabase stop
  ```
- Check status:
  ```bash
  npx supabase status
  ```
- Edge Functions (examples):
  ```bash
  npx supabase functions new hello-world
  npx supabase functions serve hello-world
  ```
- Database dump (remote, IPv4 session pooler)
  - Direct command with your project details (schema only by default):
    ```bash
    npx supabase db dump \
      --db-url "postgresql://postgres.jrhbvcjwxvyrxrmgjfbu:<EMAIL>:5432/postgres?sslmode=require" \
      -f supabase/schemas/schema.sql
    ```
  - Using an environment variable (recommended):
    ```bash
    export DATABASE_URL="postgresql://postgres.jrhbvcjwxvyrxrmgjfbu:<EMAIL>:5432/postgres?sslmode=require"
    npx supabase db dump --db-url "$DATABASE_URL" -f supabase/schemas/schema.sql
    ```
  - Alternative IPv4 transaction pooler (stateless/short connections):
    ```bash
    export DATABASE_URL="postgresql://postgres.jrhbvcjwxvyrxrmgjfbu:<EMAIL>:6543/postgres?sslmode=require"
    npx supabase db dump --db-url "$DATABASE_URL" -f supabase/schemas/schema.sql
    ```
  - Notes:
    - The command above creates/overwrites `supabase/schemas/schema.sql`.
    - For a data dump as well, add pg_dump flags, for example: `--data-only` (see `supabase db dump --help`).
    - Keep credentials out of version control. Prefer exporting `DATABASE_URL` in your shell instead of committing it.

## Notes
- Prefer `npx supabase` inside this repo to use the project-local CLI version.
- For remote DB ops (like `db dump`), Docker is not required. For local services (`start`, `stop`), Docker + Compose is required.
