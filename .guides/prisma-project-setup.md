Supabase DATABASE credentials:
URI [Direct connection]: postgresql://postgres:[YOUR-PASSWORD]@db.jrhbvcjwxvyrxrmgjfbu.supabase.co:5432/postgres
URI [Session pooler-IP4 compatible]: postgresql://postgres.jrhbvcjwxvyrxrmgjfbu:[YOUR-PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres
Password: AhmadAgriPro
# 🚀 Prisma Setup for Your Agri-Industry Connect Project

## Quick Setup Steps

### 1. **Get Your Supabase Database Password**

You need your Supabase database password to connect Prisma. Here's how to get it:

#### Option A: From Supabase Dashboard
1. Go to your Supabase project: https://supabase.com/dashboard/projects
2. Select your project (`jrhbvcjwxvyrxrmgjfbu`)
3. Go to **Settings** → **Database**
4. Look for **Connection string** section
5. Copy the password from the connection string

#### Option B: Reset Database Password
1. Go to **Settings** → **Database** 
2. Click **Reset database password**
3. Set a new password (save it securely!)

### 2. **Update Your .env File**

Replace `[YOUR-DB-PASSWORD]` in your `.env` file with your actual password:

```env
# Replace [YOUR-DB-PASSWORD] with your actual Supabase database password
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
DIRECT_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

### 3. **Sync with Your Existing Database**

Since you already have a Supabase database with data, run these commands:

```bash
# Pull the current database schema
npx prisma db pull

# Generate the Prisma client
npx prisma generate

# Test the setup
node test-prisma-setup.js
```

### 4. **Test the Setup**

Run the test script I created:

```bash
node test-prisma-setup.js
```

This will verify:
- ✅ Database connection
- ✅ Schema synchronization  
- ✅ Basic queries
- ✅ Relationships
- ✅ TypeScript types

## What's Already Configured

### ✅ **Prisma Schema** (`prisma/schema.prisma`)
- Mapped to your existing Supabase tables
- Added new models for factory support
- Configured for offline sync capabilities
- Proper relationships between all models

### ✅ **Custom Hooks** (`hooks/usePrisma.ts`)
Ready-to-use React hooks:
```typescript
// Get user profile with related data
const { profile, loading } = useProfile(userId)

// Get farms for a farmer
const { farms } = useFarms(farmerId)

// Get available experts
const { experts } = useExperts({ isAvailable: true })

// Get consultations with filtering
const { consultations } = useConsultations({ status: 'active' })

// Create/update operations
const { createFarm, createConsultation } = usePrismaMutations()
```

### ✅ **Prisma Client** (`lib/prisma.ts`)
- Singleton pattern for React Native
- Full TypeScript support
- Exported types for all models

## Quick Start Examples

### Example 1: Get All Farms
```typescript
import { useFarms } from '@/hooks/usePrisma'

function FarmsScreen() {
  const { farms, loading, error } = useFarms()
  
  if (loading) return <Text>Loading farms...</Text>
  if (error) return <Text>Error: {error}</Text>
  
  return (
    <FlatList
      data={farms}
      renderItem={({ item }) => (
        <View>
          <Text>{item.name}</Text>
          <Text>{item.location}</Text>
          <Text>Status: {item.approvalStatus}</Text>
        </View>
      )}
    />
  )
}
```

### Example 2: Create a New Consultation
```typescript
import { usePrismaMutations } from '@/hooks/usePrisma'

function CreateConsultation() {
  const { createConsultation, loading } = usePrismaMutations()
  
  const handleSubmit = async (formData) => {
    try {
      const consultation = await createConsultation({
        title: formData.title,
        description: formData.description,
        farmId: formData.farmId,
        consultationType: 'agriculture',
        status: 'pending'
      })
      
      console.log('Consultation created:', consultation.id)
      // Navigate to consultation details
    } catch (error) {
      console.error('Failed to create consultation:', error)
    }
  }
  
  return (
    <View>
      {/* Your form UI here */}
      <Button 
        title="Create Consultation" 
        onPress={handleSubmit}
        disabled={loading}
      />
    </View>
  )
}
```

### Example 3: Expert Search with Filters
```typescript
import { useExperts } from '@/hooks/usePrisma'

function ExpertSearch() {
  const [filters, setFilters] = useState({
    isAvailable: true,
    isVerified: true,
    specializationId: null
  })
  
  const { experts, loading } = useExperts(filters)
  
  return (
    <View>
      <Text>Found {experts.length} experts</Text>
      {experts.map(expert => (
        <View key={expert.id}>
          <Text>{expert.firstName} {expert.lastName}</Text>
          <Text>Rating: {expert.expertProfile?.rating || 'N/A'}</Text>
          <Text>Specialization: {expert.expertProfile?.specialization?.name}</Text>
        </View>
      ))}
    </View>
  )
}
```

## Troubleshooting

### Common Issues

#### 1. **Connection Error**
```
Error: Can't reach database server
```
**Solution:** Check your database password in the `.env` file

#### 2. **Schema Mismatch**
```
Error: Unknown field 'firstName'
```
**Solution:** 
```bash
npx prisma db pull
npx prisma generate
```

#### 3. **Type Errors**
```
Property 'expertProfile' does not exist
```
**Solution:**
```bash
npx prisma generate
# Restart your development server
```

## Next Steps After Setup

1. **Test Basic Operations**
   - Try fetching data with the hooks
   - Create a simple form to test mutations

2. **Implement Key Features**
   - Registration approval workflow
   - Factory support for industrial users
   - Offline synchronization

3. **Optimize Performance**
   - Add database indexes
   - Implement caching
   - Use pagination for large datasets

## Need Help?

- Check the complete beginner's guide: `.guides/prisma-beginner-guide.md`
- Run the test script: `node test-prisma-setup.js`
- Check Prisma docs: https://www.prisma.io/docs

---

**Ready to start coding with Prisma!** 🎯