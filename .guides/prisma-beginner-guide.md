# 🚀 Complete Beginner's Guide to Prisma

## Table of Contents
1. [What is Prisma?](#what-is-prisma)
2. [Why Use Prisma?](#why-use-prisma)
3. [Prisma vs Other ORMs](#prisma-vs-other-orms)
4. [Core Concepts](#core-concepts)
5. [Installation & Setup](#installation--setup)
6. [Project-Specific Setup](#project-specific-setup)
7. [Basic Operations](#basic-operations)
8. [Advanced Features](#advanced-features)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

---

## What is Prisma?

**Prisma** is a modern **Object-Relational Mapping (ORM)** tool that makes database access easy and type-safe for developers. Think of it as a bridge between your JavaScript/TypeScript code and your database.

### Simple Analogy
Imagine you're ordering food at a restaurant:
- **Without Prisma**: You have to speak directly to the kitchen in "kitchen language" (SQL)
- **With Prisma**: You have a waiter (Prisma) who speaks both your language (JavaScript) and kitchen language (SQL)

### What Prisma Does
```typescript
// Instead of writing raw SQL like this:
const result = await db.query(`
  SELECT u.*, p.bio, p.rating 
  FROM users u 
  JOIN expert_profiles p ON u.id = p.user_id 
  WHERE u.is_active = true
`)

// You write simple, type-safe code like this:
const experts = await prisma.user.findMany({
  where: { isActive: true },
  include: { expertProfile: true }
})
```

---

## Why Use Prisma?

### 🎯 **Type Safety**
```typescript
// Prisma knows your database structure
const user = await prisma.user.findUnique({
  where: { id: "123" }
})

// TypeScript knows exactly what 'user' contains
console.log(user.firstName) // ✅ Auto-completion works!
console.log(user.invalidField) // ❌ TypeScript error - field doesn't exist
```

### 🚀 **Developer Experience**
- **Auto-completion**: Your IDE knows all your database fields
- **Error Prevention**: Catch database errors at compile time
- **Easy Relationships**: Join tables with simple syntax
- **Migration Management**: Track database changes over time

### 🔧 **Powerful Features**
- **Query Optimization**: Automatically generates efficient SQL
- **Real-time Type Generation**: Schema changes update types instantly
- **Database Introspection**: Generate schema from existing database
- **Multiple Database Support**: PostgreSQL, MySQL, SQLite, MongoDB, etc.

---

## Prisma vs Other ORMs

| Feature | Prisma | Traditional ORMs | Raw SQL |
|---------|--------|------------------|---------|
| Type Safety | ✅ Full | ⚠️ Partial | ❌ None |
| Auto-completion | ✅ Perfect | ⚠️ Limited | ❌ None |
| Learning Curve | 🟢 Easy | 🟡 Medium | 🔴 Hard |
| Performance | ✅ Optimized | ⚠️ Varies | ✅ Full Control |
| Migrations | ✅ Built-in | ⚠️ Varies | ❌ Manual |

---

## Core Concepts

### 1. **Schema File** (`prisma/schema.prisma`)
This is your database blueprint written in Prisma's schema language:

```prisma
model User {
  id        String   @id @default(uuid())
  email     String   @unique
  name      String
  posts     Post[]   // One-to-many relationship
  createdAt DateTime @default(now())
}

model Post {
  id       String @id @default(uuid())
  title    String
  content  String
  author   User   @relation(fields: [authorId], references: [id])
  authorId String
}
```

### 2. **Prisma Client**
Generated code that provides type-safe database access:

```typescript
import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()

// Now you can use prisma.user, prisma.post, etc.
```

### 3. **Models**
Represent your database tables:
- `model User` = `users` table
- `model ExpertProfile` = `expert_profiles` table

### 4. **Relations**
How tables connect to each other:
- **One-to-One**: User ↔ Profile
- **One-to-Many**: User → Posts
- **Many-to-Many**: Users ↔ Roles

### 5. **Queries**
Operations you can perform:
- `findMany()` - Get multiple records
- `findUnique()` - Get one record
- `create()` - Add new record
- `update()` - Modify existing record
- `delete()` - Remove record

---

## Installation & Setup

### Step 1: Install Prisma
```bash
npm install prisma @prisma/client
```

### Step 2: Initialize Prisma
```bash
npx prisma init
```
This creates:
- `prisma/schema.prisma` - Your database schema
- `.env` - Environment variables

### Step 3: Configure Database Connection for Supabase

#### 🔧 **For Supabase Projects (Critical Setup)**

**Option A: Using Pooler URL (Recommended for Production)**
```env
# Get these from your Supabase project settings
DATABASE_URL="postgresql://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres?sslmode=require"
DIRECT_URL="postgresql://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres?sslmode=require"
```

**Option B: Using Direct Connection**
```env
DATABASE_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres?sslmode=require"
DIRECT_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres?sslmode=require"
```

**How to Get Your Connection Details:**
1. Go to your Supabase project dashboard
2. Navigate to **Settings** → **Database**
3. Find **Connection string** section
4. Copy the **URI** format
5. Replace `[YOUR-PASSWORD]` with your actual database password

### Step 4: Configure Schema for Multi-Schema Databases

For Supabase (which uses `auth` and `public` schemas):

```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["public", "auth"]  // 🔥 Critical for Supabase
}

// Example model
model User {
  id    String @id @default(uuid())
  email String @unique
  name  String
  
  @@schema("public")  // 🔥 Required when using multiple schemas
}
```

### Step 5: Pull Existing Database Schema (For Existing Projects)

**If you have an existing Supabase database:**
```bash
# Pull the current database structure
npx prisma db pull

# Generate the Prisma client
npx prisma generate
```

**If starting fresh:**
```bash
# Push your schema to create tables
npx prisma db push

# Generate the Prisma client
npx prisma generate
```

### Step 6: Test Connection
```bash
# Create a test file
echo 'const { PrismaClient } = require("@prisma/client")
const prisma = new PrismaClient()
async function test() {
  try {
    await prisma.$connect()
    console.log("✅ Prisma connected successfully!")
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log("✅ Query test passed:", result)
  } catch (error) {
    console.error("❌ Connection failed:", error.message)
  } finally {
    await prisma.$disconnect()
  }
}
test()' > test-prisma-connection.js

# Run the test
node test-prisma-connection.js
```

---

## Project-Specific Setup

### Our Agri-Industry Connect Project

#### 🎯 **What We Have**
- **Existing Supabase Database** with tables for users, farms, consultations
- **React Native Mobile App** that needs to access this data
- **Complex Relationships** between farmers, experts, farms, and consultations

#### 🔧 **What We've Set Up**

##### 1. **Database Schema** (`prisma/schema.prisma`)
We've mapped all your existing Supabase tables:

```prisma
// Main user profiles
model Profile {
  id                   String   @id @default(dbgenerated("uuid_generate_v4()"))
  email                String
  firstName            String   @map("first_name")
  lastName             String   @map("last_name")
  // ... other fields
  
  // Relations to different user types
  expertProfile ExpertProfile?
  farmerProfile FarmerProfile?
  workerProfile WorkerProfile?
  
  @@map("profiles") // Maps to your existing 'profiles' table
}

// Expert-specific data
model ExpertProfile {
  id               String  @id @default(dbgenerated("uuid_generate_v4()"))
  profileId        String  @unique @map("profile_id")
  specializationId String? @map("specialization_id")
  hourlyRate       Decimal? @map("hourly_rate")
  bio              String?
  isVerified       Boolean? @default(false)
  rating           Decimal?
  
  // Relations
  profile        Profile        @relation(fields: [profileId], references: [id])
  specialization Specialization? @relation(fields: [specializationId], references: [id])
  consultations  Consultation[]
  
  @@map("expert_profiles")
}

// Farms with approval system
model Farm {
  id            String         @id @default(dbgenerated("uuid_generate_v4()"))
  farmerId      String         @map("farmer_id")
  name          String
  location      String?
  area          Decimal?
  soilType      String?        @map("soil_type")
  crops         Json?          // Flexible JSON field for crop data
  
  // NEW: Approval workflow fields
  approvalStatus String?       @default("pending") @map("approval_status")
  adminNotes     String?       @map("admin_notes")
  reviewedBy     String?       @map("reviewed_by")
  reviewedAt     DateTime?     @map("reviewed_at")
  
  // Relations
  farmer        FarmerProfile @relation(fields: [farmerId], references: [id])
  consultations Consultation[]
  
  @@map("farms")
}

// NEW: Factory support for industrial users
model Factory {
  id                     String         @id @default(dbgenerated("uuid_generate_v4()"))
  workerId               String         @map("worker_id")
  name                   String         @map("factory_name")
  industryType           String         @map("industry_type")
  dailyProductionVolume  Decimal?       @map("daily_production_volume")
  machineryList          Json?          @map("machinery_list")
  
  // Relations
  worker        WorkerProfile @relation(fields: [workerId], references: [id])
  consultations Consultation[]
  
  @@map("factory_profiles")
}

// Consultations supporting both farms and factories
model Consultation {
  id                String   @id @default(dbgenerated("uuid_generate_v4()"))
  title             String?
  description       String?
  status            String?  @default("pending")
  expertId          String?  @map("expert_id")
  farmId            String?  @map("farm_id")
  factoryId         String?  @map("factory_id")        // NEW: Factory support
  consultationType  String?  @default("agriculture") @map("consultation_type") // NEW: Type field
  
  // Relations
  expert   ExpertProfile? @relation(fields: [expertId], references: [id])
  farm     Farm?          @relation(fields: [farmId], references: [id])
  factory  Factory?       @relation(fields: [factoryId], references: [id])
  messages ConsultationMessage[]
  
  @@map("consultations")
}
```

##### 2. **Environment Configuration**
```env
# Your existing Supabase config
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url

# NEW: Prisma database URLs
DATABASE_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres"
DIRECT_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres"
```

##### 3. **Prisma Client Setup** (`lib/prisma.ts`)
```typescript
import { PrismaClient } from '@prisma/client'

// Singleton pattern for React Native
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// Export types for TypeScript
export type {
  Profile,
  ExpertProfile,
  Farm,
  Factory,
  Consultation,
  // ... all other types
} from '@prisma/client'
```

##### 4. **Custom React Hooks** (`hooks/usePrisma.ts`)
We've created hooks that make database operations super easy:

```typescript
// Get user profile with all related data
const { profile, loading, error } = useProfile(userId)

// Get farms for a specific farmer
const { farms, loading } = useFarms(farmerId)

// Get available experts with filtering
const { experts } = useExperts({
  isAvailable: true,
  specializationId: "crop-diseases"
})

// Get consultations with complex filtering
const { consultations } = useConsultations({
  status: 'active',
  farmId: currentFarmId
})

// Create new records
const { createFarm, createConsultation } = usePrismaMutations()
```

---

## Basic Operations

### 1. **Reading Data**

#### Find One Record
```typescript
// Get a specific user
const user = await prisma.profile.findUnique({
  where: { id: "user-123" }
})

// Get user with expert profile
const userWithExpert = await prisma.profile.findUnique({
  where: { id: "user-123" },
  include: { expertProfile: true }
})
```

#### Find Multiple Records
```typescript
// Get all active experts
const experts = await prisma.profile.findMany({
  where: {
    expertProfile: {
      isVerified: true,
      isAvailable: true
    }
  },
  include: {
    expertProfile: {
      include: {
        specialization: true
      }
    }
  }
})

// Get farms pending approval
const pendingFarms = await prisma.farm.findMany({
  where: { approvalStatus: "pending" },
  include: {
    farmer: {
      include: {
        profile: true
      }
    }
  }
})
```

### 2. **Creating Data**

```typescript
// Create a new farm
const newFarm = await prisma.farm.create({
  data: {
    name: "Green Valley Farm",
    location: "Riyadh, Saudi Arabia",
    area: 150.5,
    soilType: "Clay Loam",
    crops: ["wheat", "tomatoes", "dates"],
    farmerId: "farmer-123",
    approvalStatus: "pending"
  }
})

// Create a consultation
const consultation = await prisma.consultation.create({
  data: {
    title: "Tomato Disease Problem",
    description: "Yellow spots appearing on leaves",
    farmId: "farm-123",
    consultationType: "agriculture",
    status: "pending"
  },
  include: {
    farm: {
      include: {
        farmer: {
          include: {
            profile: true
          }
        }
      }
    }
  }
})
```

### 3. **Updating Data**

```typescript
// Update farm approval status
const approvedFarm = await prisma.farm.update({
  where: { id: "farm-123" },
  data: {
    approvalStatus: "approved",
    reviewedBy: "admin-456",
    reviewedAt: new Date(),
    adminNotes: "All documentation verified"
  }
})

// Update consultation status
const updatedConsultation = await prisma.consultation.update({
  where: { id: "consultation-123" },
  data: {
    status: "in_progress",
    expertId: "expert-789"
  }
})
```

### 4. **Deleting Data**

```typescript
// Delete a farm
await prisma.farm.delete({
  where: { id: "farm-123" }
})

// Delete multiple records
await prisma.consultation.deleteMany({
  where: {
    status: "cancelled",
    createdAt: {
      lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Older than 30 days
    }
  }
})
```

---

## Advanced Features

### 1. **Complex Filtering**

```typescript
// Find consultations with complex criteria
const consultations = await prisma.consultation.findMany({
  where: {
    OR: [
      { farmId: { not: null } },
      { factoryId: { not: null } }
    ],
    AND: [
      { status: { in: ["pending", "active"] } },
      {
        expert: {
          isVerified: true,
          rating: { gte: 4.0 }
        }
      }
    ]
  },
  include: {
    expert: {
      include: {
        profile: true,
        specialization: true
      }
    },
    farm: true,
    factory: true,
    messages: {
      where: { isRead: false },
      orderBy: { createdAt: 'desc' }
    }
  },
  orderBy: [
    { priority: 'desc' },
    { createdAt: 'desc' }
  ]
})
```

### 2. **Aggregations**

```typescript
// Count consultations by status
const consultationStats = await prisma.consultation.groupBy({
  by: ['status'],
  _count: {
    id: true
  }
})

// Average expert rating
const avgRating = await prisma.expertProfile.aggregate({
  _avg: {
    rating: true
  },
  where: {
    isVerified: true
  }
})

// Farm statistics
const farmStats = await prisma.farm.aggregate({
  _count: {
    id: true
  },
  _sum: {
    area: true
  },
  _avg: {
    area: true
  },
  where: {
    approvalStatus: "approved"
  }
})
```

### 3. **Transactions**

```typescript
// Create consultation and send notification in one transaction
const result = await prisma.$transaction(async (tx) => {
  // Create consultation
  const consultation = await tx.consultation.create({
    data: {
      title: "Urgent Farm Issue",
      farmId: "farm-123",
      expertId: "expert-456"
    }
  })
  
  // Create notification
  await tx.notification.create({
    data: {
      userId: "expert-456",
      message: `New consultation: ${consultation.title}`,
      type: "consultation_assigned"
    }
  })
  
  return consultation
})
```

### 4. **Raw Queries** (when needed)

```typescript
// Sometimes you need raw SQL for complex operations
const result = await prisma.$queryRaw`
  SELECT 
    e.id,
    e.rating,
    COUNT(c.id) as consultation_count,
    AVG(c.rating) as avg_consultation_rating
  FROM expert_profiles e
  LEFT JOIN consultations c ON e.id = c.expert_id
  WHERE e.is_verified = true
  GROUP BY e.id, e.rating
  HAVING COUNT(c.id) > 5
  ORDER BY avg_consultation_rating DESC
`
```

---

## Best Practices

### 1. **Use Include Wisely**
```typescript
// ❌ Don't over-include (performance impact)
const user = await prisma.profile.findUnique({
  where: { id: userId },
  include: {
    expertProfile: {
      include: {
        consultations: {
          include: {
            messages: true,
            farm: {
              include: {
                farmer: {
                  include: {
                    profile: true
                  }
                }
              }
            }
          }
        }
      }
    }
  }
})

// ✅ Include only what you need
const user = await prisma.profile.findUnique({
  where: { id: userId },
  include: {
    expertProfile: {
      select: {
        rating: true,
        isVerified: true,
        specialization: {
          select: {
            name: true
          }
        }
      }
    }
  }
})
```

### 2. **Use Select for Performance**
```typescript
// ✅ Select only needed fields
const users = await prisma.profile.findMany({
  select: {
    id: true,
    firstName: true,
    lastName: true,
    expertProfile: {
      select: {
        rating: true,
        isVerified: true
      }
    }
  }
})
```

### 3. **Handle Errors Properly**
```typescript
try {
  const farm = await prisma.farm.create({
    data: farmData
  })
  return farm
} catch (error) {
  if (error.code === 'P2002') {
    // Unique constraint violation
    throw new Error('Farm with this name already exists')
  }
  if (error.code === 'P2003') {
    // Foreign key constraint violation
    throw new Error('Invalid farmer ID')
  }
  throw error
}
```

### 4. **Use Pagination**
```typescript
// ✅ Paginate large datasets
const consultations = await prisma.consultation.findMany({
  skip: page * pageSize,
  take: pageSize,
  orderBy: { createdAt: 'desc' }
})

// ✅ Or use cursor-based pagination
const consultations = await prisma.consultation.findMany({
  take: 20,
  cursor: lastConsultationId ? { id: lastConsultationId } : undefined,
  orderBy: { createdAt: 'desc' }
})
```

---

## Troubleshooting & Linking Prisma to Supabase

### 🔧 **Step-by-Step Linking Guide for Web Portal Projects**

This section provides comprehensive troubleshooting for linking Prisma to Supabase databases, especially for web portal projects.

#### **Phase 1: Initial Setup & Connection**

##### 1. **Install Prisma in Your Web Project**
```bash
# In your web portal project directory
npm install prisma @prisma/client

# Initialize Prisma
npx prisma init
```

##### 2. **Configure Environment Variables**
Create/update your `.env` file:

```env
# Supabase Connection URLs
# Option A: Pooler URL (Recommended for Production)
DATABASE_URL="postgresql://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres?sslmode=require"
DIRECT_URL="postgresql://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres?sslmode=require"

# Option B: Direct Connection (Development)
# DATABASE_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres?sslmode=require"
# DIRECT_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres?sslmode=require"

# Your existing Supabase config (keep these)
NEXT_PUBLIC_SUPABASE_URL=https://[PROJECT-REF].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

**How to Get Connection Details:**
1. Go to your Supabase project: `https://supabase.com/dashboard/projects`
2. Select your project
3. Navigate to **Settings** → **Database**
4. Find **Connection string** section
5. Copy the **URI** format and replace placeholders

##### 3. **Configure Prisma Schema for Supabase**
Edit `prisma/schema.prisma`:

```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["public", "auth"]  // 🔥 Critical for Supabase
}

// Models will be auto-generated in next step
```

#### **Phase 2: Schema Introspection & Generation**

##### 4. **Pull Existing Database Schema**
```bash
# Pull the current database structure
npx prisma db pull

# If you get schema errors, use force flag
npx prisma db pull --force

# Generate the Prisma client
npx prisma generate
```

##### 5. **Test the Connection**
Create a test file `test-prisma.js`:

```javascript
const { PrismaClient } = require('@prisma/client')

async function testConnection() {
  const prisma = new PrismaClient({
    log: ['query', 'info', 'warn', 'error'],
  })
  
  try {
    console.log('🔧 Testing Prisma connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Connected to database')
    
    // Test query
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ Query test passed:', result)
    
    // Test table access
    const tableCount = await prisma.$queryRaw`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `
    console.log('✅ Found tables:', tableCount[0].count)
    
    console.log('🎉 Prisma setup successful!')
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message)
    console.error('💡 Check troubleshooting section below')
  } finally {
    await prisma.$disconnect()
  }
}

testConnection()
```

Run the test:
```bash
node test-prisma.js
```

### 🚨 **Common Issues & Solutions**

#### **Issue 1: Connection Errors**

**Error: Can't reach database server**
```
Error: Can't reach database server at `db.xxx.supabase.co:5432`
```

**Solutions (Try in Order):**

1. **Use Pooler URL (Most Reliable):**
   ```env
   DATABASE_URL="postgresql://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres?sslmode=require"
   ```

2. **Check Password & Project Reference:**
   ```bash
   # Verify your project reference from Supabase URL
   # https://[PROJECT-REF].supabase.co
   
   # Reset database password if needed:
   # Supabase Dashboard → Settings → Database → Reset password
   ```

3. **Try Different Connection Formats:**
   ```env
   # Transaction pooler (for short connections)
   DATABASE_URL="postgresql://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:6543/postgres?pgbouncer=true&sslmode=require"
   
   # IPv4 session pooler
   DATABASE_URL="postgresql://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres?sslmode=require"
   ```

4. **Network Troubleshooting:**
   ```bash
   # Test connectivity
   ping aws-0-eu-central-1.pooler.supabase.com
   
   # Test port access
   telnet aws-0-eu-central-1.pooler.supabase.com 5432
   
   # Check if corporate firewall is blocking
   # Try from different network or disable VPN
   ```

#### **Issue 2: Schema Configuration Errors**

**Error: Cross schema references not supported**
```
Error: Cross schema references are only allowed when the target schema is listed in the schemas property
```

**Solution:**
```prisma
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["public", "auth"]  // 🔥 Add all referenced schemas
}
```

**Error: Model missing @@schema attribute**
```
Error: This model is missing an `@@schema` attribute
```

**Solution:**
```bash
# Use --force to regenerate schema completely
npx prisma db pull --force
npx prisma generate
```

#### **Issue 3: Authentication Errors**

**Error: Password authentication failed**
```
Error: password authentication failed for user "postgres"
```

**Solutions:**

1. **Reset Database Password:**
   - Go to Supabase Dashboard → Settings → Database
   - Click "Reset database password"
   - Update your `.env` file with new password

2. **Check Connection String Format:**
   ```env
   # ❌ Wrong format
   DATABASE_URL="****************************************/db"
   
   # ✅ Correct format for Supabase
   DATABASE_URL="postgresql://postgres.[PROJECT-REF]:<EMAIL>:5432/postgres?sslmode=require"
   ```

3. **Verify Project Reference:**
   ```bash
   # Extract from your Supabase URL
   # https://jrhbvcjwxvyrxrmgjfbu.supabase.co
   # Project ref: jrhbvcjwxvyrxrmgjfbu
   ```

#### **Issue 4: Schema Mismatch & Type Errors**

**Error: Unknown field 'firstName'**
```
Error: Unknown field 'firstName' on model 'User'
```

**Solution:**
```bash
# Step 1: Pull latest schema from database
npx prisma db pull

# Step 2: Check what changed
git diff prisma/schema.prisma

# Step 3: Regenerate client
npx prisma generate

# Step 4: Restart development server
npm run dev
```

**Error: Property 'expertProfile' does not exist**
```
Property 'expertProfile' does not exist on type 'User'
```

**Solutions:**
```bash
# 1. Regenerate Prisma client
npx prisma generate

# 2. Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# 3. Restart TypeScript server in VS Code
# Cmd/Ctrl + Shift + P → "TypeScript: Restart TS Server"

# 4. Validate schema
npx prisma validate
```

#### **Issue 5: Development vs Production Issues**

**Works locally but fails in production:**

**Solutions:**

1. **Environment Variables:**
   ```bash
   # Check production environment variables
   echo $DATABASE_URL
   echo $DIRECT_URL
   
   # Ensure they're set in your deployment platform
   # Vercel: Project Settings → Environment Variables
   # Netlify: Site Settings → Environment Variables
   ```

2. **Different Connection Strings for Different Environments:**
   ```env
   # Development (.env.local)
   DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
   
   # Production (.env.production)
   DATABASE_URL="postgresql://postgres.xxx:<EMAIL>:5432/postgres"
   ```

3. **Build-time vs Runtime Issues:**
   ```bash
   # Ensure Prisma client is generated during build
   # Add to package.json scripts:
   "postinstall": "prisma generate"
   
   # Or in build command:
   "build": "prisma generate && next build"
   ```

### 🔧 **Advanced Troubleshooting Commands**

```bash
# Validate schema syntax
npx prisma validate

# Check database connection without pulling schema
npx prisma db execute --stdin <<< "SELECT 1"

# View all tables in database
npx prisma db execute --stdin <<< "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"

# Check Prisma version compatibility
npx prisma --version
npm list @prisma/client

# Reset everything and start fresh
npx prisma db pull --force
npx prisma generate
```

### 🚨 **Emergency Recovery Procedure**

If everything breaks and you need to start over:

```bash
# 1. Backup current schema
cp prisma/schema.prisma prisma/schema.backup

# 2. Remove Prisma files
rm -rf prisma/
rm -rf node_modules/@prisma/
rm -rf node_modules/.prisma/

# 3. Reinstall Prisma
npm uninstall prisma @prisma/client
npm install prisma @prisma/client

# 4. Initialize fresh
npx prisma init

# 5. Configure for Supabase (edit prisma/schema.prisma)
# Add the datasource configuration from above

# 6. Pull from database
npx prisma db pull --force

# 7. Generate client
npx prisma generate

# 8. Test connection
node test-prisma.js
```

### 🎯 **Web Portal Specific Considerations**

#### **Next.js Integration**
```typescript
// lib/prisma.ts
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma
```

#### **API Routes**
```typescript
// pages/api/users.ts or app/api/users/route.ts
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const users = await prisma.profiles.findMany({
      include: {
        expert_profiles: true
      }
    })
    return Response.json(users)
  } catch (error) {
    return Response.json({ error: 'Failed to fetch users' }, { status: 500 })
  }
}
```

#### **Server Components (App Router)**
```typescript
// app/dashboard/page.tsx
import { prisma } from '@/lib/prisma'

export default async function DashboardPage() {
  const users = await prisma.profiles.findMany({
    take: 10,
    orderBy: { created_at: 'desc' }
  })
  
  return (
    <div>
      {users.map(user => (
        <div key={user.id}>
          {user.first_name} {user.last_name}
        </div>
      ))}
    </div>
  )
}
```

### 📊 **Performance Optimization for Web Portals**

```typescript
// Use connection pooling for better performance
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
})

// Optimize queries
const users = await prisma.profiles.findMany({
  select: {  // Only select needed fields
    id: true,
    first_name: true,
    last_name: true,
    expert_profiles: {
      select: {
        rating: true,
        is_verified: true
      }
    }
  },
  take: 20,  // Limit results
  skip: page * 20,  // Pagination
})
```

### 🔍 **Debugging Tools**

#### **Enable Detailed Logging**
```typescript
const prisma = new PrismaClient({
  log: [
    { level: 'query', emit: 'event' },
    { level: 'error', emit: 'stdout' },
    { level: 'info', emit: 'stdout' },
    { level: 'warn', emit: 'stdout' },
  ],
})

prisma.$on('query', (e) => {
  console.log('Query: ' + e.query)
  console.log('Params: ' + e.params)
  console.log('Duration: ' + e.duration + 'ms')
})
```

#### **Use Prisma Studio**
```bash
npx prisma studio
```
Opens a web interface at `http://localhost:5555` to browse your database.

#### **Database Introspection**
```bash
# See what Prisma detects in your database
npx prisma db pull --print
```

This comprehensive troubleshooting guide should help you successfully link Prisma to any Supabase project, whether it's a mobile app or web portal!

---

## Project-Specific Examples

### 1. **Farmer Registration with Approval**
```typescript
// hooks/useRegistration.ts
import { usePrismaMutations } from './usePrisma'

export const useRegistration = () => {
  const { loading, error } = usePrismaMutations()
  
  const submitFarmRegistration = async (farmData, userId) => {
    try {
      // Create registration request for admin approval
      const request = await prisma.registrationRequest.create({
        data: {
          userId: userId,
          requestType: 'farm',
          requestData: farmData,
          status: 'pending'
        }
      })
      
      // Show success message to user
      return { success: true, requestId: request.id }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
  
  return { submitFarmRegistration, loading, error }
}
```

### 2. **Expert Discovery with Filtering**
```typescript
// components/ExpertSearch.tsx
import { useExperts } from '@/hooks/usePrisma'

export const ExpertSearch = () => {
  const [filters, setFilters] = useState({
    specializationId: null,
    isAvailable: true,
    minRating: 4.0
  })
  
  const { experts, loading } = useExperts({
    specializationId: filters.specializationId,
    isAvailable: filters.isAvailable,
    // Custom filter for rating
    where: {
      expertProfile: {
        rating: { gte: filters.minRating }
      }
    }
  })
  
  return (
    <View>
      {/* Filter UI */}
      <ExpertFilters onFiltersChange={setFilters} />
      
      {/* Results */}
      {loading ? (
        <LoadingSpinner />
      ) : (
        <FlatList
          data={experts}
          renderItem={({ item }) => (
            <ExpertCard 
              expert={item}
              onPress={() => navigateToExpert(item.id)}
            />
          )}
        />
      )}
    </View>
  )
}
```

### 3. **Offline Sync Implementation**
```typescript
// lib/offlineSync.ts
import { prisma } from './prisma'
import { NetInfo } from '@react-native-async-storage/async-storage'

export class OfflineSync {
  static async addToQueue(data: any, type: string, userId: string) {
    await prisma.offlineSyncQueue.create({
      data: {
        userId,
        dataType: type,
        dataPayload: data,
        syncStatus: 'pending'
      }
    })
  }
  
  static async processPendingSync() {
    const isConnected = await NetInfo.fetch().then(state => state.isConnected)
    
    if (!isConnected) return
    
    const pendingItems = await prisma.offlineSyncQueue.findMany({
      where: { syncStatus: 'pending' },
      orderBy: { createdAt: 'asc' }
    })
    
    for (const item of pendingItems) {
      try {
        await this.processItem(item)
        
        // Mark as synced
        await prisma.offlineSyncQueue.update({
          where: { id: item.id },
          data: { 
            syncStatus: 'synced',
            syncedAt: new Date()
          }
        })
      } catch (error) {
        // Mark as failed
        await prisma.offlineSyncQueue.update({
          where: { id: item.id },
          data: { syncStatus: 'failed' }
        })
      }
    }
  }
  
  private static async processItem(item: any) {
    switch (item.dataType) {
      case 'consultation_request':
        return await prisma.consultation.create({
          data: item.dataPayload
        })
      case 'farm_data':
        return await prisma.farm.update({
          where: { id: item.dataPayload.id },
          data: item.dataPayload
        })
      // Add more cases as needed
    }
  }
}
```

---

## Next Steps

### 1. **Test the Setup**
- Try the examples in this guide
- Create a simple component that fetches data
- Test the custom hooks we've created

### 2. **Implement Key Features**
- Registration approval workflow
- Offline synchronization
- Factory support for industrial users

### 3. **Optimize Performance**
- Add database indexes for frequently queried fields
- Implement caching for read-heavy operations
- Use pagination for large datasets

### 4. **Monitor and Debug**
- Enable query logging in development
- Use Prisma Studio to browse data
- Set up error tracking

---

## Useful Resources

- **Prisma Documentation**: https://www.prisma.io/docs
- **Prisma + Supabase Guide**: https://supabase.com/docs/guides/integrations/prisma
- **Prisma Schema Reference**: https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference
- **Prisma Client API**: https://www.prisma.io/docs/reference/api-reference/prisma-client-reference

---

**Congratulations!** 🎉 You now have a solid understanding of Prisma and how it's set up in your project. Start with the basic operations and gradually work your way up to the advanced features. Happy coding!