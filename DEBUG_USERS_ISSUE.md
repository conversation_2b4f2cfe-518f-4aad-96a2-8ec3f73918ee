# Debug Users Not Showing Issue

## 🔍 **Tables Being Queried**

### **For Experts Tab:**
- **Main Table**: `profiles`
- **Joined Table**: `expert_profiles`
- **Filter**: `WHERE role = 'expert'`

### **For Farmers Tab:**
- **Main Table**: `profiles`
- **Joined Table**: `client_profiles`
- **Filter**: `WHERE role IN ('farmer', 'client')`

## 🐛 **Debug Steps to Follow**

### **Step 1: Check Browser Console**
1. Open browser developer tools (F12)
2. Go to Console tab
3. Navigate to `/dashboard/users`
4. Look for these debug messages:
   - "Current user:" - Shows if you're logged in as admin
   - "User role:" - Should show "admin"
   - "Profiles table test:" - Shows if profiles table is accessible
   - "Expert profiles table test:" - Shows if expert_profiles table exists
   - "Fetched expert users:" - Shows the actual data returned

### **Step 2: Run These SQL Queries in Supabase Dashboard**

#### **Check if profiles table has data:**
```sql
SELECT COUNT(*) as total_profiles FROM profiles;
```

#### **Check if there are admin users:**
```sql
SELECT id, first_name, last_name, email, role 
FROM profiles 
WHERE role = 'admin' 
LIMIT 5;
```

#### **Check if there are expert users:**
```sql
SELECT COUNT(*) as expert_count FROM profiles WHERE role = 'expert';
```

#### **Check expert users with details:**
```sql
SELECT p.id, p.first_name, p.last_name, p.email, p.role, p.created_at
FROM profiles p
WHERE p.role = 'expert'
LIMIT 5;
```

#### **Check expert_profiles table:**
```sql
SELECT COUNT(*) as expert_profiles_count FROM expert_profiles;
```

#### **Check if expert_profiles has the new columns:**
```sql
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'expert_profiles' 
AND column_name IN ('expert_type', 'certifications', 'current_position', 'organization', 'languages_spoken', 'professional_memberships', 'awards_honors');
```

#### **Test the exact query used by the app:**
```sql
SELECT id, first_name, last_name, email, created_at, account_activated, is_available, role,
       expert_profiles(verification_status, bio, years_of_experience, education, expertise_area, expert_type, certifications, current_position, organization, languages_spoken, professional_memberships, awards_honors)
FROM profiles 
WHERE role = 'expert'
LIMIT 5;
```

### **Step 3: Check RLS Policies**

#### **Check if RLS is blocking access:**
```sql
-- Check RLS policies on profiles table
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'profiles';
```

#### **Test direct access to profiles:**
```sql
-- This should work if you're admin
SELECT * FROM profiles LIMIT 1;
```

### **Step 4: Check User Role Assignment**

#### **Verify your user has admin role:**
```sql
-- Check your current user's role
SELECT auth.uid() as current_user_id;

-- Check if your user has admin role
SELECT p.id, p.first_name, p.last_name, p.role
FROM profiles p
WHERE p.id = auth.uid();
```

## 🔧 **Common Issues & Solutions**

### **Issue 1: No Expert Users in Database**
**Symptoms**: Console shows "Fetched expert users: []" and "Total count: 0"
**Solution**: Create test expert users or check if existing users have correct role

### **Issue 2: Database Migration Not Applied**
**Symptoms**: Error about missing columns (expert_type, certifications, etc.)
**Solution**: Apply the migration:
```bash
cd /path/to/your/project
supabase db push
```

### **Issue 3: RLS Policies Blocking Access**
**Symptoms**: Console shows "Profiles table test: null" or permission errors
**Solution**: Check RLS policies or temporarily disable RLS for testing:
```sql
-- Temporarily disable RLS for testing (re-enable after!)
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
```

### **Issue 4: User Not Admin**
**Symptoms**: Console shows "Access denied: Not admin user"
**Solution**: Update your user's role:
```sql
UPDATE profiles SET role = 'admin' WHERE id = auth.uid();
```

### **Issue 5: expert_profiles Records Missing**
**Symptoms**: Users show but with no expert profile data
**Solution**: Create expert_profiles records for expert users:
```sql
-- Create expert_profiles for users with role = 'expert'
INSERT INTO expert_profiles (id, verification_status, expert_type)
SELECT id, 'pending', 'AGRICULTURE'
FROM profiles 
WHERE role = 'expert' 
AND id NOT IN (SELECT id FROM expert_profiles);
```

## 📋 **Quick Test Checklist**

- [ ] Browser console shows admin user logged in
- [ ] Profiles table has data (COUNT > 0)
- [ ] Expert users exist (role = 'expert')
- [ ] expert_profiles table exists and has new columns
- [ ] RLS policies allow admin access
- [ ] Migration applied successfully
- [ ] No JavaScript errors in console

## 🚀 **Next Steps**

1. **Check browser console** for debug messages
2. **Run SQL queries** in Supabase dashboard
3. **Report findings** - which step shows the issue
4. **Apply appropriate solution** based on the issue found

**Most likely causes:**
1. **No expert users in database** (most common)
2. **Database migration not applied**
3. **RLS policies blocking access**
4. **User not assigned admin role**
